{"ast": null, "code": "import { HighContrastModeDetector } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, NgModule } from '@angular/core';\n\n/**\n * Injection token that configures whether the Material sanity checks are enabled.\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nconst MATERIAL_SANITY_CHECKS = /*#__PURE__*/new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nlet MatCommonModule = /*#__PURE__*/(() => {\n  class MatCommonModule {\n    constructor() {\n      // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n      // in MatCommonModule.\n      inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n    }\n    static ɵfac = function MatCommonModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatCommonModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatCommonModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule, BidiModule]\n    });\n  }\n  return MatCommonModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatCommonModule as M, MATERIAL_SANITY_CHECKS as a };\n//# sourceMappingURL=common-module-cKSwHniA.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}