{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/material/card\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/common\";\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(authService) {\n      this.authService = authService;\n      this.currentUser = null;\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.currentUserValue;\n    }\n    static #_ = this.ɵfac = function ProfileComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      standalone: false,\n      decls: 47,\n      vars: 8,\n      consts: [[1, \"profile-container\"], [1, \"container\"], [\"mat-button\", \"\", \"color\", \"primary\"], [\"mat-button\", \"\", \"color\", \"accent\"], [\"mat-button\", \"\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Profile & Security Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"mat-card\")(5, \"mat-card-header\")(6, \"mat-card-title\");\n          i0.ɵɵtext(7, \"User Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\")(10, \"strong\");\n          i0.ɵɵtext(11, \"Name:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p\")(14, \"strong\");\n          i0.ɵɵtext(15, \"Email:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\")(18, \"strong\");\n          i0.ɵɵtext(19, \"Phone:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\")(22, \"strong\");\n          i0.ɵɵtext(23, \"Member since:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(24);\n          i0.ɵɵpipe(25, \"date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"mat-card-actions\")(27, \"button\", 2)(28, \"mat-icon\");\n          i0.ɵɵtext(29, \"edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \" Edit Profile \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"mat-card\")(32, \"mat-card-header\")(33, \"mat-card-title\");\n          i0.ɵɵtext(34, \"Security Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"mat-card-content\")(36, \"p\");\n          i0.ɵɵtext(37, \"Manage your account security settings including two-factor authentication.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"mat-card-actions\")(39, \"button\", 3)(40, \"mat-icon\");\n          i0.ɵɵtext(41, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" Setup 2FA \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"button\", 4)(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"lock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" Change Password \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate2(\" \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" \", ctx.currentUser == null ? null : ctx.currentUser.lastName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.phone) || \"Not provided\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(25, 5, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"mediumDate\"));\n        }\n      },\n      dependencies: [i2.MatCard, i2.MatCardActions, i2.MatCardContent, i2.MatCardHeader, i2.MatCardTitle, i3.MatButton, i4.MatIcon, i5.DatePipe],\n      styles: [\".profile-container[_ngcontent-%COMP%]{min-height:100vh;background:#f5f5f5;padding:2rem}.container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}h1[_ngcontent-%COMP%]{color:#333;margin-bottom:2rem}mat-card[_ngcontent-%COMP%]{margin-bottom:1.5rem}\"]\n    });\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}