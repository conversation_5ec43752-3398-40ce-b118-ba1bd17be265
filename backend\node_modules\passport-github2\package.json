{"name": "passport-github2", "version": "0.1.12", "description": "GitHub authentication strategy for Passport.", "keywords": ["passport", "github", "auth", "authn", "authentication", "identity"], "author": {"name": "<PERSON>", "email": "jared<PERSON><PERSON>@gmail.com", "url": "http://www.jaredhanson.net/"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "http://github.com/cfsghost/passport-github.git"}, "bugs": {"url": "http://github.com/cfsghost/passport-github/issues"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "main": "./lib", "dependencies": {"passport-oauth2": "1.x.x"}, "devDependencies": {"mocha": "1.x.x", "chai": "1.x.x"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "node_modules/.bin/mocha --reporter spec --require test/bootstrap/node test/*.test.js"}}