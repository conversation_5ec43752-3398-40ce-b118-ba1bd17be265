{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from '@guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/auth/login',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./modules/auth/auth.module').then(m => m.AuthModule)\n}, {\n  path: 'dashboard',\n  loadChildren: () => import('./modules/dashboard/dashboard.module').then(m => m.DashboardModule),\n  canActivate: [AuthGuard],\n  data: {\n    requireEmailVerification: true\n  }\n}, {\n  path: 'payment',\n  loadChildren: () => import('./modules/payment/payment.module').then(m => m.PaymentModule),\n  canActivate: [AuthGuard],\n  data: {\n    requireEmailVerification: true\n  }\n}, {\n  path: 'profile',\n  loadChildren: () => import('./modules/profile/profile.module').then(m => m.ProfileModule),\n  canActivate: [AuthGuard],\n  data: {\n    requireEmailVerification: true\n  }\n}, {\n  path: 'unauthorized',\n  loadChildren: () => import('./modules/shared/shared.module').then(m => m.SharedModule)\n}, {\n  path: '**',\n  redirectTo: '/auth/login'\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static #_ = this.ɵfac = function AppRoutingModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, {\n        enableTracing: false,\n        // Set to true for debugging\n        scrollPositionRestoration: 'top'\n      }), RouterModule]\n    });\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}