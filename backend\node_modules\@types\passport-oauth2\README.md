# Installation
> `npm install --save @types/passport-oauth2`

# Summary
This package contains type definitions for passport-oauth2 (https://github.com/jaredhanson/passport-oauth2#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/passport-oauth2.

### Additional Details
 * Last updated: Wed, 29 May 2024 17:07:22 GMT
 * Dependencies: [@types/express](https://npmjs.com/package/@types/express), [@types/oauth](https://npmjs.com/package/@types/oauth), [@types/passport](https://npmjs.com/package/@types/passport)

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/pasieronen), [<PERSON>](https://github.com/<PERSON>), [<PERSON>](https://github.com/Eduardo<PERSON>), [<PERSON>](https://github.com/ivan94), [<PERSON>](https://github.com/daphnesmit), and [<PERSON>](https://github.com/<PERSON>).
