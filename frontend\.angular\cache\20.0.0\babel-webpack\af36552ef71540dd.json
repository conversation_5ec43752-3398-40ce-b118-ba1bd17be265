{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction RegisterComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.passwordStrength, \"%\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getPasswordStrengthColor());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getPasswordStrengthColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.passwordStrengthText);\n  }\n}\nfunction RegisterComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldError(\"acceptTerms\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_spinner_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 33);\n  }\n}\nfunction RegisterComponent_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Create Account\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let RegisterComponent = /*#__PURE__*/(() => {\n  class RegisterComponent {\n    constructor(formBuilder, authService, router, snackBar) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.loading = false;\n      this.hidePassword = true;\n      this.hideConfirmPassword = true;\n      this.passwordStrength = 0;\n      this.passwordStrengthText = '';\n      this.registerForm = this.formBuilder.group({\n        firstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        lastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        email: ['', [Validators.required, Validators.email]],\n        phone: ['', [Validators.pattern(/^\\+?[1-9]\\d{1,14}$/)]],\n        password: ['', [Validators.required, Validators.minLength(8), this.passwordValidator]],\n        confirmPassword: ['', [Validators.required]],\n        acceptTerms: [false, [Validators.requiredTrue]]\n      }, {\n        validators: this.passwordMatchValidator\n      });\n    }\n    ngOnInit() {\n      if (this.authService.isAuthenticated) {\n        this.router.navigate(['/dashboard']);\n      }\n      this.registerForm.get('password')?.valueChanges.subscribe(password => {\n        this.updatePasswordStrength(password);\n      });\n    }\n    onSubmit() {\n      if (this.registerForm.invalid) {\n        this.markFormGroupTouched(this.registerForm);\n        return;\n      }\n      this.loading = true;\n      const userData = this.registerForm.value;\n      this.authService.register(userData).subscribe({\n        next: response => {\n          this.snackBar.open('Registration successful! Please check your email for verification.', 'Close', {\n            duration: 8000\n          });\n          this.router.navigate(['/auth/login'], {\n            queryParams: {\n              message: 'Please verify your email before logging in.'\n            }\n          });\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'Registration failed', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    getFieldError(fieldName) {\n      const field = this.registerForm.get(fieldName);\n      if (field?.errors && field.touched) {\n        if (field.errors['required']) return `${this.getFieldLabel(fieldName)} is required`;\n        if (field.errors['email']) return 'Please enter a valid email address';\n        if (field.errors['minlength']) return `${this.getFieldLabel(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        if (field.errors['maxlength']) return `${this.getFieldLabel(fieldName)} must not exceed ${field.errors['maxlength'].requiredLength} characters`;\n        if (field.errors['pattern']) {\n          if (fieldName === 'phone') return 'Please enter a valid phone number';\n          return 'Please enter a valid format';\n        }\n        if (field.errors['passwordStrength']) return field.errors['passwordStrength'];\n        if (field.errors['passwordMismatch']) return 'Passwords do not match';\n        if (field.errors['requiredTrue']) return 'You must accept the terms and conditions';\n      }\n      return '';\n    }\n    getFieldLabel(fieldName) {\n      const labels = {\n        firstName: 'First name',\n        lastName: 'Last name',\n        email: 'Email',\n        phone: 'Phone number',\n        password: 'Password',\n        confirmPassword: 'Confirm password'\n      };\n      return labels[fieldName] || fieldName;\n    }\n    passwordValidator(control) {\n      const password = control.value;\n      if (!password) return null;\n      const errors = [];\n      if (password.length < 8) {\n        errors.push('at least 8 characters');\n      }\n      if (!/[A-Z]/.test(password)) {\n        errors.push('one uppercase letter');\n      }\n      if (!/[a-z]/.test(password)) {\n        errors.push('one lowercase letter');\n      }\n      if (!/\\d/.test(password)) {\n        errors.push('one number');\n      }\n      if (!/[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)) {\n        errors.push('one special character');\n      }\n      if (errors.length > 0) {\n        return {\n          passwordStrength: `Password must contain ${errors.join(', ')}`\n        };\n      }\n      return null;\n    }\n    passwordMatchValidator(form) {\n      const password = form.get('password');\n      const confirmPassword = form.get('confirmPassword');\n      if (!password || !confirmPassword) return null;\n      if (password.value !== confirmPassword.value) {\n        confirmPassword.setErrors({\n          passwordMismatch: true\n        });\n        return {\n          passwordMismatch: true\n        };\n      }\n      if (confirmPassword.errors?.['passwordMismatch']) {\n        delete confirmPassword.errors['passwordMismatch'];\n        if (Object.keys(confirmPassword.errors).length === 0) {\n          confirmPassword.setErrors(null);\n        }\n      }\n      return null;\n    }\n    updatePasswordStrength(password) {\n      if (!password) {\n        this.passwordStrength = 0;\n        this.passwordStrengthText = '';\n        return;\n      }\n      let strength = 0;\n      const checks = [password.length >= 8, /[A-Z]/.test(password), /[a-z]/.test(password), /\\d/.test(password), /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(password)];\n      strength = checks.filter(check => check).length;\n      this.passwordStrength = strength / 5 * 100;\n      const strengthTexts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];\n      this.passwordStrengthText = strengthTexts[strength - 1] || 'Very Weak';\n    }\n    getPasswordStrengthColor() {\n      if (this.passwordStrength < 20) return 'warn';\n      if (this.passwordStrength < 40) return 'accent';\n      if (this.passwordStrength < 60) return 'primary';\n      if (this.passwordStrength < 80) return 'primary';\n      return 'primary';\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n      });\n    }\n    static #_ = this.ɵfac = function RegisterComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      standalone: false,\n      decls: 84,\n      vars: 16,\n      consts: [[1, \"auth-container\"], [1, \"auth-card\", \"fade-in\"], [1, \"auth-header\"], [1, \"security-badge\"], [1, \"auth-content\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"name-row\"], [\"appearance\", \"outline\", 1, \"form-field\", \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"autocomplete\", \"given-name\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"autocomplete\", \"family-name\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matInput\", \"\", \"type\", \"tel\", \"formControlName\", \"phone\", \"placeholder\", \"+1234567890\", \"autocomplete\", \"tel\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"class\", \"password-strength\", 4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [1, \"checkbox-field\"], [\"formControlName\", \"acceptTerms\"], [\"href\", \"/terms\", \"target\", \"_blank\"], [\"href\", \"/privacy\", \"target\", \"_blank\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/auth/login\", 1, \"text-primary\"], [1, \"password-strength\"], [1, \"strength-bar\"], [1, \"strength-fill\", 3, \"ngClass\"], [1, \"strength-text\", 3, \"ngClass\"], [1, \"error-message\"], [\"diameter\", \"20\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4, \"Create Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Join our secure platform today\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n          i0.ɵɵtext(9, \"verified_user\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" Secure Registration \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 4)(12, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_12_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(13, \"div\", 6)(14, \"mat-form-field\", 7)(15, \"mat-label\");\n          i0.ɵɵtext(16, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 8);\n          i0.ɵɵelementStart(18, \"mat-icon\", 9);\n          i0.ɵɵtext(19, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"mat-error\");\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"mat-form-field\", 7)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"input\", 10);\n          i0.ɵɵelementStart(26, \"mat-icon\", 9);\n          i0.ɵɵtext(27, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-error\");\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 11)(31, \"mat-label\");\n          i0.ɵɵtext(32, \"Email Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"input\", 12);\n          i0.ɵɵelementStart(34, \"mat-icon\", 9);\n          i0.ɵɵtext(35, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"mat-error\");\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"mat-form-field\", 11)(39, \"mat-label\");\n          i0.ɵɵtext(40, \"Phone Number (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(41, \"input\", 13);\n          i0.ɵɵelementStart(42, \"mat-icon\", 9);\n          i0.ɵɵtext(43, \"phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"mat-error\");\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-hint\");\n          i0.ɵɵtext(47, \"For SMS verification and 2FA\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"mat-form-field\", 11)(49, \"mat-label\");\n          i0.ɵɵtext(50, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(51, \"input\", 14);\n          i0.ɵɵelementStart(52, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_52_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(53, \"mat-icon\");\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"mat-error\");\n          i0.ɵɵtext(56);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(57, RegisterComponent_div_57_Template, 5, 5, \"div\", 16);\n          i0.ɵɵelementStart(58, \"mat-form-field\", 11)(59, \"mat-label\");\n          i0.ɵɵtext(60, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(61, \"input\", 17);\n          i0.ɵɵelementStart(62, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_62_listener() {\n            return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n          });\n          i0.ɵɵelementStart(63, \"mat-icon\");\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"mat-error\");\n          i0.ɵɵtext(66);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 18)(68, \"mat-checkbox\", 19);\n          i0.ɵɵtext(69, \" I agree to the \");\n          i0.ɵɵelementStart(70, \"a\", 20);\n          i0.ɵɵtext(71, \"Terms of Service\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(72, \" and \");\n          i0.ɵɵelementStart(73, \"a\", 21);\n          i0.ɵɵtext(74, \"Privacy Policy\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(75, RegisterComponent_div_75_Template, 2, 1, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"button\", 23);\n          i0.ɵɵtemplate(77, RegisterComponent_mat_spinner_77_Template, 1, 0, \"mat-spinner\", 24)(78, RegisterComponent_span_78_Template, 2, 0, \"span\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 26)(80, \"span\");\n          i0.ɵɵtext(81, \"Already have an account? \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"a\", 27);\n          i0.ɵɵtext(83, \"Sign In\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_8_0;\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(9);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"firstName\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"lastName\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"email\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"phone\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"password\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_8_0.value);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"confirmPassword\"));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.getFieldError(\"acceptTerms\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i6.MatFormField, i6.MatLabel, i6.MatHint, i6.MatError, i6.MatSuffix, i7.MatInput, i8.MatButton, i8.MatIconButton, i9.MatIcon, i10.MatCheckbox, i11.MatProgressSpinner],\n      styles: [\".name-row[_ngcontent-%COMP%]{display:flex;gap:1rem}.name-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%]{flex:1}.password-strength[_ngcontent-%COMP%]{margin-bottom:1rem}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]{width:100%;height:4px;background:#e0e0e0;border-radius:2px;overflow:hidden;margin-bottom:.5rem}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill[_ngcontent-%COMP%]{height:100%;transition:width .3s ease,background-color .3s ease}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.warn[_ngcontent-%COMP%]{background:#f44336}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.accent[_ngcontent-%COMP%]{background:#ff9800}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.primary[_ngcontent-%COMP%]{background:#4caf50}.password-strength[_ngcontent-%COMP%]   .strength-text[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500}.password-strength[_ngcontent-%COMP%]   .strength-text.warn[_ngcontent-%COMP%]{color:#f44336}.password-strength[_ngcontent-%COMP%]   .strength-text.accent[_ngcontent-%COMP%]{color:#ff9800}.password-strength[_ngcontent-%COMP%]   .strength-text.primary[_ngcontent-%COMP%]{color:#4caf50}.checkbox-field[_ngcontent-%COMP%]{margin:1.5rem 0}.checkbox-field[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{font-size:.875rem}.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 768px){.name-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}}\"]\n    });\n  }\n  return RegisterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}