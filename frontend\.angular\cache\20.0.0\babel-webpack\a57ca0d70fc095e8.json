{"ast": null, "code": "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform, getSupportedInputTypes } from '@angular/cdk/platform';\nimport { AutofillMonitor, TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, isSignal, effect, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { M as MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-D1GvPuqO.mjs';\nimport { h as MAT_FORM_FIELD, k as MatFormFieldControl } from './form-field-C9DZXojn.mjs';\nexport { b as <PERSON><PERSON><PERSON><PERSON>, j as <PERSON><PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as Mat<PERSON>refix, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-C9DZXojn.mjs';\nimport { E as ErrorStateMatcher } from './error-options-DCNQlTOA.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { M as MatFormFieldModule } from './module-DzZHEh7B.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport './animation-DfMFjxHu.mjs';\nimport '@angular/cdk/layout';\nimport '@angular/cdk/observers';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = ['button', 'checkbox', 'file', 'hidden', 'image', 'radio', 'range', 'reset', 'submit'];\n/** Injection token that can be used to provide the default options for the input. */\nconst MAT_INPUT_CONFIG = /*#__PURE__*/new InjectionToken('MAT_INPUT_CONFIG');\nlet MatInput = /*#__PURE__*/(() => {\n  class MatInput {\n    _elementRef = inject(ElementRef);\n    _platform = inject(Platform);\n    ngControl = inject(NgControl, {\n      optional: true,\n      self: true\n    });\n    _autofillMonitor = inject(AutofillMonitor);\n    _ngZone = inject(NgZone);\n    _formField = inject(MAT_FORM_FIELD, {\n      optional: true\n    });\n    _renderer = inject(Renderer2);\n    _uid = inject(_IdGenerator).getId('mat-input-');\n    _previousNativeValue;\n    _inputValueAccessor;\n    _signalBasedValueAccessor;\n    _previousPlaceholder;\n    _errorStateTracker;\n    _config = inject(MAT_INPUT_CONFIG, {\n      optional: true\n    });\n    _cleanupIosKeyup;\n    _cleanupWebkitWheel;\n    /** Whether the component is being rendered on the server. */\n    _isServer;\n    /** Whether the component is a native html select. */\n    _isNativeSelect;\n    /** Whether the component is a textarea. */\n    _isTextarea;\n    /** Whether the input is inside of a form field. */\n    _isInFormField;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    focused = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    stateChanges = new Subject();\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    controlType = 'mat-input';\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    autofilled = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = coerceBooleanProperty(value);\n      // Browsers may not fire the blur event if the input is disabled too quickly.\n      // Reset from here to ensure that the element doesn't become stuck.\n      if (this.focused) {\n        this.focused = false;\n        this.stateChanges.next();\n      }\n    }\n    _disabled = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n      return this._id;\n    }\n    set id(value) {\n      this._id = value || this._uid;\n    }\n    _id;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    placeholder;\n    /**\n     * Name of the input.\n     * @docs-private\n     */\n    name;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n      return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n      this._required = coerceBooleanProperty(value);\n    }\n    _required;\n    /** Input type of the element. */\n    get type() {\n      return this._type;\n    }\n    set type(value) {\n      const prevType = this._type;\n      this._type = value || 'text';\n      this._validateType();\n      // When using Angular inputs, developers are no longer able to set the properties on the native\n      // input element. To ensure that bindings for `type` work, we need to sync the setter\n      // with the native property. Textarea elements don't support the type property or attribute.\n      if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n        this._elementRef.nativeElement.type = this._type;\n      }\n      if (this._type !== prevType) {\n        this._ensureWheelDefaultBehavior();\n      }\n    }\n    _type = 'text';\n    /** An object used to control when error messages are shown. */\n    get errorStateMatcher() {\n      return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n      this._errorStateTracker.matcher = value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    userAriaDescribedBy;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n      return this._signalBasedValueAccessor ? this._signalBasedValueAccessor.value() : this._inputValueAccessor.value;\n    }\n    set value(value) {\n      if (value !== this.value) {\n        if (this._signalBasedValueAccessor) {\n          this._signalBasedValueAccessor.value.set(value);\n        } else {\n          this._inputValueAccessor.value = value;\n        }\n        this.stateChanges.next();\n      }\n    }\n    /** Whether the element is readonly. */\n    get readonly() {\n      return this._readonly;\n    }\n    set readonly(value) {\n      this._readonly = coerceBooleanProperty(value);\n    }\n    _readonly = false;\n    /** Whether the input should remain interactive when it is disabled. */\n    disabledInteractive;\n    /** Whether the input is in an error state. */\n    get errorState() {\n      return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n      this._errorStateTracker.errorState = value;\n    }\n    _neverEmptyInputTypes = ['date', 'datetime', 'datetime-local', 'month', 'time', 'week'].filter(t => getSupportedInputTypes().has(t));\n    constructor() {\n      const parentForm = inject(NgForm, {\n        optional: true\n      });\n      const parentFormGroup = inject(FormGroupDirective, {\n        optional: true\n      });\n      const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n      const accessor = inject(MAT_INPUT_VALUE_ACCESSOR, {\n        optional: true,\n        self: true\n      });\n      const element = this._elementRef.nativeElement;\n      const nodeName = element.nodeName.toLowerCase();\n      if (accessor) {\n        if (isSignal(accessor.value)) {\n          this._signalBasedValueAccessor = accessor;\n        } else {\n          this._inputValueAccessor = accessor;\n        }\n      } else {\n        // If no input value accessor was explicitly specified, use the element as the input value\n        // accessor.\n        this._inputValueAccessor = element;\n      }\n      this._previousNativeValue = this.value;\n      // Force setter to be called in case id was not specified.\n      this.id = this.id;\n      // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n      // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n      // exists on iOS, we only bother to install the listener on iOS.\n      if (this._platform.IOS) {\n        this._ngZone.runOutsideAngular(() => {\n          this._cleanupIosKeyup = this._renderer.listen(element, 'keyup', this._iOSKeyupListener);\n        });\n      }\n      this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n      this._isServer = !this._platform.isBrowser;\n      this._isNativeSelect = nodeName === 'select';\n      this._isTextarea = nodeName === 'textarea';\n      this._isInFormField = !!this._formField;\n      this.disabledInteractive = this._config?.disabledInteractive || false;\n      if (this._isNativeSelect) {\n        this.controlType = element.multiple ? 'mat-native-select-multiple' : 'mat-native-select';\n      }\n      if (this._signalBasedValueAccessor) {\n        effect(() => {\n          // Read the value so the effect can register the dependency.\n          this._signalBasedValueAccessor.value();\n          this.stateChanges.next();\n        });\n      }\n    }\n    ngAfterViewInit() {\n      if (this._platform.isBrowser) {\n        this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n          this.autofilled = event.isAutofilled;\n          this.stateChanges.next();\n        });\n      }\n    }\n    ngOnChanges() {\n      this.stateChanges.next();\n    }\n    ngOnDestroy() {\n      this.stateChanges.complete();\n      if (this._platform.isBrowser) {\n        this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n      }\n      this._cleanupIosKeyup?.();\n      this._cleanupWebkitWheel?.();\n    }\n    ngDoCheck() {\n      if (this.ngControl) {\n        // We need to re-evaluate this on every change detection cycle, because there are some\n        // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n        // that whatever logic is in here has to be super lean or we risk destroying the performance.\n        this.updateErrorState();\n        // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n        // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n        // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n        // disabled.\n        if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n          this.disabled = this.ngControl.disabled;\n          this.stateChanges.next();\n        }\n      }\n      // We need to dirty-check the native element's value, because there are some cases where\n      // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n      // updating the value using `emitEvent: false`).\n      this._dirtyCheckNativeValue();\n      // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n      // present or not depends on a query which is prone to \"changed after checked\" errors.\n      this._dirtyCheckPlaceholder();\n    }\n    /** Focuses the input. */\n    focus(options) {\n      this._elementRef.nativeElement.focus(options);\n    }\n    /** Refreshes the error state of the input. */\n    updateErrorState() {\n      this._errorStateTracker.updateErrorState();\n    }\n    /** Callback for the cases where the focused state of the input changes. */\n    _focusChanged(isFocused) {\n      if (isFocused === this.focused) {\n        return;\n      }\n      if (!this._isNativeSelect && isFocused && this.disabled && this.disabledInteractive) {\n        const element = this._elementRef.nativeElement;\n        // Focusing an input that has text will cause all the text to be selected. Clear it since\n        // the user won't be able to change it. This is based on the internal implementation.\n        if (element.type === 'number') {\n          // setSelectionRange doesn't work on number inputs so it needs to be set briefly to text.\n          element.type = 'text';\n          element.setSelectionRange(0, 0);\n          element.type = 'number';\n        } else {\n          element.setSelectionRange(0, 0);\n        }\n      }\n      this.focused = isFocused;\n      this.stateChanges.next();\n    }\n    _onInput() {\n      // This is a noop function and is used to let Angular know whenever the value changes.\n      // Angular will run a new change detection each time the `input` event has been dispatched.\n      // It's necessary that Angular recognizes the value change, because when floatingLabel\n      // is set to false and Angular forms aren't used, the placeholder won't recognize the\n      // value changes and will not disappear.\n      // Listening to the input event wouldn't be necessary when the input is using the\n      // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n    }\n    /** Does some manual dirty checking on the native input `value` property. */\n    _dirtyCheckNativeValue() {\n      const newValue = this._elementRef.nativeElement.value;\n      if (this._previousNativeValue !== newValue) {\n        this._previousNativeValue = newValue;\n        this.stateChanges.next();\n      }\n    }\n    /** Does some manual dirty checking on the native input `placeholder` attribute. */\n    _dirtyCheckPlaceholder() {\n      const placeholder = this._getPlaceholder();\n      if (placeholder !== this._previousPlaceholder) {\n        const element = this._elementRef.nativeElement;\n        this._previousPlaceholder = placeholder;\n        placeholder ? element.setAttribute('placeholder', placeholder) : element.removeAttribute('placeholder');\n      }\n    }\n    /** Gets the current placeholder of the form field. */\n    _getPlaceholder() {\n      return this.placeholder || null;\n    }\n    /** Make sure the input is a supported type. */\n    _validateType() {\n      if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw getMatInputUnsupportedTypeError(this._type);\n      }\n    }\n    /** Checks whether the input type is one of the types that are never empty. */\n    _isNeverEmpty() {\n      return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n    }\n    /** Checks whether the input is invalid based on the native validation. */\n    _isBadInput() {\n      // The `validity` property won't be present on platform-server.\n      let validity = this._elementRef.nativeElement.validity;\n      return validity && validity.badInput;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n      return !this._isNeverEmpty() && !this._elementRef.nativeElement.value && !this._isBadInput() && !this.autofilled;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n      if (this._isNativeSelect) {\n        // For a single-selection `<select>`, the label should float when the selected option has\n        // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n        // overlapping the label with the options.\n        const selectElement = this._elementRef.nativeElement;\n        const firstOption = selectElement.options[0];\n        // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n        // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n        return this.focused || selectElement.multiple || !this.empty || !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label);\n      } else {\n        return this.focused && !this.disabled || !this.empty;\n      }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get describedByIds() {\n      const element = this._elementRef.nativeElement;\n      const existingDescribedBy = element.getAttribute('aria-describedby');\n      return existingDescribedBy?.split(' ') || [];\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n      const element = this._elementRef.nativeElement;\n      if (ids.length) {\n        element.setAttribute('aria-describedby', ids.join(' '));\n      } else {\n        element.removeAttribute('aria-describedby');\n      }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n      // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n      // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n      // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n      if (!this.focused) {\n        this.focus();\n      }\n    }\n    /** Whether the form control is a native select that is displayed inline. */\n    _isInlineSelect() {\n      const element = this._elementRef.nativeElement;\n      return this._isNativeSelect && (element.multiple || element.size > 1);\n    }\n    _iOSKeyupListener = event => {\n      const el = event.target;\n      // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n      // indicate different things. If the value is 0, it means that the caret is at the start\n      // of the input, whereas a value of `null` means that the input doesn't support\n      // manipulating the selection range. Inputs that don't support setting the selection range\n      // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n      // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n      if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n        // Note: Just setting `0, 0` doesn't fix the issue. Setting\n        // `1, 1` fixes it for the first time that you type text and\n        // then hold delete. Toggling to `1, 1` and then back to\n        // `0, 0` seems to completely fix it.\n        el.setSelectionRange(1, 1);\n        el.setSelectionRange(0, 0);\n      }\n    };\n    _webkitBlinkWheelListener = () => {\n      // This is a noop function and is used to enable mouse wheel input\n      // on number inputs\n      // on blink and webkit browsers.\n    };\n    /**\n     * In blink and webkit browsers a focused number input does not increment or decrement its value\n     * on mouse wheel interaction unless a wheel event listener is attached to it or one of its\n     * ancestors or a passive wheel listener is attached somewhere in the DOM. For example: Hitting\n     * a tooltip once enables the mouse wheel input for all number inputs as long as it exists. In\n     * order to get reliable and intuitive behavior we apply a wheel event on our own thus making\n     * sure increment and decrement by mouse wheel works every time.\n     * @docs-private\n     */\n    _ensureWheelDefaultBehavior() {\n      this._cleanupWebkitWheel?.();\n      if (this._type === 'number' && (this._platform.BLINK || this._platform.WEBKIT)) {\n        this._cleanupWebkitWheel = this._renderer.listen(this._elementRef.nativeElement, 'wheel', this._webkitBlinkWheelListener);\n      }\n    }\n    /** Gets the value to set on the `readonly` attribute. */\n    _getReadonlyAttribute() {\n      if (this._isNativeSelect) {\n        return null;\n      }\n      if (this.readonly || this.disabled && this.disabledInteractive) {\n        return 'true';\n      }\n      return null;\n    }\n    static ɵfac = function MatInput_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatInput)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatInput,\n      selectors: [[\"input\", \"matInput\", \"\"], [\"textarea\", \"matInput\", \"\"], [\"select\", \"matNativeControl\", \"\"], [\"input\", \"matNativeControl\", \"\"], [\"textarea\", \"matNativeControl\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-input-element\"],\n      hostVars: 21,\n      hostBindings: function MatInput_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatInput_focus_HostBindingHandler() {\n            return ctx._focusChanged(true);\n          })(\"blur\", function MatInput_blur_HostBindingHandler() {\n            return ctx._focusChanged(false);\n          })(\"input\", function MatInput_input_HostBindingHandler() {\n            return ctx._onInput();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"required\", ctx.required);\n          i0.ɵɵattribute(\"name\", ctx.name || null)(\"readonly\", ctx._getReadonlyAttribute())(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null)(\"aria-invalid\", ctx.empty && ctx.required ? null : ctx.errorState)(\"aria-required\", ctx.required)(\"id\", ctx.id);\n          i0.ɵɵclassProp(\"mat-input-server\", ctx._isServer)(\"mat-mdc-form-field-textarea-control\", ctx._isInFormField && ctx._isTextarea)(\"mat-mdc-form-field-input-control\", ctx._isInFormField)(\"mat-mdc-input-disabled-interactive\", ctx.disabledInteractive)(\"mdc-text-field__input\", ctx._isInFormField)(\"mat-mdc-native-select-inline\", ctx._isInlineSelect());\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        id: \"id\",\n        placeholder: \"placeholder\",\n        name: \"name\",\n        required: \"required\",\n        type: \"type\",\n        errorStateMatcher: \"errorStateMatcher\",\n        userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n        value: \"value\",\n        readonly: \"readonly\",\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n      },\n      exportAs: [\"matInput\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatFormFieldControl,\n        useExisting: MatInput\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return MatInput;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatInputModule = /*#__PURE__*/(() => {\n  class MatInputModule {\n    static ɵfac = function MatInputModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatInputModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatInputModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule]\n    });\n  }\n  return MatInputModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MAT_INPUT_CONFIG, MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n//# sourceMappingURL=input.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}