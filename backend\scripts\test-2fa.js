const axios = require('axios');
const speakeasy = require('speakeasy');

const BASE_URL = 'http://localhost:3002';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: '2FA',
  lastName: 'Test',
  phone: '+1234567890'
};

let authToken = '';
let twoFactorSecret = '';

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
}

async function setupTestUser() {
  console.log('\n🔧 Setting up 2FA test user...');
  
  // Try to create user
  const signupResult = await apiCall('POST', '/auth/signup', testUser);
  
  if (signupResult.success) {
    console.log('✅ 2FA test user created');
  } else if (signupResult.status === 409) {
    console.log('ℹ️  2FA test user already exists');
  } else {
    console.log('❌ Failed to create 2FA test user:', signupResult.error);
    return false;
  }
  
  // Login to get token
  const loginResult = await apiCall('POST', '/auth/login', {
    email: testUser.email,
    password: testUser.password
  });
  
  if (loginResult.success) {
    authToken = loginResult.data.token;
    console.log('✅ 2FA test user logged in');
    return true;
  } else {
    console.log('❌ Failed to login 2FA test user:', loginResult.error);
    return false;
  }
}

async function test2FASetup() {
  console.log('\n🧪 Testing 2FA Setup (TOTP)...');
  
  const result = await apiCall('POST', '/2fa/setup', null, authToken);
  
  if (result.success) {
    twoFactorSecret = result.data.secret;
    console.log('✅ 2FA setup successful');
    console.log('🔐 Secret:', twoFactorSecret);
    console.log('📱 QR Code generated (base64 data available)');
    console.log('ℹ️  You can scan this QR code with:');
    console.log('   - Google Authenticator');
    console.log('   - Microsoft Authenticator');
    console.log('   - Authy');
    console.log('   - Any TOTP-compatible app');
    return true;
  } else {
    console.log('❌ 2FA setup failed:', result.error);
    return false;
  }
}

async function test2FAVerification() {
  console.log('\n🧪 Testing 2FA Verification (TOTP)...');
  
  if (!twoFactorSecret) {
    console.log('❌ No 2FA secret available for testing');
    return false;
  }
  
  // Generate TOTP token using the secret
  const token = speakeasy.totp({
    secret: twoFactorSecret,
    encoding: 'base32'
  });
  
  console.log('🔢 Generated TOTP token:', token);
  
  const result = await apiCall('POST', '/2fa/verify', { token }, authToken);
  
  if (result.success) {
    console.log('✅ 2FA verification successful - 2FA is now enabled');
    return true;
  } else {
    console.log('❌ 2FA verification failed:', result.error);
    return false;
  }
}

async function test2FAStatus() {
  console.log('\n🧪 Testing 2FA Status...');
  
  const result = await apiCall('GET', '/2fa/status', null, authToken);
  
  if (result.success) {
    console.log('✅ 2FA status retrieved:', result.data);
    console.log(`🔒 2FA Enabled: ${result.data.enabled ? 'Yes' : 'No'}`);
    return true;
  } else {
    console.log('❌ 2FA status check failed:', result.error);
    return false;
  }
}

async function test2FAEmailOTP() {
  console.log('\n🧪 Testing 2FA Email OTP...');
  
  const result = await apiCall('POST', '/2fa/send-email', null, authToken);
  
  if (result.success) {
    console.log('✅ 2FA email OTP sent');
    console.log('📧 Check console logs for OTP email content');
    console.log('ℹ️  In a real scenario, user would receive OTP via email');
    return true;
  } else {
    console.log('❌ 2FA email OTP failed:', result.error);
    return false;
  }
}

async function test2FASMS() {
  console.log('\n🧪 Testing 2FA SMS OTP...');
  
  const result = await apiCall('POST', '/2fa/send-sms', null, authToken);
  
  if (result.success) {
    console.log('✅ 2FA SMS OTP sent');
    console.log('📱 Check console logs for SMS content');
    console.log('ℹ️  In a real scenario, user would receive OTP via SMS');
    return true;
  } else {
    console.log('❌ 2FA SMS OTP failed:', result.error);
    return false;
  }
}

async function testLoginWith2FA() {
  console.log('\n🧪 Testing Login with 2FA...');
  
  if (!twoFactorSecret) {
    console.log('❌ No 2FA secret available for testing');
    return false;
  }
  
  // Generate TOTP token
  const twoFactorToken = speakeasy.totp({
    secret: twoFactorSecret,
    encoding: 'base32'
  });
  
  console.log('🔢 Using TOTP token:', twoFactorToken);
  
  const result = await apiCall('POST', '/auth/login', {
    email: testUser.email,
    password: testUser.password,
    twoFactorToken: twoFactorToken
  });
  
  if (result.success) {
    console.log('✅ Login with 2FA successful');
    return true;
  } else {
    console.log('❌ Login with 2FA failed:', result.error);
    return false;
  }
}

async function testChangePasswordWith2FA() {
  console.log('\n🧪 Testing Change Password with 2FA...');
  
  if (!twoFactorSecret) {
    console.log('❌ No 2FA secret available for testing');
    return false;
  }
  
  // Generate TOTP token
  const twoFactorToken = speakeasy.totp({
    secret: twoFactorSecret,
    encoding: 'base32'
  });
  
  console.log('🔢 Using TOTP token for password change:', twoFactorToken);
  
  const result = await apiCall('POST', '/auth/change-password', {
    currentPassword: testUser.password,
    newPassword: 'NewTestPassword789!',
    twoFactorToken: twoFactorToken
  }, authToken);
  
  if (result.success) {
    console.log('✅ Password change with 2FA successful');
    testUser.password = 'NewTestPassword789!'; // Update for future tests
    return true;
  } else {
    console.log('❌ Password change with 2FA failed:', result.error);
    return false;
  }
}

async function test2FACompatibility() {
  console.log('\n🧪 Testing 2FA App Compatibility...');
  
  if (!twoFactorSecret) {
    console.log('❌ No 2FA secret available for testing');
    return false;
  }
  
  console.log('📱 Testing TOTP compatibility with popular authenticator apps:');
  
  // Test multiple time windows to ensure compatibility
  const timeWindows = [-1, 0, 1]; // Previous, current, next 30-second window
  let validTokens = 0;
  
  for (const window of timeWindows) {
    const token = speakeasy.totp({
      secret: twoFactorSecret,
      encoding: 'base32',
      window: window
    });
    
    // Test if this token would be accepted
    const result = await apiCall('POST', '/2fa/verify-email', { code: token }, authToken);
    
    if (result.success && result.data.valid) {
      validTokens++;
    }
  }
  
  console.log(`✅ TOTP tokens generated successfully`);
  console.log(`🔐 Secret format: Base32 (compatible with all major apps)`);
  console.log(`⏰ Time window: 30 seconds (standard)`);
  console.log(`📊 Algorithm: SHA1 (widely supported)`);
  
  console.log('\n📱 Compatible Authenticator Apps:');
  console.log('   ✅ Google Authenticator');
  console.log('   ✅ Microsoft Authenticator');
  console.log('   ✅ Authy (by Twilio)');
  console.log('   ✅ 1Password');
  console.log('   ✅ LastPass Authenticator');
  console.log('   ✅ Any RFC 6238 TOTP app');
  
  return true;
}

// Main test runner
async function run2FATests() {
  console.log('🔐 Starting 2FA System Tests...');
  console.log('=' .repeat(50));
  
  const tests = [
    { name: 'Setup Test User', fn: setupTestUser },
    { name: '2FA Setup (TOTP)', fn: test2FASetup },
    { name: '2FA Verification', fn: test2FAVerification },
    { name: '2FA Status Check', fn: test2FAStatus },
    { name: '2FA Email OTP', fn: test2FAEmailOTP },
    { name: '2FA SMS OTP', fn: test2FASMS },
    { name: 'Login with 2FA', fn: testLoginWith2FA },
    { name: 'Change Password with 2FA', fn: testChangePasswordWith2FA },
    { name: '2FA App Compatibility', fn: test2FACompatibility }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message);
      failed++;
    }
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 2FA Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('🎉 All 2FA tests passed!');
  } else {
    console.log('⚠️  Some 2FA tests failed. Check the logs above for details.');
  }
  
  console.log('\n📝 2FA Setup Instructions:');
  console.log('1. Use /2fa/setup to get QR code and secret');
  console.log('2. Scan QR code with any TOTP authenticator app');
  console.log('3. Use /2fa/verify with generated token to enable 2FA');
  console.log('4. Use 2FA tokens for login and sensitive operations');
}

// Run tests if this script is executed directly
if (require.main === module) {
  run2FATests().catch(console.error);
}

module.exports = { run2FATests, test2FACompatibility };
