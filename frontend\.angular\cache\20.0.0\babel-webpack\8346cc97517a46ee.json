{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, DOCUMENT, NgZone, Injector, RendererFactory2, Injectable } from '@angular/core';\nimport { Platform, _getEventTarget } from '@angular/cdk/platform';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS, R as RippleRenderer, d as defaultRippleAnimationConfig } from './ripple-BYgV4oZC.mjs';\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {\n  capture: true\n};\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nlet MatRippleLoader = /*#__PURE__*/(() => {\n  class MatRippleLoader {\n    _document = inject(DOCUMENT);\n    _animationsDisabled = _animationsDisabled();\n    _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n      optional: true\n    });\n    _platform = inject(Platform);\n    _ngZone = inject(NgZone);\n    _injector = inject(Injector);\n    _eventCleanups;\n    _hosts = new Map();\n    constructor() {\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n      this._eventCleanups = this._ngZone.runOutsideAngular(() => rippleInteractionEvents.map(name => renderer.listen(this._document, name, this._onInteraction, eventListenerOptions)));\n    }\n    ngOnDestroy() {\n      const hosts = this._hosts.keys();\n      for (const host of hosts) {\n        this.destroyRipple(host);\n      }\n      this._eventCleanups.forEach(cleanup => cleanup());\n    }\n    /**\n     * Configures the ripple that will be rendered by the ripple loader.\n     *\n     * Stores the given information about how the ripple should be configured on the host\n     * element so that it can later be retrived & used when the ripple is actually created.\n     */\n    configureRipple(host, config) {\n      // Indicates that the ripple has not yet been rendered for this component.\n      host.setAttribute(matRippleUninitialized, this._globalRippleOptions?.namespace ?? '');\n      // Store the additional class name(s) that should be added to the ripple element.\n      if (config.className || !host.hasAttribute(matRippleClassName)) {\n        host.setAttribute(matRippleClassName, config.className || '');\n      }\n      // Store whether the ripple should be centered.\n      if (config.centered) {\n        host.setAttribute(matRippleCentered, '');\n      }\n      if (config.disabled) {\n        host.setAttribute(matRippleDisabled, '');\n      }\n    }\n    /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n    setDisabled(host, disabled) {\n      const ripple = this._hosts.get(host);\n      // If the ripple has already been instantiated, just disable it.\n      if (ripple) {\n        ripple.target.rippleDisabled = disabled;\n        if (!disabled && !ripple.hasSetUpEvents) {\n          ripple.hasSetUpEvents = true;\n          ripple.renderer.setupTriggerEvents(host);\n        }\n      } else if (disabled) {\n        // Otherwise, set an attribute so we know what the\n        // disabled state should be when the ripple is initialized.\n        host.setAttribute(matRippleDisabled, '');\n      } else {\n        host.removeAttribute(matRippleDisabled);\n      }\n    }\n    /**\n     * Handles creating and attaching component internals\n     * when a component is initially interacted with.\n     */\n    _onInteraction = event => {\n      const eventTarget = _getEventTarget(event);\n      if (eventTarget instanceof HTMLElement) {\n        // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n        const element = eventTarget.closest(`[${matRippleUninitialized}=\"${this._globalRippleOptions?.namespace ?? ''}\"]`);\n        if (element) {\n          this._createRipple(element);\n        }\n      }\n    };\n    /** Creates a MatRipple and appends it to the given element. */\n    _createRipple(host) {\n      if (!this._document || this._hosts.has(host)) {\n        return;\n      }\n      // Create the ripple element.\n      host.querySelector('.mat-ripple')?.remove();\n      const rippleEl = this._document.createElement('span');\n      rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n      host.append(rippleEl);\n      const globalOptions = this._globalRippleOptions;\n      const enterDuration = this._animationsDisabled ? 0 : globalOptions?.animation?.enterDuration ?? defaultRippleAnimationConfig.enterDuration;\n      const exitDuration = this._animationsDisabled ? 0 : globalOptions?.animation?.exitDuration ?? defaultRippleAnimationConfig.exitDuration;\n      const target = {\n        rippleDisabled: this._animationsDisabled || globalOptions?.disabled || host.hasAttribute(matRippleDisabled),\n        rippleConfig: {\n          centered: host.hasAttribute(matRippleCentered),\n          terminateOnPointerUp: globalOptions?.terminateOnPointerUp,\n          animation: {\n            enterDuration,\n            exitDuration\n          }\n        }\n      };\n      const renderer = new RippleRenderer(target, this._ngZone, rippleEl, this._platform, this._injector);\n      const hasSetUpEvents = !target.rippleDisabled;\n      if (hasSetUpEvents) {\n        renderer.setupTriggerEvents(host);\n      }\n      this._hosts.set(host, {\n        target,\n        renderer,\n        hasSetUpEvents\n      });\n      host.removeAttribute(matRippleUninitialized);\n    }\n    destroyRipple(host) {\n      const ripple = this._hosts.get(host);\n      if (ripple) {\n        ripple.renderer._removeTriggerEvents();\n        this._hosts.delete(host);\n      }\n    }\n    static ɵfac = function MatRippleLoader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRippleLoader)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatRippleLoader,\n      factory: MatRippleLoader.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MatRippleLoader;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatRippleLoader as M };\n//# sourceMappingURL=ripple-loader-BnMiRtmT.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}