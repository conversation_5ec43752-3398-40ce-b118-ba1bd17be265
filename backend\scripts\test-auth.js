const axios = require('axios');

const BASE_URL = 'http://localhost:3003';
let authToken = '';
let userId = '';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User',
  phone: '+1234567890'
};

const testUser2 = {
  email: '<EMAIL>',
  password: 'TestPassword456!',
  firstName: 'Test2',
  lastName: 'User2',
  phone: '+1234567891'
};

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
}

// Test functions
async function testSignup() {
  console.log('\n🧪 Testing User Signup...');
  
  const result = await apiCall('POST', '/auth/signup', testUser);
  
  if (result.success) {
    console.log('✅ Signup successful:', result.data);
    userId = result.data.userId;
    return true;
  } else {
    console.log('❌ Signup failed:', result.error);
    return false;
  }
}

async function testLogin() {
  console.log('\n🧪 Testing User Login...');
  
  const result = await apiCall('POST', '/auth/login', {
    email: testUser.email,
    password: testUser.password
  });
  
  if (result.success) {
    console.log('✅ Login successful');
    authToken = result.data.token;
    console.log('🔑 Auth token received');
    return true;
  } else {
    console.log('❌ Login failed:', result.error);
    return false;
  }
}

async function test2FASetup() {
  console.log('\n🧪 Testing 2FA Setup...');
  
  const result = await apiCall('POST', '/2fa/setup', null, authToken);
  
  if (result.success) {
    console.log('✅ 2FA setup successful');
    console.log('🔐 Secret:', result.data.secret);
    console.log('📱 QR Code available');
    return result.data.secret;
  } else {
    console.log('❌ 2FA setup failed:', result.error);
    return null;
  }
}

async function test2FAStatus() {
  console.log('\n🧪 Testing 2FA Status...');
  
  const result = await apiCall('GET', '/2fa/status', null, authToken);
  
  if (result.success) {
    console.log('✅ 2FA status retrieved:', result.data);
    return result.data.enabled;
  } else {
    console.log('❌ 2FA status failed:', result.error);
    return false;
  }
}

async function test2FAEmail() {
  console.log('\n🧪 Testing 2FA Email OTP...');
  
  const result = await apiCall('POST', '/2fa/send-email', null, authToken);
  
  if (result.success) {
    console.log('✅ 2FA email sent:', result.data);
    return true;
  } else {
    console.log('❌ 2FA email failed:', result.error);
    return false;
  }
}

async function testChangePassword() {
  console.log('\n🧪 Testing Change Password...');
  
  const newPassword = 'NewTestPassword789!';
  const result = await apiCall('POST', '/auth/change-password', {
    currentPassword: testUser.password,
    newPassword: newPassword
  }, authToken);
  
  if (result.success) {
    console.log('✅ Password changed successfully:', result.data);
    testUser.password = newPassword; // Update for future tests
    return true;
  } else {
    console.log('❌ Password change failed:', result.error);
    return false;
  }
}

async function testForgotPassword() {
  console.log('\n🧪 Testing Forgot Password...');
  
  const result = await apiCall('POST', '/auth/forgot-password', {
    email: testUser.email
  });
  
  if (result.success) {
    console.log('✅ Forgot password request sent:', result.data);
    return true;
  } else {
    console.log('❌ Forgot password failed:', result.error);
    return false;
  }
}

async function testOAuthUrls() {
  console.log('\n🧪 Testing OAuth URLs...');
  
  const providers = ['google', 'github', 'microsoft'];
  
  for (const provider of providers) {
    const result = await apiCall('GET', `/auth/oauth/${provider}/url`);
    
    if (result.success) {
      console.log(`✅ ${provider} OAuth URL generated:`, result.data.url.substring(0, 100) + '...');
    } else {
      console.log(`❌ ${provider} OAuth URL failed:`, result.error);
    }
  }
}

async function testInvalidLogin() {
  console.log('\n🧪 Testing Invalid Login...');
  
  const result = await apiCall('POST', '/auth/login', {
    email: testUser.email,
    password: 'wrongpassword'
  });
  
  if (!result.success && result.status === 401) {
    console.log('✅ Invalid login properly rejected');
    return true;
  } else {
    console.log('❌ Invalid login should have been rejected');
    return false;
  }
}

async function testEmailVerification() {
  console.log('\n🧪 Testing Email Verification Flow...');
  
  // Create a new user for email verification test
  const result = await apiCall('POST', '/auth/signup', testUser2);
  
  if (result.success) {
    console.log('✅ Second user created for email verification test');
    console.log('📧 Check console logs for verification email details');
    return true;
  } else {
    console.log('❌ Failed to create second user:', result.error);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Authentication System Tests...');
  console.log('=' .repeat(50));
  
  const tests = [
    { name: 'Signup', fn: testSignup },
    { name: 'Login', fn: testLogin },
    { name: '2FA Setup', fn: test2FASetup },
    { name: '2FA Status', fn: test2FAStatus },
    { name: '2FA Email', fn: test2FAEmail },
    { name: 'Change Password', fn: testChangePassword },
    { name: 'Forgot Password', fn: testForgotPassword },
    { name: 'OAuth URLs', fn: testOAuthUrls },
    { name: 'Invalid Login', fn: testInvalidLogin },
    { name: 'Email Verification', fn: testEmailVerification }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message);
      failed++;
    }
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, apiCall, BASE_URL };
