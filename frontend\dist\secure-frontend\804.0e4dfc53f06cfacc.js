"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[804],{4804:(G,m,s)=>{s.r(m),s.d(m,{ProfileModule:()=>S});var l=s(6396),f=s(1736),a=s(9417),d=s(5596),h=s(8834),g=s(1074),C=s(340),p=s(6863),P=s(9183),F=s(8822),t=s(7241),i=s(8564),w=s(4796),v=s(3156),_=s(7097),b=s(4179),u=s(3573);function O(r,c){if(1&r&&(t.j41(0,"div",16),t.nrm(1,"img",17),t.k0s()),2&r){const e=t.XpG();t.R7$(),t.Y8G("src",e.currentUser.avatarUrl,t.B4B)("alt",(e.currentUser.firstName||"")+" "+(e.currentUser.lastName||""))}}function M(r,c){if(1&r&&(t.j41(0,"div",18)(1,"p")(2,"strong"),t.EFF(3,"Connected via:"),t.k0s(),t.j41(4,"span",19),t.nrm(5,"i"),t.EFF(6),t.k0s()()()),2&r){const e=t.XpG();t.R7$(5),t.HbH(e.getOAuthProviderIcon()),t.xc7("color",e.getOAuthProviderColor()),t.R7$(),t.SpI(" ",e.getOAuthProviderName()," ")}}function k(r,c){if(1&r){const e=t.RV6();t.j41(0,"button",20),t.bIt("click",function(){i.eBV(e);const n=t.XpG();return i.Njj(n.toggleChangePassword())}),t.j41(1,"mat-icon"),t.EFF(2),t.k0s(),t.EFF(3),t.k0s()}if(2&r){const e=t.XpG();t.R7$(2),t.JRh(e.showChangePassword?"expand_less":"expand_more"),t.R7$(),t.SpI(" ",e.showChangePassword?"Cancel":"Change Password"," ")}}function j(r,c){if(1&r&&(t.j41(0,"div",21)(1,"mat-icon",22),t.EFF(2,"info"),t.k0s(),t.j41(3,"span"),t.EFF(4),t.k0s()()),2&r){const e=t.XpG();t.R7$(4),t.SpI("Password managed by ",e.getOAuthProviderName())}}function E(r,c){if(1&r&&(t.j41(0,"mat-form-field",25)(1,"mat-label"),t.EFF(2,"2FA Code (Required)"),t.k0s(),t.nrm(3,"input",36),t.j41(4,"mat-icon",37),t.EFF(5,"verified_user"),t.k0s(),t.j41(6,"mat-hint"),t.EFF(7,"Enter the 6-digit code from your authenticator app"),t.k0s(),t.j41(8,"mat-error"),t.EFF(9),t.k0s()()),2&r){const e=t.XpG(2);t.R7$(9),t.JRh(e.getFieldError("twoFactorToken"))}}function R(r,c){1&r&&t.nrm(0,"mat-spinner",38)}function U(r,c){1&r&&(t.j41(0,"span"),t.EFF(1,"Change Password"),t.k0s())}function y(r,c){if(1&r){const e=t.RV6();t.j41(0,"div",23)(1,"form",24),t.bIt("ngSubmit",function(){i.eBV(e);const n=t.XpG();return i.Njj(n.onChangePassword())}),t.j41(2,"mat-form-field",25)(3,"mat-label"),t.EFF(4,"Current Password"),t.k0s(),t.nrm(5,"input",26),t.j41(6,"button",27),t.bIt("click",function(){i.eBV(e);const n=t.XpG();return i.Njj(n.hideCurrentPassword=!n.hideCurrentPassword)}),t.j41(7,"mat-icon"),t.EFF(8),t.k0s()(),t.j41(9,"mat-error"),t.EFF(10),t.k0s()(),t.j41(11,"mat-form-field",25)(12,"mat-label"),t.EFF(13,"New Password"),t.k0s(),t.nrm(14,"input",28),t.j41(15,"button",27),t.bIt("click",function(){i.eBV(e);const n=t.XpG();return i.Njj(n.hideNewPassword=!n.hideNewPassword)}),t.j41(16,"mat-icon"),t.EFF(17),t.k0s()(),t.j41(18,"mat-error"),t.EFF(19),t.k0s()(),t.j41(20,"mat-form-field",25)(21,"mat-label"),t.EFF(22,"Confirm New Password"),t.k0s(),t.nrm(23,"input",29),t.j41(24,"button",27),t.bIt("click",function(){i.eBV(e);const n=t.XpG();return i.Njj(n.hideConfirmPassword=!n.hideConfirmPassword)}),t.j41(25,"mat-icon"),t.EFF(26),t.k0s()(),t.j41(27,"mat-error"),t.EFF(28),t.k0s()(),t.DNE(29,E,10,1,"mat-form-field",30),t.j41(30,"div",31)(31,"button",32),t.DNE(32,R,1,0,"mat-spinner",33)(33,U,2,0,"span",34),t.k0s(),t.j41(34,"button",35),t.bIt("click",function(){i.eBV(e);const n=t.XpG();return i.Njj(n.toggleChangePassword())}),t.EFF(35," Cancel "),t.k0s()()()()}if(2&r){const e=t.XpG();t.R7$(),t.Y8G("formGroup",e.changePasswordForm),t.R7$(4),t.Y8G("type",e.hideCurrentPassword?"password":"text"),t.R7$(3),t.JRh(e.hideCurrentPassword?"visibility_off":"visibility"),t.R7$(2),t.JRh(e.getFieldError("currentPassword")),t.R7$(4),t.Y8G("type",e.hideNewPassword?"password":"text"),t.R7$(3),t.JRh(e.hideNewPassword?"visibility_off":"visibility"),t.R7$(2),t.JRh(e.getFieldError("newPassword")),t.R7$(4),t.Y8G("type",e.hideConfirmPassword?"password":"text"),t.R7$(3),t.JRh(e.hideConfirmPassword?"visibility_off":"visibility"),t.R7$(2),t.JRh(e.getFieldError("confirmPassword")),t.R7$(),t.Y8G("ngIf",e.twoFactorStatus.enabled),t.R7$(2),t.Y8G("disabled",e.loading),t.R7$(),t.Y8G("ngIf",e.loading),t.R7$(),t.Y8G("ngIf",!e.loading)}}const I=[{path:"",component:(()=>{class r{constructor(e,o,n,$,x){this.authService=e,this.twoFactorService=o,this.oauthService=n,this.formBuilder=$,this.snackBar=x,this.currentUser=null,this.twoFactorStatus={enabled:!1},this.loading=!1,this.hideCurrentPassword=!0,this.hideNewPassword=!0,this.hideConfirmPassword=!0,this.showChangePassword=!1,this.changePasswordForm=this.formBuilder.group({currentPassword:["",[a.k0.required]],newPassword:["",[a.k0.required,a.k0.minLength(8)]],confirmPassword:["",[a.k0.required]],twoFactorToken:[""]},{validators:this.passwordMatchValidator})}ngOnInit(){this.currentUser=this.authService.currentUserValue,this.load2FAStatus()}load2FAStatus(){this.twoFactorService.get2FAStatus().subscribe({next:e=>{this.twoFactorStatus=e},error:e=>{console.error("Failed to load 2FA status:",e)}})}toggleChangePassword(){this.showChangePassword=!this.showChangePassword,this.showChangePassword||this.changePasswordForm.reset()}onChangePassword(){if(this.changePasswordForm.invalid)return void this.markFormGroupTouched(this.changePasswordForm);this.loading=!0;const e=this.changePasswordForm.value;this.authService.changePassword(e.currentPassword,e.newPassword,e.twoFactorToken||void 0).subscribe({next:o=>{this.snackBar.open("Password changed successfully!","Close",{duration:3e3}),this.changePasswordForm.reset(),this.showChangePassword=!1,this.loading=!1},error:o=>{this.snackBar.open(o.message||"Failed to change password","Close",{duration:5e3}),this.loading=!1}})}isOAuthUser(){return this.oauthService.isOAuthUser(this.currentUser)}getOAuthProviderName(){return this.oauthService.getOAuthProviderName(this.currentUser)}getOAuthProviderIcon(){return this.oauthService.getOAuthProviderIcon(this.currentUser)}getOAuthProviderColor(){return this.oauthService.getOAuthProviderColor(this.currentUser)}getFieldError(e){const o=this.changePasswordForm.get(e);if(o?.errors&&o.touched){if(o.errors.required)return`${e} is required`;if(o.errors.minlength)return`${e} must be at least ${o.errors.minlength.requiredLength} characters`;if(o.errors.passwordMismatch)return"Passwords do not match"}return""}passwordMatchValidator(e){const o=e.get("newPassword"),n=e.get("confirmPassword");return n.setErrors(o&&n&&o.value!==n.value?{passwordMismatch:!0}:null),null}markFormGroupTouched(e){Object.keys(e.controls).forEach(o=>{e.get(o)?.markAsTouched()})}static#t=this.\u0275fac=function(o){return new(o||r)(t.rXU(w.u),t.rXU(v.f),t.rXU(_.T),t.rXU(a.ok),t.rXU(F.UG))};static#e=this.\u0275cmp=t.VBU({type:r,selectors:[["app-profile"]],standalone:!1,decls:66,vars:25,consts:[[1,"profile-container"],[1,"container"],[1,"user-info"],["class","user-avatar",4,"ngIf"],[1,"user-details"],["class","oauth-info",4,"ngIf"],[1,"account-status"],[1,"status-chips"],[1,"status-chip"],["mat-button","","color","primary"],[1,"security-section"],[1,"section-header"],["mat-button","","color","primary",3,"click",4,"ngIf"],["class","oauth-password-notice",4,"ngIf"],["class","change-password-form",4,"ngIf"],["mat-button","","color","accent","routerLink","/dashboard/two-factor"],[1,"user-avatar"],[3,"src","alt"],[1,"oauth-info"],[1,"oauth-provider"],["mat-button","","color","primary",3,"click"],[1,"oauth-password-notice"],["color","warn"],[1,"change-password-form"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"form-field"],["matInput","","formControlName","currentPassword","autocomplete","current-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],["matInput","","formControlName","newPassword","autocomplete","new-password",3,"type"],["matInput","","formControlName","confirmPassword","autocomplete","new-password",3,"type"],["class","form-field","appearance","outline",4,"ngIf"],[1,"form-actions"],["mat-raised-button","","color","primary","type","submit",3,"disabled"],["diameter","20",4,"ngIf"],[4,"ngIf"],["mat-button","","type","button",3,"click"],["matInput","","formControlName","twoFactorToken","placeholder","000000","maxlength","6","autocomplete","one-time-code"],["matSuffix",""],["diameter","20"]],template:function(o,n){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"h1"),t.EFF(3,"Profile & Security Settings"),t.k0s(),t.j41(4,"mat-card")(5,"mat-card-header")(6,"mat-card-title"),t.EFF(7,"User Information"),t.k0s()(),t.j41(8,"mat-card-content")(9,"div",2),t.DNE(10,O,2,2,"div",3),t.j41(11,"div",4)(12,"p")(13,"strong"),t.EFF(14,"Name:"),t.k0s(),t.EFF(15),t.k0s(),t.j41(16,"p")(17,"strong"),t.EFF(18,"Email:"),t.k0s(),t.EFF(19),t.k0s(),t.j41(20,"p")(21,"strong"),t.EFF(22,"Phone:"),t.k0s(),t.EFF(23),t.k0s(),t.j41(24,"p")(25,"strong"),t.EFF(26,"Member since:"),t.k0s(),t.EFF(27),t.nI1(28,"date"),t.k0s(),t.DNE(29,M,7,5,"div",5),t.j41(30,"div",6)(31,"div",7)(32,"div",8)(33,"mat-icon"),t.EFF(34),t.k0s(),t.j41(35,"span"),t.EFF(36),t.k0s()(),t.j41(37,"div",8)(38,"mat-icon"),t.EFF(39,"security"),t.k0s(),t.j41(40,"span"),t.EFF(41),t.k0s()()()()()()(),t.j41(42,"mat-card-actions")(43,"button",9)(44,"mat-icon"),t.EFF(45,"edit"),t.k0s(),t.EFF(46," Edit Profile "),t.k0s()()(),t.j41(47,"mat-card")(48,"mat-card-header")(49,"mat-card-title"),t.EFF(50,"Security Settings"),t.k0s()(),t.j41(51,"mat-card-content")(52,"p"),t.EFF(53,"Manage your account security settings including two-factor authentication and password."),t.k0s(),t.j41(54,"div",10)(55,"div",11)(56,"h3"),t.EFF(57,"Password"),t.k0s(),t.DNE(58,k,4,2,"button",12)(59,j,5,1,"div",13),t.k0s(),t.DNE(60,y,36,14,"div",14),t.k0s()(),t.j41(61,"mat-card-actions")(62,"button",15)(63,"mat-icon"),t.EFF(64,"security"),t.k0s(),t.EFF(65),t.k0s()()()()()),2&o&&(t.R7$(10),t.Y8G("ngIf",null==n.currentUser?null:n.currentUser.avatarUrl),t.R7$(5),t.Lme(" ",null==n.currentUser?null:n.currentUser.firstName," ",null==n.currentUser?null:n.currentUser.lastName),t.R7$(4),t.SpI(" ",null==n.currentUser?null:n.currentUser.email),t.R7$(4),t.SpI(" ",(null==n.currentUser?null:n.currentUser.phone)||"Not provided"),t.R7$(4),t.SpI(" ",t.i5U(28,22,null==n.currentUser?null:n.currentUser.createdAt,"mediumDate")),t.R7$(2),t.Y8G("ngIf",n.isOAuthUser()),t.R7$(3),t.AVh("verified",null==n.currentUser?null:n.currentUser.emailVerified)("unverified",!(null!=n.currentUser&&n.currentUser.emailVerified)),t.R7$(2),t.JRh(null!=n.currentUser&&n.currentUser.emailVerified?"verified":"warning"),t.R7$(2),t.SpI("Email ",null!=n.currentUser&&n.currentUser.emailVerified?"Verified":"Unverified"),t.R7$(),t.AVh("enabled",n.twoFactorStatus.enabled)("disabled",!n.twoFactorStatus.enabled),t.R7$(4),t.SpI("2FA ",n.twoFactorStatus.enabled?"Enabled":"Disabled"),t.R7$(17),t.Y8G("ngIf",!n.isOAuthUser()),t.R7$(),t.Y8G("ngIf",n.isOAuthUser()),t.R7$(),t.Y8G("ngIf",n.showChangePassword&&!n.isOAuthUser()),t.R7$(5),t.SpI(" ",n.twoFactorStatus.enabled?"Manage 2FA":"Setup 2FA"," "))},dependencies:[l.bT,f.Wk,a.qT,a.me,a.BC,a.cb,a.tU,a.j4,a.JD,d.RN,d.YY,d.m2,d.MM,d.dh,h.$z,b.M,g.An,u.j,u.M,u.c,u.b,u.g,p.fg,P.LG,l.vh],styles:[".profile-container[_ngcontent-%COMP%]{min-height:100vh;background:#f5f5f5;padding:2rem}.container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}h1[_ngcontent-%COMP%]{color:#333;margin-bottom:2rem}mat-card[_ngcontent-%COMP%]{margin-bottom:1.5rem}.user-info[_ngcontent-%COMP%]{display:flex;gap:1.5rem;align-items:flex-start}.user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:3px solid #e0e0e0}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]{flex:1}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333;margin-right:.5rem}.oauth-info[_ngcontent-%COMP%]{margin-top:1rem}.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.5rem;padding:.25rem .75rem;background:#f5f5f5;border-radius:20px;font-size:.875rem}.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.account-status[_ngcontent-%COMP%]{margin-top:1rem}.account-status[_ngcontent-%COMP%]   .status-chips[_ngcontent-%COMP%]{display:flex;gap:.5rem;flex-wrap:wrap}.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;padding:.5rem 1rem;border-radius:20px;font-size:.875rem;font-weight:500}.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.account-status[_ngcontent-%COMP%]   .status-chip.verified[_ngcontent-%COMP%]{background:#e8f5e8;color:#2e7d32;border:1px solid #4caf50}.account-status[_ngcontent-%COMP%]   .status-chip.unverified[_ngcontent-%COMP%]{background:#fff3e0;color:#f57c00;border:1px solid #ff9800}.account-status[_ngcontent-%COMP%]   .status-chip.enabled[_ngcontent-%COMP%]{background:#e3f2fd;color:#1976d2;border:1px solid #2196f3}.account-status[_ngcontent-%COMP%]   .status-chip.disabled[_ngcontent-%COMP%]{background:#f5f5f5;color:#666;border:1px solid #ccc}.security-section[_ngcontent-%COMP%]{margin-top:1.5rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#333}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#666;font-size:.875rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.change-password-form[_ngcontent-%COMP%]{background:#f9f9f9;padding:1.5rem;border-radius:8px;border:1px solid #e0e0e0}.change-password-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{width:100%;margin-bottom:1rem}.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-top:1.5rem}.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}"]})}return r})()}];let S=(()=>{class r{static#t=this.\u0275fac=function(o){return new(o||r)};static#e=this.\u0275mod=t.$C({type:r});static#n=this.\u0275inj=i.G2t({imports:[l.MD,f.iI.forChild(I),a.X1,d.Hu,h.Hl,g.m_,C.M,p.fS,P.D6,F._T]})}return r})()}}]);