{"ast": null, "code": "import { L as ListKeyManager } from './list-key-manager-C7tp3RbG.mjs';\nclass FocusKeyManager extends ListKeyManager {\n  _origin = 'program';\n  /**\n   * Sets the focus origin that will be passed in to the items for any subsequent `focus` calls.\n   * @param origin Focus origin to be used when focusing items.\n   */\n  setFocusOrigin(origin) {\n    this._origin = origin;\n    return this;\n  }\n  setActiveItem(item) {\n    super.setActiveItem(item);\n    if (this.activeItem) {\n      this.activeItem.focus(this._origin);\n    }\n  }\n}\nexport { FocusKeyManager as F };\n//# sourceMappingURL=focus-key-manager-CPmlyB_c.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}