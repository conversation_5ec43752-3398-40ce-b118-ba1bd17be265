// Use global styles

.w-100 { width: 100%; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mb-3 { margin-bottom: 1rem; }
.text-center { text-align: center; }
.text-primary {
  color: #3f51b5;
  text-decoration: none;
  &:hover { text-decoration: underline; }
}

// OAuth specific styles
.oauth-section {
  .divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e0e0e0;
    }

    span {
      background: white;
      padding: 0 1rem;
      color: #666;
      font-size: 0.875rem;
    }
  }
}

.oauth-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  .oauth-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    width: 100%;
    border-radius: 8px;
    transition: all 0.2s ease;

    i {
      font-size: 1.25rem;
    }

    span {
      font-weight: 500;
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }
}
