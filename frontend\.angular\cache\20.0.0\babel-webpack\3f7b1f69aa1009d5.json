{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/payment.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nfunction DashboardComponent_mat_card_60_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"mat-icon\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 34);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 35);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const payment_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + payment_r1.status);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getStatusIcon(payment_r1.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(payment_r1.description || \"Payment\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 5, payment_r1.createdAt, \"medium\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatCurrency(payment_r1.amount, payment_r1.currency), \" \");\n  }\n}\nfunction DashboardComponent_mat_card_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 25)(1, \"mat-card-header\")(2, \"mat-card-title\");\n    i0.ɵɵtext(3, \"Recent Payments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-subtitle\");\n    i0.ɵɵtext(5, \"Your latest transactions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 26);\n    i0.ɵɵtemplate(8, DashboardComponent_mat_card_60_div_8_Template, 12, 8, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 28)(10, \"button\", 29);\n    i0.ɵɵtext(11, \" View All Payments \");\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"arrow_forward\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentPayments);\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    constructor(authService, paymentService) {\n      this.authService = authService;\n      this.paymentService = paymentService;\n      this.currentUser = null;\n      this.recentPayments = [];\n      this.stats = {\n        totalPayments: 0,\n        successfulPayments: 0,\n        totalAmount: 0\n      };\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.currentUserValue;\n      this.loadDashboardData();\n    }\n    loadDashboardData() {\n      this.paymentService.getMyPayments().subscribe({\n        next: response => {\n          this.recentPayments = response.payments.slice(0, 5);\n          this.calculateStats(response.payments);\n        },\n        error: error => {\n          console.error('Failed to load payments:', error);\n        }\n      });\n    }\n    calculateStats(payments) {\n      this.stats.totalPayments = payments.length;\n      this.stats.successfulPayments = payments.filter(p => p.status === 'paid').length;\n      this.stats.totalAmount = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0);\n    }\n    formatCurrency(amount, currency) {\n      return this.paymentService.formatCurrency(amount, currency);\n    }\n    getStatusIcon(status) {\n      return this.paymentService.getStatusIcon(status);\n    }\n    static #_ = this.ɵfac = function DashboardComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.PaymentService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      standalone: false,\n      decls: 88,\n      vars: 13,\n      consts: [[1, \"dashboard-container\"], [1, \"container\"], [1, \"welcome-section\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"stat-icon\", \"success\"], [1, \"stat-icon\", \"primary\"], [1, \"actions-card\"], [1, \"actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/payment/test\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/profile\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/profile\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/payment\"], [\"class\", \"payments-card\", 4, \"ngIf\"], [1, \"security-card\"], [1, \"security-items\"], [1, \"security-item\"], [3, \"ngClass\"], [1, \"status\", 3, \"ngClass\"], [1, \"security-actions\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/profile\"], [1, \"payments-card\"], [1, \"payment-list\"], [\"class\", \"payment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all\"], [\"mat-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/payment\"], [1, \"payment-item\"], [1, \"payment-icon\"], [1, \"payment-details\"], [1, \"payment-description\"], [1, \"payment-date\"], [1, \"payment-amount\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Here's an overview of your account activity\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-card\", 4)(9, \"mat-card-content\")(10, \"div\", 5)(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"payment\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 6)(14, \"div\", 7);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 8);\n          i0.ɵɵtext(17, \"Total Payments\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(18, \"mat-card\", 4)(19, \"mat-card-content\")(20, \"div\", 9)(21, \"mat-icon\");\n          i0.ɵɵtext(22, \"check_circle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 6)(24, \"div\", 7);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 8);\n          i0.ɵɵtext(27, \"Successful\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"mat-card\", 4)(29, \"mat-card-content\")(30, \"div\", 10)(31, \"mat-icon\");\n          i0.ɵɵtext(32, \"account_balance_wallet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 6)(34, \"div\", 7);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 8);\n          i0.ɵɵtext(37, \"Total Amount\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(38, \"mat-card\", 11)(39, \"mat-card-header\")(40, \"mat-card-title\");\n          i0.ɵɵtext(41, \"Quick Actions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"mat-card-content\")(43, \"div\", 12)(44, \"button\", 13)(45, \"mat-icon\");\n          i0.ɵɵtext(46, \"payment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(47, \" Test Payment \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"button\", 14)(49, \"mat-icon\");\n          i0.ɵɵtext(50, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(51, \" Setup 2FA \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"button\", 15)(53, \"mat-icon\");\n          i0.ɵɵtext(54, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \" Edit Profile \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"button\", 16)(57, \"mat-icon\");\n          i0.ɵɵtext(58, \"history\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(59, \" Payment History \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(60, DashboardComponent_mat_card_60_Template, 14, 1, \"mat-card\", 17);\n          i0.ɵɵelementStart(61, \"mat-card\", 18)(62, \"mat-card-header\")(63, \"mat-card-title\")(64, \"mat-icon\");\n          i0.ɵɵtext(65, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(66, \" Security Status \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"mat-card-content\")(68, \"div\", 19)(69, \"div\", 20)(70, \"mat-icon\", 21);\n          i0.ɵɵtext(71);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"span\");\n          i0.ɵɵtext(73, \"Email Verification\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"span\", 22);\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 20)(77, \"mat-icon\", 21);\n          i0.ɵɵtext(78);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"span\");\n          i0.ɵɵtext(80, \"Two-Factor Authentication\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"span\", 22);\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(83, \"div\", 23)(84, \"button\", 24)(85, \"mat-icon\");\n          i0.ɵɵtext(86, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(87, \" Manage Security \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"Welcome back, \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \"!\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.stats.totalPayments);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(ctx.stats.successfulPayments);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", ctx.stats.totalAmount);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngIf\", ctx.recentPayments.length > 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngClass\", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"unverified\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"warning\", \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"unverified\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"Verified\" : \"Pending\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", (ctx.currentUser == null ? null : ctx.currentUser.twoFactorEnabled) ? \"verified\" : \"unverified\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.twoFactorEnabled) ? \"security\" : \"warning\", \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", (ctx.currentUser == null ? null : ctx.currentUser.twoFactorEnabled) ? \"verified\" : \"unverified\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.twoFactorEnabled) ? \"Enabled\" : \"Disabled\", \" \");\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.RouterLink, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardSubtitle, i5.MatCardTitle, i6.MatButton, i7.MatIcon, i3.DatePipe],\n      styles: [\".dashboard-container[_ngcontent-%COMP%]{min-height:100vh;background:#f5f5f5;padding:2rem}.container[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto}.welcome-section[_ngcontent-%COMP%]{margin-bottom:2rem}.welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#333;margin-bottom:.5rem;font-size:2rem;font-weight:400}.welcome-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:1.1rem}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1.5rem;margin-bottom:2rem}.stat-card[_ngcontent-%COMP%]   .mat-mdc-card-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:1.5rem}.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;display:flex;align-items:center;justify-content:center;background:#e3f2fd;color:#2196f3}.stat-card[_ngcontent-%COMP%]   .stat-icon.success[_ngcontent-%COMP%]{background:#e8f5e8;color:#4caf50}.stat-card[_ngcontent-%COMP%]   .stat-icon.primary[_ngcontent-%COMP%]{background:#f3e5f5;color:#9c27b0}.stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:28px;width:28px;height:28px}.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{font-size:2rem;font-weight:600;color:#333;line-height:1}.stat-card[_ngcontent-%COMP%]   .stat-info[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{color:#666;font-size:.875rem;margin-top:.25rem}.actions-card[_ngcontent-%COMP%]{margin-bottom:2rem}.actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem}.actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{height:56px;font-size:1rem}.actions-card[_ngcontent-%COMP%]   .actions-grid[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:.5rem}.payments-card[_ngcontent-%COMP%]{margin-bottom:2rem}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:1rem 0;border-bottom:1px solid #e0e0e0}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;background:#f5f5f5}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-icon[_ngcontent-%COMP%]   mat-icon.status-paid[_ngcontent-%COMP%]{color:#4caf50}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-icon[_ngcontent-%COMP%]   mat-icon.status-pending[_ngcontent-%COMP%]{color:#ff9800}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-icon[_ngcontent-%COMP%]   mat-icon.status-failed[_ngcontent-%COMP%]{color:#f44336}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%]{flex:1}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%]   .payment-description[_ngcontent-%COMP%]{font-weight:500;color:#333;margin-bottom:.25rem}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-details[_ngcontent-%COMP%]   .payment-date[_ngcontent-%COMP%]{font-size:.875rem;color:#666}.payments-card[_ngcontent-%COMP%]   .payment-list[_ngcontent-%COMP%]   .payment-amount[_ngcontent-%COMP%]{font-weight:600;color:#333;font-size:1.1rem}.payments-card[_ngcontent-%COMP%]   .view-all[_ngcontent-%COMP%]{text-align:center;margin-top:1rem;padding-top:1rem;border-top:1px solid #e0e0e0}.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;padding:1rem 0;border-bottom:1px solid #e0e0e0}.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   mat-icon.verified[_ngcontent-%COMP%]{color:#4caf50}.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   mat-icon.unverified[_ngcontent-%COMP%]{color:#ff9800}.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){flex:1;font-weight:500;color:#333}.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{font-size:.875rem;padding:.25rem .75rem;border-radius:12px;font-weight:500}.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .status.verified[_ngcontent-%COMP%]{background:#e8f5e8;color:#4caf50}.security-card[_ngcontent-%COMP%]   .security-items[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .status.unverified[_ngcontent-%COMP%]{background:#fff3e0;color:#ff9800}.security-card[_ngcontent-%COMP%]   .security-actions[_ngcontent-%COMP%]{text-align:center;margin-top:1rem;padding-top:1rem;border-top:1px solid #e0e0e0}@media (max-width: 768px){.dashboard-container[_ngcontent-%COMP%]{padding:1rem}.welcome-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}.actions-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n    });\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}