{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"./services/loading.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nimport * as i10 from \"@angular/material/divider\";\nconst _c0 = a0 => ({\n  \"with-navbar\": a0\n});\nconst _c1 = a0 => ({\n  \"verified\": a0\n});\nfunction AppComponent_mat_toolbar_0_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" 2FA Enabled \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_mat_toolbar_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-toolbar\", 5)(1, \"span\", 6)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 7);\n    i0.ɵɵelementStart(6, \"button\", 8)(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Dashboard \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 9)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"payment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Payments \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 10)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 11)(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-menu\", null, 0)(23, \"div\", 12)(24, \"div\", 13);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 14);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 15)(29, \"span\", 16)(30, \"mat-icon\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, AppComponent_mat_toolbar_0_span_33_Template, 4, 0, \"span\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(34, \"mat-divider\");\n    i0.ɵɵelementStart(35, \"button\", 18)(36, \"mat-icon\");\n    i0.ɵɵtext(37, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function AppComponent_mat_toolbar_0_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(40, \"mat-icon\");\n    i0.ɵɵtext(41, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42, \" Logout \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const userMenu_r3 = i0.ɵɵreference(22);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.title, \" \");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getUserDisplayName());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser == null ? null : ctx_r1.currentUser.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r1.isEmailVerified()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.isEmailVerified() ? \"verified\" : \"warning\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isEmailVerified() ? \"Verified\" : \"Unverified\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTwoFactorEnabled());\n  }\n}\nfunction AppComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"mat-spinner\", 22);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Please verify your email address to access all features.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 24);\n    i0.ɵɵtext(6, \"Verify Now\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor(authService, loadingService, router) {\n      this.authService = authService;\n      this.loadingService = loadingService;\n      this.router = router;\n      this.title = 'SecureApp';\n      this.currentUser = null;\n      this.loading$ = this.loadingService.loading$;\n      this.showNavbar = false;\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      // Subscribe to current user\n      this.authService.currentUser.pipe(takeUntil(this.destroy$)).subscribe(user => {\n        this.currentUser = user;\n      });\n      // Auto logout on token expiration\n      setInterval(() => {\n        this.authService.autoLogout();\n      }, 60000); // Check every minute\n      // Show/hide navbar based on route\n      this.router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this.destroy$)).subscribe(event => {\n        this.showNavbar = !event.url.startsWith('/auth');\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    logout() {\n      this.authService.logout();\n    }\n    getUserDisplayName() {\n      if (this.currentUser) {\n        return `${this.currentUser.firstName} ${this.currentUser.lastName}`;\n      }\n      return '';\n    }\n    isEmailVerified() {\n      return this.authService.isEmailVerified;\n    }\n    isTwoFactorEnabled() {\n      return this.authService.isTwoFactorEnabled;\n    }\n    static #_ = this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.LoadingService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: false,\n      decls: 6,\n      vars: 8,\n      consts: [[\"userMenu\", \"matMenu\"], [\"color\", \"primary\", \"class\", \"navbar\", 4, \"ngIf\"], [3, \"ngClass\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [\"class\", \"security-notification\", 4, \"ngIf\"], [\"color\", \"primary\", 1, \"navbar\"], [1, \"app-title\"], [1, \"spacer\"], [\"mat-button\", \"\", \"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/payment\", \"routerLinkActive\", \"active\"], [\"mat-button\", \"\", \"routerLink\", \"/profile\", \"routerLinkActive\", \"active\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"user-email\"], [1, \"user-status\"], [1, \"status-badge\", 3, \"ngClass\"], [\"class\", \"status-badge verified\", 4, \"ngIf\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"status-badge\", \"verified\"], [1, \"loading-overlay\"], [\"diameter\", \"50\"], [1, \"security-notification\"], [\"mat-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/profile\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppComponent_mat_toolbar_0_Template, 43, 10, \"mat-toolbar\", 1);\n          i0.ɵɵelementStart(1, \"main\", 2);\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, AppComponent_div_3_Template, 2, 0, \"div\", 3);\n          i0.ɵɵpipe(4, \"async\");\n          i0.ɵɵtemplate(5, AppComponent_div_5_Template, 7, 0, \"div\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showNavbar && ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c0, ctx.showNavbar && ctx.currentUser));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 4, ctx.loading$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser && !ctx.isEmailVerified());\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i3.RouterOutlet, i3.RouterLink, i3.RouterLinkActive, i5.MatToolbar, i6.MatButton, i6.MatIconButton, i7.MatIcon, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.MatProgressSpinner, i10.MatDivider, i4.AsyncPipe],\n      styles: [\".navbar[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;z-index:1000;box-shadow:0 2px 8px #0000001a}.app-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:1.25rem;font-weight:500}.spacer[_ngcontent-%COMP%]{flex:1 1 auto}.navbar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin:0 .25rem}.navbar[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%]{background:#ffffff1a}main[_ngcontent-%COMP%]{min-height:100vh}main.with-navbar[_ngcontent-%COMP%]{padding-top:64px}.user-info[_ngcontent-%COMP%]{padding:1rem;min-width:250px}.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%]{font-weight:500;font-size:1rem;margin-bottom:.25rem}.user-info[_ngcontent-%COMP%]   .user-email[_ngcontent-%COMP%]{color:#666;font-size:.875rem;margin-bottom:.75rem}.user-info[_ngcontent-%COMP%]   .user-status[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.user-info[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.25rem;padding:.25rem .5rem;border-radius:12px;font-size:.75rem;font-weight:500;background:#ffebee;color:#f44336}.user-info[_ngcontent-%COMP%]   .status-badge.verified[_ngcontent-%COMP%]{background:#e8f5e8;color:#4caf50}.user-info[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:14px;width:14px;height:14px}.loading-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#00000080;display:flex;align-items:center;justify-content:center;z-index:9999}.security-notification[_ngcontent-%COMP%]{position:fixed;top:64px;left:0;right:0;background:#ff9800;color:#fff;padding:.75rem 1rem;display:flex;align-items:center;gap:.5rem;z-index:999}.security-notification[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px}.security-notification[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{flex:1}.security-notification[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{color:#fff}@media (max-width: 768px){.navbar[_ngcontent-%COMP%]   .app-title[_ngcontent-%COMP%]{font-size:1rem}.navbar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:none}.user-info[_ngcontent-%COMP%]{min-width:200px;padding:.75rem}}\"]\n    });\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}