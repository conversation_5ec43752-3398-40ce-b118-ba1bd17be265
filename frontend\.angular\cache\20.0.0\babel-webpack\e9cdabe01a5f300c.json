{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction LoginComponent_form_12_mat_spinner_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction LoginComponent_form_12_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 7);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_12_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"mat-form-field\", 8)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 9);\n    i0.ɵɵelementStart(5, \"mat-icon\", 10);\n    i0.ɵɵtext(6, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 8)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 11);\n    i0.ɵɵelementStart(13, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_12_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hidePassword = !ctx_r1.hidePassword);\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-error\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 13);\n    i0.ɵɵtemplate(19, LoginComponent_form_12_mat_spinner_19_Template, 1, 0, \"mat-spinner\", 14)(20, LoginComponent_form_12_span_20_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 16)(22, \"span\");\n    i0.ɵɵtext(23, \"or\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_12_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Login with OTP \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 18)(29, \"a\", 19);\n    i0.ɵɵtext(30, \"Forgot Password?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 20)(32, \"span\");\n    i0.ɵɵtext(33, \"Don't have an account? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"a\", 21);\n    i0.ɵɵtext(35, \"Sign Up\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.loginForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r1.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"password\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_13_mat_spinner_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction LoginComponent_form_13_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 7);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_13_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTwoFactorSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"mat-icon\", 24);\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Two-Factor Authentication\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 8)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Authentication Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 25);\n    i0.ɵɵelementStart(12, \"mat-icon\", 10);\n    i0.ɵɵtext(13, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-error\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"button\", 13);\n    i0.ɵɵtemplate(17, LoginComponent_form_13_mat_spinner_17_Template, 1, 0, \"mat-spinner\", 14)(18, LoginComponent_form_13_span_18_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_13_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.backToLogin());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.twoFactorForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.twoFactorForm, \"twoFactorToken\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_14_button_16_mat_spinner_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction LoginComponent_form_14_button_16_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Send OTP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_14_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵtemplate(1, LoginComponent_form_14_button_16_mat_spinner_1_Template, 1, 0, \"mat-spinner\", 14)(2, LoginComponent_form_14_button_16_span_2_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_14_div_17_mat_spinner_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 22);\n  }\n}\nfunction LoginComponent_form_14_div_17_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_14_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 8)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Enter OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 31);\n    i0.ɵɵelementStart(5, \"mat-icon\", 10);\n    i0.ɵɵtext(6, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_div_17_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loginWithOTP());\n    });\n    i0.ɵɵtemplate(10, LoginComponent_form_14_div_17_mat_spinner_10_Template, 1, 0, \"mat-spinner\", 14)(11, LoginComponent_form_14_div_17_span_11_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_div_17_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Resend OTP \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"code\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 27)(1, \"div\", 23)(2, \"mat-icon\", 24);\n    i0.ɵɵtext(3, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Login with OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Enter your email or phone number to receive a one-time password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 8)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Email or Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 28);\n    i0.ɵɵelementStart(12, \"mat-icon\", 10);\n    i0.ɵɵtext(13, \"contact_mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-error\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, LoginComponent_form_14_button_16_Template, 3, 3, \"button\", 29)(17, LoginComponent_form_14_div_17_Template, 16, 4, \"div\", 15);\n    i0.ɵɵelementStart(18, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.otpForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"identifier\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.otpSent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpSent);\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(formBuilder, authService, router, route, snackBar) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.loading = false;\n      this.hidePassword = true;\n      this.showOTPLogin = false;\n      this.showTwoFactor = false;\n      this.otpSent = false;\n      this.returnUrl = '';\n      this.loginForm = this.formBuilder.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(8)]]\n      });\n      this.otpForm = this.formBuilder.group({\n        identifier: ['', [Validators.required]],\n        code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n      });\n      this.twoFactorForm = this.formBuilder.group({\n        twoFactorToken: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n      });\n    }\n    ngOnInit() {\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n      if (this.authService.isAuthenticated) {\n        this.router.navigate([this.returnUrl]);\n      }\n      const message = this.route.snapshot.queryParams['message'];\n      if (message) {\n        this.snackBar.open(message, 'Close', {\n          duration: 5000\n        });\n      }\n    }\n    onSubmit() {\n      if (this.loginForm.invalid) {\n        this.markFormGroupTouched(this.loginForm);\n        return;\n      }\n      this.loading = true;\n      const credentials = this.loginForm.value;\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          if (response.requiresTwoFactor) {\n            this.showTwoFactor = true;\n            this.snackBar.open('Please enter your two-factor authentication code', 'Close', {\n              duration: 5000\n            });\n          } else {\n            this.snackBar.open('Login successful!', 'Close', {\n              duration: 3000\n            });\n            this.router.navigate([this.returnUrl]);\n          }\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'Login failed', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    onTwoFactorSubmit() {\n      if (this.twoFactorForm.invalid) {\n        this.markFormGroupTouched(this.twoFactorForm);\n        return;\n      }\n      this.loading = true;\n      const credentials = {\n        ...this.loginForm.value,\n        twoFactorToken: this.twoFactorForm.value.twoFactorToken\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'Two-factor authentication failed', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    sendOTP() {\n      const identifier = this.otpForm.get('identifier')?.value;\n      if (!identifier) {\n        this.snackBar.open('Please enter email or phone number', 'Close', {\n          duration: 3000\n        });\n        return;\n      }\n      this.loading = true;\n      const request = {\n        identifier,\n        type: 'login'\n      };\n      this.authService.sendOTP(request).subscribe({\n        next: () => {\n          this.otpSent = true;\n          this.snackBar.open('OTP sent successfully!', 'Close', {\n            duration: 3000\n          });\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'Failed to send OTP', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    loginWithOTP() {\n      if (this.otpForm.invalid) {\n        this.markFormGroupTouched(this.otpForm);\n        return;\n      }\n      this.loading = true;\n      const {\n        identifier,\n        code\n      } = this.otpForm.value;\n      this.authService.loginWithOTP(identifier, code).subscribe({\n        next: () => {\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'OTP login failed', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    toggleOTPLogin() {\n      this.showOTPLogin = !this.showOTPLogin;\n      this.showTwoFactor = false;\n      this.otpSent = false;\n      this.otpForm.reset();\n    }\n    backToLogin() {\n      this.showTwoFactor = false;\n      this.showOTPLogin = false;\n      this.otpSent = false;\n    }\n    getFieldError(form, fieldName) {\n      const field = form.get(fieldName);\n      if (field?.errors && field.touched) {\n        if (field.errors['required']) return `${fieldName} is required`;\n        if (field.errors['email']) return 'Please enter a valid email';\n        if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        if (field.errors['pattern']) return 'Please enter a valid format';\n      }\n      return '';\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n      });\n    }\n    static #_ = this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      standalone: false,\n      decls: 15,\n      vars: 3,\n      consts: [[1, \"auth-container\"], [1, \"auth-card\", \"fade-in\"], [1, \"auth-header\"], [1, \"security-badge\"], [1, \"auth-content\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"divider\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"w-100\", 3, \"click\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"text-primary\"], [1, \"text-center\", \"mt-2\"], [\"routerLink\", \"/auth/register\", 1, \"text-primary\"], [\"diameter\", \"20\"], [1, \"text-center\", \"mb-3\"], [\"color\", \"primary\", 2, \"font-size\", \"48px\", \"width\", \"48px\", \"height\", \"48px\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"w-100\", \"mt-2\", 3, \"click\"], [3, \"formGroup\"], [\"matInput\", \"\", \"formControlName\", \"identifier\", \"placeholder\", \"<EMAIL> or +1234567890\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", \"class\", \"submit-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4, \"Welcome Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Sign in to your secure account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n          i0.ɵɵtext(9, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" Secure Login \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 4);\n          i0.ɵɵtemplate(12, LoginComponent_form_12_Template, 36, 8, \"form\", 5)(13, LoginComponent_form_13_Template, 23, 5, \"form\", 5)(14, LoginComponent_form_14_Template, 22, 4, \"form\", 6);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showOTPLogin && !ctx.showTwoFactor);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showTwoFactor);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showOTPLogin);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i6.MatFormField, i6.MatLabel, i6.MatError, i6.MatSuffix, i7.MatInput, i8.MatButton, i8.MatIconButton, i9.MatIcon, i10.MatProgressSpinner],\n      styles: [\".w-100[_ngcontent-%COMP%]{width:100%}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:1rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:1rem}.text-center[_ngcontent-%COMP%]{text-align:center}.text-primary[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.text-primary[_ngcontent-%COMP%]:hover{text-decoration:underline}\"]\n    });\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}