"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const axios_1 = tslib_1.__importDefault(require("axios"));
let EmailService = class EmailService {
    constructor() {
        this.brevoApiUrl = 'https://api.brevo.com/v3/smtp/email';
        this.brevoApiKey = process.env.BREVO_API_KEY || '';
        this.useBrevo = !!this.brevoApiKey && this.brevoApiKey !== 'your_brevo_api_key_here';
        if (!this.useBrevo) {
            console.log('📧 Using fallback email service (console logging)');
        }
        else {
            console.log('📧 Using Brevo email service');
        }
    }
    async sendEmailViaBrevo(to, subject, htmlContent) {
        try {
            const emailData = {
                sender: {
                    name: process.env.EMAIL_FROM_NAME || 'SecureApp',
                    email: process.env.EMAIL_FROM || '<EMAIL>'
                },
                to: [{ email: to }],
                subject,
                htmlContent
            };
            const response = await axios_1.default.post(this.brevoApiUrl, emailData, {
                headers: {
                    'api-key': this.brevoApiKey,
                    'Content-Type': 'application/json'
                }
            });
            console.log(`✅ Email sent via Brevo to ${to} - Message ID: ${response.data.messageId}`);
        }
        catch (error) {
            console.error('❌ Failed to send email via Brevo:', error.response?.data || error.message);
            throw new Error(`Failed to send email via Brevo: ${error.response?.data?.message || error.message}`);
        }
    }
    async sendEmailViaFallback(to, subject, htmlContent) {
        console.log('📧 [DEMO MODE] Email would be sent:');
        console.log(`To: ${to}`);
        console.log(`Subject: ${subject}`);
        console.log(`HTML Content: ${htmlContent.substring(0, 200)}...`);
        console.log('✅ Email logged to console (demo mode)');
    }
    async sendVerificationEmail(email, token) {
        const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:4200'}/verify-email?token=${token}`;
        const subject = 'Verify Your Email Address';
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Verify Your Email Address</h2>
        <p>Thank you for registering with SecureApp. Please click the button below to verify your email address:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}"
             style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Verify Email
          </a>
        </div>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
        <p style="color: #666; font-size: 12px;">This link will expire in 24 hours.</p>
        <p style="color: #666; font-size: 12px;">If you didn't create an account, please ignore this email.</p>
      </div>
    `;
        if (this.useBrevo) {
            await this.sendEmailViaBrevo(email, subject, htmlContent);
        }
        else {
            await this.sendEmailViaFallback(email, subject, htmlContent);
        }
    }
    async sendPasswordResetEmail(email, token) {
        const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:4200'}/reset-password?token=${token}`;
        const subject = 'Reset Your Password';
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Reset Your Password</h2>
        <p>You requested to reset your password. Click the button below to set a new password:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}"
             style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Reset Password
          </a>
        </div>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #666;">${resetUrl}</p>
        <p style="color: #666; font-size: 12px;">This link will expire in 1 hour.</p>
        <p style="color: #666; font-size: 12px;">If you didn't request this, please ignore this email.</p>
      </div>
    `;
        if (this.useBrevo) {
            await this.sendEmailViaBrevo(email, subject, htmlContent);
        }
        else {
            await this.sendEmailViaFallback(email, subject, htmlContent);
        }
    }
    async sendOTPEmail(email, otp, type) {
        const subject = type === 'login' ? 'Your Login Code' :
            type === '2fa' ? 'Your 2FA Verification Code' : 'Your Verification Code';
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">${subject}</h2>
        <p>Your verification code is:</p>
        <div style="text-align: center; margin: 30px 0;">
          <div style="background-color: #f8f9fa; border: 2px dashed #007bff; padding: 20px; font-size: 32px; font-weight: bold; letter-spacing: 5px; color: #007bff;">
            ${otp}
          </div>
        </div>
        <p style="color: #666;">This code will expire in 10 minutes.</p>
        <p style="color: #666; font-size: 12px;">If you didn't request this code, please ignore this email.</p>
      </div>
    `;
        if (this.useBrevo) {
            await this.sendEmailViaBrevo(email, subject, htmlContent);
        }
        else {
            await this.sendEmailViaFallback(email, subject, htmlContent);
        }
    }
    async send2FAEmail(email, otp) {
        await this.sendOTPEmail(email, otp, '2fa');
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__metadata("design:paramtypes", [])
], EmailService);
//# sourceMappingURL=email.service.js.map