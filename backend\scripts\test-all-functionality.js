const axios = require('axios');
const speakeasy = require('speakeasy');

const BASE_URL = 'http://localhost:3002';
let authToken = '';
let twoFactorSecret = '';
let otpCode = '';

// Test user data with timestamp
const timestamp = Date.now();
const testUser = {
  email: `fulltest-${timestamp}@example.com`,
  password: 'FullTest123!',
  firstName: 'Full',
  lastName: 'Test',
  phone: `+123456${timestamp.toString().slice(-4)}`
};

// Helper function to make API calls with detailed logging
async function apiCall(method, endpoint, data = null, token = null, description = '') {
  console.log(`\n🔄 ${description || `${method} ${endpoint}`}`);
  console.log(`📤 Request: ${method} ${BASE_URL}${endpoint}`);
  if (data) console.log(`📦 Body:`, JSON.stringify(data, null, 2));
  if (token) console.log(`🔑 Token: ${token.substring(0, 20)}...`);

  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    console.log(`✅ Success: ${response.status} ${response.statusText}`);
    console.log(`📥 Response:`, JSON.stringify(response.data, null, 2));
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    console.log(`❌ Error: ${error.response?.status || 'Network'} ${error.response?.statusText || error.message}`);
    console.log(`📥 Error Response:`, JSON.stringify(error.response?.data || error.message, null, 2));
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
}

async function testAllFunctionality() {
  console.log('🚀 COMPREHENSIVE FUNCTIONALITY TEST');
  console.log('=' .repeat(80));
  console.log('👤 Test User:', testUser.email);
  console.log('📱 Test Phone:', testUser.phone);
  console.log('=' .repeat(80));

  // 1. User Registration
  console.log('\n1️⃣  USER REGISTRATION');
  console.log('-'.repeat(40));
  const signupResult = await apiCall('POST', '/auth/signup', testUser, null, 'Register new user');
  if (!signupResult.success && signupResult.status !== 409) {
    console.log('🛑 Cannot continue without user registration');
    return;
  }

  // 2. User Login
  console.log('\n2️⃣  USER LOGIN');
  console.log('-'.repeat(40));
  const loginResult = await apiCall('POST', '/auth/login', {
    email: testUser.email,
    password: testUser.password
  }, null, 'Login with email and password');
  
  if (loginResult.success) {
    authToken = loginResult.data.token;
    console.log('🎯 Auth token obtained for subsequent requests');
  } else {
    console.log('🛑 Cannot continue without authentication token');
    return;
  }

  // 3. Test OTP Send (Email) - This is where the 5000 error occurs
  console.log('\n3️⃣  OTP SEND (EMAIL)');
  console.log('-'.repeat(40));
  const otpEmailResult = await apiCall('POST', '/otp/send', {
    email: testUser.email,
    type: 'login'
  }, null, 'Send OTP to email for login');

  // 4. Test OTP Send (Phone)
  console.log('\n4️⃣  OTP SEND (PHONE)');
  console.log('-'.repeat(40));
  const otpPhoneResult = await apiCall('POST', '/otp/send', {
    phone: testUser.phone,
    type: 'login'
  }, null, 'Send OTP to phone (should route to email)');

  // 5. 2FA Setup - This is where setup 2FA fails
  console.log('\n5️⃣  2FA SETUP');
  console.log('-'.repeat(40));
  const setup2FAResult = await apiCall('POST', '/2fa/setup', null, authToken, 'Setup 2FA and generate QR code');
  
  if (setup2FAResult.success) {
    twoFactorSecret = setup2FAResult.data.secret;
    console.log('🔐 2FA Secret obtained:', twoFactorSecret);
    console.log('📱 QR Code generated for authenticator apps');
  }

  // 6. 2FA Verification (Enable)
  if (twoFactorSecret) {
    console.log('\n6️⃣  2FA VERIFICATION (ENABLE)');
    console.log('-'.repeat(40));
    const token = speakeasy.totp({
      secret: twoFactorSecret,
      encoding: 'base32'
    });
    console.log('🔢 Generated TOTP token:', token);
    
    const verify2FAResult = await apiCall('POST', '/2fa/verify', { token }, authToken, 'Verify and enable 2FA');
  }

  // 7. 2FA Status Check
  console.log('\n7️⃣  2FA STATUS CHECK');
  console.log('-'.repeat(40));
  const status2FAResult = await apiCall('GET', '/2fa/status', null, authToken, 'Check 2FA status');

  // 8. 2FA Disable
  if (twoFactorSecret) {
    console.log('\n8️⃣  2FA DISABLE');
    console.log('-'.repeat(40));
    const token = speakeasy.totp({
      secret: twoFactorSecret,
      encoding: 'base32'
    });
    console.log('🔢 Generated TOTP token for disable:', token);
    
    const disable2FAResult = await apiCall('POST', '/2fa/disable', { token }, authToken, 'Disable 2FA');
  }

  // 9. Change Password
  console.log('\n9️⃣  CHANGE PASSWORD');
  console.log('-'.repeat(40));
  const newPassword = 'NewFullTest456!';
  const changePasswordResult = await apiCall('POST', '/auth/change-password', {
    currentPassword: testUser.password,
    newPassword: newPassword
  }, authToken, 'Change password');

  // 10. Forgot Password (Email)
  console.log('\n🔟 FORGOT PASSWORD (EMAIL)');
  console.log('-'.repeat(40));
  const forgotPasswordResult = await apiCall('POST', '/auth/forgot-password', {
    email: testUser.email
  }, null, 'Forgot password with email');

  // 11. Forgot Password (Phone)
  console.log('\n1️⃣1️⃣ FORGOT PASSWORD (PHONE)');
  console.log('-'.repeat(40));
  const forgotPasswordPhoneResult = await apiCall('POST', '/auth/forgot-password', {
    phone: testUser.phone
  }, null, 'Forgot password with phone');

  // 12. OAuth URLs
  console.log('\n1️⃣2️⃣ OAUTH URLS');
  console.log('-'.repeat(40));
  const providers = ['google', 'github', 'microsoft'];
  for (const provider of providers) {
    await apiCall('GET', `/auth/oauth/${provider}/url`, null, null, `Get ${provider.toUpperCase()} OAuth URL`);
  }

  // 13. Test OTP Login (if OTP was sent successfully)
  if (otpEmailResult.success) {
    console.log('\n1️⃣3️⃣ OTP LOGIN TEST');
    console.log('-'.repeat(40));
    console.log('ℹ️  In real scenario, user would enter OTP from email');
    console.log('ℹ️  For testing, we would need the actual OTP code from logs');
  }

  console.log('\n' + '=' .repeat(80));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(80));
  console.log('🎯 This test identifies exactly where each issue occurs');
  console.log('🔍 Check the detailed logs above for specific error messages');
  console.log('🛠️  Use this information to fix the exact problems');
  console.log('');
  console.log('🔧 Common Issues to Check:');
  console.log('   • Database connection and schema');
  console.log('   • Email service configuration (Brevo API)');
  console.log('   • JWT token generation and validation');
  console.log('   • Frontend-backend parameter mapping');
  console.log('   • OAuth client ID configuration');
  console.log('');
  console.log('📱 Next Steps:');
  console.log('   1. Fix any 500 errors shown above');
  console.log('   2. Verify email service is working');
  console.log('   3. Test frontend integration');
  console.log('   4. Setup real OAuth client IDs');
}

// Run the test
if (require.main === module) {
  testAllFunctionality().catch(console.error);
}

module.exports = { testAllFunctionality };
