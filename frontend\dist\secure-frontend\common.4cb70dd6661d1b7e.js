"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[76],{7097:(m,l,o)=>{o.d(l,{T:()=>E});var u=o(8810),n=o(9437),d=o(8141),s=o(5312),i=o(8564),g=o(2306),f=o(7715),_=o(4796);let E=(()=>{class e{constructor(t,r,a){this.http=t,this.router=r,this.authService=a,this.oauthProviders=[{name:"google",displayName:"Google",icon:"fab fa-google",color:"#db4437"},{name:"github",displayName:"GitHub",icon:"fab fa-github",color:"#333"},{name:"microsoft",displayName:"Microsoft",icon:"fab fa-microsoft",color:"#00a1f1"}]}getAvailableProviders(){return this.oauthProviders}getOAuthUrl(t){return this.http.get(`${s.c.apiUrl}/auth/oauth/${t}/url`).pipe((0,n.W)(this.handleError))}initiateOAuthLogin(t){this.getOAuthUrl(t).subscribe({next:r=>{sessionStorage.setItem("oauth_provider",t),sessionStorage.setItem("oauth_redirect",this.router.url),window.location.href=r.url},error:r=>{console.error(`Failed to get ${t} OAuth URL:`,r)}})}handleOAuthCallback(t,r){const a=sessionStorage.getItem("oauth_provider"),O=sessionStorage.getItem("oauth_redirect")||"/dashboard";if(!a)return(0,u.$)(()=>new Error("OAuth provider not found in session"));const h={code:t};return r&&(h.state=r),this.http.post(`${s.c.apiUrl}/auth/oauth/${a}/callback`,h).pipe((0,d.M)(c=>{c.token&&(sessionStorage.removeItem("oauth_provider"),sessionStorage.removeItem("oauth_redirect"),this.authService.setToken(c.token),this.router.navigate([O]))}),(0,n.W)(this.handleError))}isOAuthUser(t){return!(!t?.oauthProvider||!(t?.googleId||t?.githubId||t?.microsoftId))}getOAuthProviderName(t){return t?.googleId?"Google":t?.githubId?"GitHub":t?.microsoftId?"Microsoft":"Unknown"}getOAuthProviderIcon(t){return t?.googleId?"fab fa-google":t?.githubId?"fab fa-github":t?.microsoftId?"fab fa-microsoft":"fas fa-user"}getOAuthProviderColor(t){return t?.googleId?"#db4437":t?.githubId?"#333":t?.microsoftId?"#00a1f1":"#6c757d"}linkOAuthAccount(t){this.getOAuthUrl(t).subscribe({next:r=>{sessionStorage.setItem("oauth_action","link"),sessionStorage.setItem("oauth_provider",t),window.location.href=r.url},error:r=>{console.error(`Failed to link ${t} account:`,r)}})}unlinkOAuthAccount(t){const r=this.authService.getAuthHeaders();return this.http.delete(`${s.c.apiUrl}/auth/oauth/${t}/unlink`,{headers:r}).pipe((0,n.W)(this.handleError))}handleError(t){let r="OAuth authentication error occurred";return r=t.error instanceof ErrorEvent?t.error.message:t.error?.message||t.message||`Error Code: ${t.status}`,console.error("OAuth Service Error:",t),(0,u.$)(()=>new Error(r))}static#t=this.\u0275fac=function(r){return new(r||e)(i.KVO(g.Qq),i.KVO(f.Ix),i.KVO(_.u))};static#r=this.\u0275prov=i.jDH({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})()}}]);