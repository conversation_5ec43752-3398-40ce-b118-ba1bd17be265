{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, finalize } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/loading.service\";\nexport let AuthInterceptor = /*#__PURE__*/(() => {\n  class AuthInterceptor {\n    constructor(authService, router, loadingService) {\n      this.authService = authService;\n      this.router = router;\n      this.loadingService = loadingService;\n    }\n    intercept(request, next) {\n      // Show loading indicator\n      this.loadingService.show();\n      // Add auth token if available\n      const token = this.authService.getToken();\n      if (token && !this.authService.isTokenExpired()) {\n        request = request.clone({\n          setHeaders: {\n            Authorization: `Bearer ${token}`,\n            'Content-Type': 'application/json',\n            'X-Requested-With': 'XMLHttpRequest'\n          }\n        });\n      }\n      // Add CSRF protection\n      request = request.clone({\n        setHeaders: {\n          'X-CSRF-Token': this.generateCSRFToken()\n        }\n      });\n      return next.handle(request).pipe(catchError(error => {\n        this.handleError(error);\n        return throwError(() => error);\n      }), finalize(() => {\n        // Hide loading indicator\n        this.loadingService.hide();\n      }));\n    }\n    handleError(error) {\n      switch (error.status) {\n        case 401:\n          // Unauthorized - token expired or invalid\n          this.authService.logout();\n          this.router.navigate(['/auth/login'], {\n            queryParams: {\n              message: 'Session expired. Please login again.'\n            }\n          });\n          break;\n        case 403:\n          // Forbidden - insufficient permissions\n          this.router.navigate(['/unauthorized']);\n          break;\n        case 429:\n          // Too many requests - rate limited\n          console.warn('Rate limit exceeded. Please try again later.');\n          break;\n        case 0:\n          // Network error\n          console.error('Network error. Please check your connection.');\n          break;\n        default:\n          console.error('HTTP Error:', error);\n      }\n    }\n    generateCSRFToken() {\n      // Simple CSRF token generation\n      return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    }\n    static #_ = this.ɵfac = function AuthInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.LoadingService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n  return AuthInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}