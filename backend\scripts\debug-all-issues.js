const axios = require('axios');
const speakeasy = require('speakeasy');

const BASE_URL = 'http://localhost:3002';
let authToken = '';
let twoFactorSecret = '';

// Test user data with timestamp
const timestamp = Date.now();
const testUser = {
  email: `debug-${timestamp}@example.com`,
  password: 'DebugTest123!',
  firstName: 'Debug',
  lastName: 'Test',
  phone: `+123456${timestamp.toString().slice(-4)}`
};

// Helper function to make API calls with detailed logging
async function apiCall(method, endpoint, data = null, token = null, description = '') {
  console.log(`\n🔄 ${description || `${method} ${endpoint}`}`);
  console.log(`📤 Request: ${method} ${BASE_URL}${endpoint}`);
  if (data) console.log(`📦 Body:`, JSON.stringify(data, null, 2));
  if (token) console.log(`🔑 Token: ${token.substring(0, 30)}...`);

  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    console.log(`✅ Success: ${response.status} ${response.statusText}`);
    console.log(`📥 Response:`, JSON.stringify(response.data, null, 2));
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    console.log(`❌ Error: ${error.response?.status || 'Network'} ${error.response?.statusText || error.message}`);
    console.log(`📥 Error Response:`, JSON.stringify(error.response?.data || error.message, null, 2));
    console.log(`🔍 Full Error:`, error.message);
    if (error.response?.data?.error) {
      console.log(`🔍 Backend Error:`, error.response.data.error);
    }
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
}

async function debugAllIssues() {
  console.log('🐛 DEBUGGING ALL ISSUES - COMPREHENSIVE TEST');
  console.log('=' .repeat(80));
  console.log('👤 Test User:', testUser.email);
  console.log('📱 Test Phone:', testUser.phone);
  console.log('🎯 Goal: Identify exact cause of 500 errors and fix all issues');
  console.log('=' .repeat(80));

  // 1. Health Check
  console.log('\n🏥 HEALTH CHECK');
  console.log('-'.repeat(40));
  const healthResult = await apiCall('GET', '/ping', null, null, 'Backend health check');
  if (!healthResult.success) {
    console.log('🛑 Backend is not responding. Cannot continue.');
    return;
  }

  // 2. User Registration
  console.log('\n1️⃣  USER REGISTRATION');
  console.log('-'.repeat(40));
  const signupResult = await apiCall('POST', '/auth/signup', testUser, null, 'Register new user');
  if (!signupResult.success && signupResult.status !== 409) {
    console.log('🛑 Registration failed. Cannot continue.');
    return;
  }

  // 3. User Login
  console.log('\n2️⃣  USER LOGIN');
  console.log('-'.repeat(40));
  const loginResult = await apiCall('POST', '/auth/login', {
    email: testUser.email,
    password: testUser.password
  }, null, 'Login with email and password');
  
  if (loginResult.success) {
    authToken = loginResult.data.token;
    console.log('🎯 Auth token obtained successfully');
  } else {
    console.log('🛑 Login failed. Cannot test authenticated endpoints.');
    return;
  }

  // 4. Test OTP Send (Email) - This is where the 500 error occurs
  console.log('\n3️⃣  OTP SEND (EMAIL) - DEBUGGING 500 ERROR');
  console.log('-'.repeat(40));
  console.log('🔍 This is where the frontend gets 500 error');
  const otpEmailResult = await apiCall('POST', '/otp/send', {
    email: testUser.email,
    type: 'login'
  }, null, 'Send OTP to email for login - EXACT FRONTEND CALL');

  // 5. Test alternative OTP endpoint
  console.log('\n4️⃣  ALTERNATIVE OTP ENDPOINT TEST');
  console.log('-'.repeat(40));
  const otpEmailAltResult = await apiCall('POST', '/otp/send-email', {
    email: testUser.email,
    type: 'login'
  }, null, 'Test alternative OTP endpoint');

  // 6. 2FA Setup - This is where setup 2FA fails
  console.log('\n5️⃣  2FA SETUP - DEBUGGING FRONTEND ISSUE');
  console.log('-'.repeat(40));
  console.log('🔍 This is where frontend "does nothing"');
  const setup2FAResult = await apiCall('POST', '/2fa/setup', {}, authToken, 'Setup 2FA - EXACT FRONTEND CALL');
  
  if (setup2FAResult.success) {
    twoFactorSecret = setup2FAResult.data.secret;
    console.log('🔐 2FA Secret obtained:', twoFactorSecret);
    console.log('📱 QR Code data:', setup2FAResult.data.qrCode ? 'Generated' : 'Missing');
  }

  // 7. 2FA Status Check
  console.log('\n6️⃣  2FA STATUS CHECK');
  console.log('-'.repeat(40));
  const status2FAResult = await apiCall('GET', '/2fa/status', null, authToken, 'Check 2FA status');

  // 8. Test Forgot Password (Email)
  console.log('\n7️⃣  FORGOT PASSWORD (EMAIL)');
  console.log('-'.repeat(40));
  const forgotPasswordResult = await apiCall('POST', '/auth/forgot-password', {
    email: testUser.email
  }, null, 'Forgot password with email');

  // 9. Test Forgot Password (Phone)
  console.log('\n8️⃣  FORGOT PASSWORD (PHONE)');
  console.log('-'.repeat(40));
  const forgotPasswordPhoneResult = await apiCall('POST', '/auth/forgot-password', {
    phone: testUser.phone
  }, null, 'Forgot password with phone');

  // 10. OAuth URLs Test
  console.log('\n9️⃣  OAUTH URLS TEST');
  console.log('-'.repeat(40));
  const providers = ['google', 'github', 'microsoft'];
  for (const provider of providers) {
    await apiCall('GET', `/auth/oauth/${provider}/url`, null, null, `Get ${provider.toUpperCase()} OAuth URL`);
  }

  // 11. Test Change Password
  console.log('\n🔟 CHANGE PASSWORD TEST');
  console.log('-'.repeat(40));
  const changePasswordResult = await apiCall('POST', '/auth/change-password', {
    currentPassword: testUser.password,
    newPassword: 'NewDebugTest456!'
  }, authToken, 'Change password without 2FA');

  console.log('\n' + '=' .repeat(80));
  console.log('🔍 DEBUGGING SUMMARY');
  console.log('=' .repeat(80));
  console.log('🎯 Key Findings:');
  console.log('');
  
  if (!otpEmailResult.success) {
    console.log('❌ OTP Send Error (500):');
    console.log('   • This is the exact error frontend gets');
    console.log('   • Check backend logs above for specific cause');
    console.log('   • Likely database or email service issue');
  } else {
    console.log('✅ OTP Send: Working');
  }

  if (!setup2FAResult.success) {
    console.log('❌ 2FA Setup Error:');
    console.log('   • This is why frontend "does nothing"');
    console.log('   • Check authentication or database issues');
  } else {
    console.log('✅ 2FA Setup: Working');
  }

  console.log('');
  console.log('🛠️  Next Steps:');
  console.log('   1. Fix any 500 errors identified above');
  console.log('   2. Check database connection and schema');
  console.log('   3. Verify email service configuration');
  console.log('   4. Test frontend integration after backend fixes');
  console.log('');
  console.log('📊 Test completed. Use findings above to fix issues.');
}

// Run the debug test
if (require.main === module) {
  debugAllIssues().catch(console.error);
}

module.exports = { debugAllIssues };
