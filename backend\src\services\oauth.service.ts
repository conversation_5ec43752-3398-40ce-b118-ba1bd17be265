import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {OAuth2Client} from 'google-auth-library';
import axios from 'axios';
import {UserRepository} from '../repositories';
import {User} from '../models';

@injectable({scope: BindingScope.TRANSIENT})
export class OAuthService {
  private googleClient: OAuth2Client;

  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
  ) {
    this.googleClient = new OAuth2Client(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.OAUTH_REDIRECT_URL
    );
  }

  async verifyGoogleToken(token: string): Promise<any> {
    try {
      const ticket = await this.googleClient.verifyIdToken({
        idToken: token,
        audience: process.env.GOOGLE_CLIENT_ID,
      });
      
      const payload = ticket.getPayload();
      if (!payload) {
        throw new HttpErrors.BadRequest('Invalid Google token');
      }

      return {
        id: payload.sub,
        email: payload.email,
        firstName: payload.given_name,
        lastName: payload.family_name,
        avatarUrl: payload.picture,
        emailVerified: payload.email_verified
      };
    } catch (error) {
      console.error('Google token verification failed:', error);
      throw new HttpErrors.BadRequest('Invalid Google token');
    }
  }

  async verifyGitHubToken(accessToken: string): Promise<any> {
    try {
      // Get user info from GitHub
      const userResponse = await axios.get('https://api.github.com/user', {
        headers: {
          'Authorization': `token ${accessToken}`,
          'User-Agent': 'SecureApp'
        }
      });

      // Get user email (might be private)
      const emailResponse = await axios.get('https://api.github.com/user/emails', {
        headers: {
          'Authorization': `token ${accessToken}`,
          'User-Agent': 'SecureApp'
        }
      });

      const primaryEmail = emailResponse.data.find((email: any) => email.primary);
      
      return {
        id: userResponse.data.id.toString(),
        email: primaryEmail?.email || userResponse.data.email,
        firstName: userResponse.data.name?.split(' ')[0] || userResponse.data.login,
        lastName: userResponse.data.name?.split(' ').slice(1).join(' ') || '',
        avatarUrl: userResponse.data.avatar_url,
        emailVerified: primaryEmail?.verified || false
      };
    } catch (error) {
      console.error('GitHub token verification failed:', error);
      throw new HttpErrors.BadRequest('Invalid GitHub token');
    }
  }

  async verifyMicrosoftToken(accessToken: string): Promise<any> {
    try {
      const response = await axios.get('https://graph.microsoft.com/v1.0/me', {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      return {
        id: response.data.id,
        email: response.data.mail || response.data.userPrincipalName,
        firstName: response.data.givenName,
        lastName: response.data.surname,
        avatarUrl: null, // Microsoft Graph requires separate call for photo
        emailVerified: true // Microsoft accounts are typically verified
      };
    } catch (error) {
      console.error('Microsoft token verification failed:', error);
      throw new HttpErrors.BadRequest('Invalid Microsoft token');
    }
  }

  async findOrCreateOAuthUser(provider: string, oauthData: any): Promise<User> {
    let user: User | null = null;

    // First, try to find user by OAuth ID
    const oauthIdField = `${provider}Id`;
    user = await this.userRepository.findOne({
      where: {[oauthIdField]: oauthData.id}
    });

    if (user) {
      // Update user info if found
      await this.userRepository.updateById(user.id, {
        firstName: oauthData.firstName,
        lastName: oauthData.lastName,
        avatarUrl: oauthData.avatarUrl,
        updatedAt: new Date()
      });
      return user;
    }

    // If not found by OAuth ID, try to find by email
    if (oauthData.email) {
      user = await this.userRepository.findOne({
        where: {email: oauthData.email}
      });

      if (user) {
        // Link OAuth account to existing user
        await this.userRepository.updateById(user.id, {
          [oauthIdField]: oauthData.id,
          oauthProvider: provider,
          avatarUrl: oauthData.avatarUrl || user.avatarUrl,
          emailVerified: oauthData.emailVerified || user.emailVerified,
          updatedAt: new Date()
        });
        return user;
      }
    }

    // Create new user
    const newUser = await this.userRepository.create({
      email: oauthData.email,
      firstName: oauthData.firstName,
      lastName: oauthData.lastName,
      [oauthIdField]: oauthData.id,
      oauthProvider: provider,
      avatarUrl: oauthData.avatarUrl,
      emailVerified: oauthData.emailVerified || true,
      roles: ['user'],
      createdAt: new Date(),
      updatedAt: new Date()
    });

    return newUser;
  }

  generateOAuthUrl(provider: string, state?: string): string {
    const baseUrl = process.env.OAUTH_REDIRECT_URL || 'http://localhost:3002/auth/oauth/callback';
    
    switch (provider) {
      case 'google':
        const googleUrl = this.googleClient.generateAuthUrl({
          access_type: 'offline',
          scope: ['profile', 'email'],
          state: state ? `google:${state}` : 'google'
        });
        return googleUrl;

      case 'github':
        const githubUrl = `https://github.com/login/oauth/authorize?` +
          `client_id=${process.env.GITHUB_CLIENT_ID}&` +
          `redirect_uri=${encodeURIComponent(baseUrl)}&` +
          `scope=user:email&` +
          `state=${state ? `github:${state}` : 'github'}`;
        return githubUrl;

      case 'microsoft':
        const microsoftUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?` +
          `client_id=${process.env.MICROSOFT_CLIENT_ID}&` +
          `response_type=code&` +
          `redirect_uri=${encodeURIComponent(baseUrl)}&` +
          `scope=User.Read&` +
          `state=${state ? `microsoft:${state}` : 'microsoft'}`;
        return microsoftUrl;

      default:
        throw new HttpErrors.BadRequest('Unsupported OAuth provider');
    }
  }

  async exchangeCodeForToken(provider: string, code: string): Promise<string> {
    switch (provider) {
      case 'google':
        const {tokens} = await this.googleClient.getToken(code);
        return tokens.access_token!;

      case 'github':
        const githubResponse = await axios.post('https://github.com/login/oauth/access_token', {
          client_id: process.env.GITHUB_CLIENT_ID,
          client_secret: process.env.GITHUB_CLIENT_SECRET,
          code
        }, {
          headers: {
            'Accept': 'application/json'
          }
        });
        return githubResponse.data.access_token;

      case 'microsoft':
        const microsoftResponse = await axios.post('https://login.microsoftonline.com/common/oauth2/v2.0/token', {
          client_id: process.env.MICROSOFT_CLIENT_ID,
          client_secret: process.env.MICROSOFT_CLIENT_SECRET,
          code,
          grant_type: 'authorization_code',
          redirect_uri: process.env.OAUTH_REDIRECT_URL
        }, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });
        return microsoftResponse.data.access_token;

      default:
        throw new HttpErrors.BadRequest('Unsupported OAuth provider');
    }
  }
}
