{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * Current injector value used by `inject`.\n * - `undefined`: it is an error to call `inject`\n * - `null`: `inject` can be called but there is no injector (limp-mode).\n * - Injector instance: Use the injector for resolution.\n */\nlet _currentInjector = undefined;\nfunction getCurrentInjector() {\n  return _currentInjector;\n}\nfunction setCurrentInjector(injector) {\n  const former = _currentInjector;\n  _currentInjector = injector;\n  return former;\n}\n\n/**\n * Value returned if the key-value pair couldn't be found in the context\n * hierarchy.\n */\nconst NOT_FOUND = /*#__PURE__*/Symbol('NotFound');\n/**\n * Error thrown when the key-value pair couldn't be found in the context\n * hierarchy. Context can be attached below.\n */\nclass NotFoundError extends Error {\n  name = 'ɵNotFound';\n  constructor(message) {\n    super(message);\n  }\n}\n/**\n * Type guard for checking if an unknown value is a NotFound.\n */\nfunction isNotFound(e) {\n  return e === NOT_FOUND || e?.name === 'ɵNotFound';\n}\nexport { NOT_FOUND, NotFoundError, getCurrentInjector, isNotFound, setCurrentInjector };\n//# sourceMappingURL=di.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}