import { UserRepository } from '../repositories';
import { SecurityService, EmailService, SmsService } from '../services';
export declare class OtpController {
    protected userRepository: UserRepository;
    securityService: SecurityService;
    emailService: EmailService;
    smsService: SmsService;
    constructor(userRepository: UserRepository, securityService: SecurityService, emailService: EmailService, smsService: SmsService);
    sendOTP(request: {
        email?: string;
        phone?: string;
        type: string;
    }): Promise<{
        message: string;
    }>;
    sendEmailOTP(request: {
        email: string;
        type: string;
    }): Promise<{
        message: string;
    }>;
    sendSMSOTP(request: {
        phone: string;
        type: string;
    }): Promise<{
        message: string;
    }>;
    verifyOTP(request: {
        identifier: string;
        code: string;
        type: string;
    }): Promise<{
        valid: boolean;
        message: string;
    }>;
    loginWithOTP(request: {
        identifier: string;
        code: string;
    }): Promise<{
        token: string;
        user: any;
    }>;
}
