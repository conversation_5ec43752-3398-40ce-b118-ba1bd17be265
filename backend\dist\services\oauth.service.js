"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OAuthService = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const google_auth_library_1 = require("google-auth-library");
const axios_1 = tslib_1.__importDefault(require("axios"));
const repositories_1 = require("../repositories");
let OAuthService = class OAuthService {
    constructor(userRepository) {
        this.userRepository = userRepository;
        this.googleClient = new google_auth_library_1.OAuth2Client(process.env.GOOGLE_CLIENT_ID, process.env.GOOGLE_CLIENT_SECRET, process.env.OAUTH_REDIRECT_URL);
    }
    async verifyGoogleToken(token) {
        try {
            const ticket = await this.googleClient.verifyIdToken({
                idToken: token,
                audience: process.env.GOOGLE_CLIENT_ID,
            });
            const payload = ticket.getPayload();
            if (!payload) {
                throw new rest_1.HttpErrors.BadRequest('Invalid Google token');
            }
            return {
                id: payload.sub,
                email: payload.email,
                firstName: payload.given_name,
                lastName: payload.family_name,
                avatarUrl: payload.picture,
                emailVerified: payload.email_verified
            };
        }
        catch (error) {
            console.error('Google token verification failed:', error);
            throw new rest_1.HttpErrors.BadRequest('Invalid Google token');
        }
    }
    async verifyGitHubToken(accessToken) {
        try {
            // Get user info from GitHub
            const userResponse = await axios_1.default.get('https://api.github.com/user', {
                headers: {
                    'Authorization': `token ${accessToken}`,
                    'User-Agent': 'SecureApp'
                }
            });
            // Get user email (might be private)
            const emailResponse = await axios_1.default.get('https://api.github.com/user/emails', {
                headers: {
                    'Authorization': `token ${accessToken}`,
                    'User-Agent': 'SecureApp'
                }
            });
            const primaryEmail = emailResponse.data.find((email) => email.primary);
            return {
                id: userResponse.data.id.toString(),
                email: primaryEmail?.email || userResponse.data.email,
                firstName: userResponse.data.name?.split(' ')[0] || userResponse.data.login,
                lastName: userResponse.data.name?.split(' ').slice(1).join(' ') || '',
                avatarUrl: userResponse.data.avatar_url,
                emailVerified: primaryEmail?.verified || false
            };
        }
        catch (error) {
            console.error('GitHub token verification failed:', error);
            throw new rest_1.HttpErrors.BadRequest('Invalid GitHub token');
        }
    }
    async verifyMicrosoftToken(accessToken) {
        try {
            const response = await axios_1.default.get('https://graph.microsoft.com/v1.0/me', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });
            return {
                id: response.data.id,
                email: response.data.mail || response.data.userPrincipalName,
                firstName: response.data.givenName,
                lastName: response.data.surname,
                avatarUrl: null, // Microsoft Graph requires separate call for photo
                emailVerified: true // Microsoft accounts are typically verified
            };
        }
        catch (error) {
            console.error('Microsoft token verification failed:', error);
            throw new rest_1.HttpErrors.BadRequest('Invalid Microsoft token');
        }
    }
    async findOrCreateOAuthUser(provider, oauthData) {
        let user = null;
        // First, try to find user by OAuth ID
        const oauthIdField = `${provider}Id`;
        user = await this.userRepository.findOne({
            where: { [oauthIdField]: oauthData.id }
        });
        if (user) {
            // Update user info if found
            await this.userRepository.updateById(user.id, {
                firstName: oauthData.firstName,
                lastName: oauthData.lastName,
                avatarUrl: oauthData.avatarUrl,
                updatedAt: new Date()
            });
            return user;
        }
        // If not found by OAuth ID, try to find by email
        if (oauthData.email) {
            user = await this.userRepository.findOne({
                where: { email: oauthData.email }
            });
            if (user) {
                // Link OAuth account to existing user
                await this.userRepository.updateById(user.id, {
                    [oauthIdField]: oauthData.id,
                    oauthProvider: provider,
                    avatarUrl: oauthData.avatarUrl || user.avatarUrl,
                    emailVerified: oauthData.emailVerified || user.emailVerified,
                    updatedAt: new Date()
                });
                return user;
            }
        }
        // Create new user
        const newUser = await this.userRepository.create({
            email: oauthData.email,
            firstName: oauthData.firstName,
            lastName: oauthData.lastName,
            [oauthIdField]: oauthData.id,
            oauthProvider: provider,
            avatarUrl: oauthData.avatarUrl,
            emailVerified: oauthData.emailVerified || true,
            roles: ['user'],
            createdAt: new Date(),
            updatedAt: new Date()
        });
        return newUser;
    }
    generateOAuthUrl(provider, state) {
        const baseUrl = process.env.OAUTH_REDIRECT_URL || 'http://localhost:3002/auth/oauth/callback';
        switch (provider) {
            case 'google':
                const googleUrl = this.googleClient.generateAuthUrl({
                    access_type: 'offline',
                    scope: ['profile', 'email'],
                    state: state ? `google:${state}` : 'google'
                });
                return googleUrl;
            case 'github':
                const githubUrl = `https://github.com/login/oauth/authorize?` +
                    `client_id=${process.env.GITHUB_CLIENT_ID}&` +
                    `redirect_uri=${encodeURIComponent(baseUrl)}&` +
                    `scope=user:email&` +
                    `state=${state ? `github:${state}` : 'github'}`;
                return githubUrl;
            case 'microsoft':
                const microsoftUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?` +
                    `client_id=${process.env.MICROSOFT_CLIENT_ID}&` +
                    `response_type=code&` +
                    `redirect_uri=${encodeURIComponent(baseUrl)}&` +
                    `scope=User.Read&` +
                    `state=${state ? `microsoft:${state}` : 'microsoft'}`;
                return microsoftUrl;
            default:
                throw new rest_1.HttpErrors.BadRequest('Unsupported OAuth provider');
        }
    }
    async exchangeCodeForToken(provider, code) {
        switch (provider) {
            case 'google':
                const { tokens } = await this.googleClient.getToken(code);
                return tokens.access_token;
            case 'github':
                const githubResponse = await axios_1.default.post('https://github.com/login/oauth/access_token', {
                    client_id: process.env.GITHUB_CLIENT_ID,
                    client_secret: process.env.GITHUB_CLIENT_SECRET,
                    code
                }, {
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                return githubResponse.data.access_token;
            case 'microsoft':
                const microsoftResponse = await axios_1.default.post('https://login.microsoftonline.com/common/oauth2/v2.0/token', {
                    client_id: process.env.MICROSOFT_CLIENT_ID,
                    client_secret: process.env.MICROSOFT_CLIENT_SECRET,
                    code,
                    grant_type: 'authorization_code',
                    redirect_uri: process.env.OAUTH_REDIRECT_URL
                }, {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                });
                return microsoftResponse.data.access_token;
            default:
                throw new rest_1.HttpErrors.BadRequest('Unsupported OAuth provider');
        }
    }
};
exports.OAuthService = OAuthService;
exports.OAuthService = OAuthService = tslib_1.__decorate([
    (0, core_1.injectable)({ scope: core_1.BindingScope.TRANSIENT }),
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__metadata("design:paramtypes", [repositories_1.UserRepository])
], OAuthService);
//# sourceMappingURL=oauth.service.js.map