{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/two-factor.service\";\nimport * as i3 from \"../../services/oauth.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/form-field\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nfunction ProfileComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"img\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatarUrl, i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r0.currentUser.firstName || \"\") + \" \" + (ctx_r0.currentUser.lastName || \"\"));\n  }\n}\nfunction ProfileComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Connected via:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 19);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMap(ctx_r0.getOAuthProviderIcon());\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getOAuthProviderColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getOAuthProviderName(), \" \");\n  }\n}\nfunction ProfileComponent_button_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_button_58_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleChangePassword());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.showChangePassword ? \"expand_less\" : \"expand_more\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.showChangePassword ? \"Cancel\" : \"Change Password\", \" \");\n  }\n}\nfunction ProfileComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"mat-icon\", 22);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Password managed by \", ctx_r0.getOAuthProviderName());\n  }\n}\nfunction ProfileComponent_div_60_mat_form_field_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 25)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"2FA Code (Required)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 36);\n    i0.ɵɵelementStart(4, \"mat-icon\", 37);\n    i0.ɵɵtext(5, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-hint\");\n    i0.ɵɵtext(7, \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-error\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"twoFactorToken\"));\n  }\n}\nfunction ProfileComponent_div_60_mat_spinner_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 38);\n  }\n}\nfunction ProfileComponent_div_60_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Change Password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"form\", 24);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_60_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onChangePassword());\n    });\n    i0.ɵɵelementStart(2, \"mat-form-field\", 25)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Current Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 26);\n    i0.ɵɵelementStart(6, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_60_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideCurrentPassword = !ctx_r0.hideCurrentPassword);\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-error\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 25)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 28);\n    i0.ɵɵelementStart(15, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_60_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideNewPassword = !ctx_r0.hideNewPassword);\n    });\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-error\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"mat-form-field\", 25)(21, \"mat-label\");\n    i0.ɵɵtext(22, \"Confirm New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"input\", 29);\n    i0.ɵɵelementStart(24, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_60_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideConfirmPassword = !ctx_r0.hideConfirmPassword);\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"mat-error\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, ProfileComponent_div_60_mat_form_field_29_Template, 10, 1, \"mat-form-field\", 30);\n    i0.ɵɵelementStart(30, \"div\", 31)(31, \"button\", 32);\n    i0.ɵɵtemplate(32, ProfileComponent_div_60_mat_spinner_32_Template, 1, 0, \"mat-spinner\", 33)(33, ProfileComponent_div_60_span_33_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_60_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleChangePassword());\n    });\n    i0.ɵɵtext(35, \" Cancel \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.changePasswordForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideCurrentPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideCurrentPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"currentPassword\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideNewPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideNewPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"newPassword\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"confirmPassword\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.twoFactorStatus.enabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading);\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, twoFactorService, oauthService, formBuilder, snackBar) {\n    this.authService = authService;\n    this.twoFactorService = twoFactorService;\n    this.oauthService = oauthService;\n    this.formBuilder = formBuilder;\n    this.snackBar = snackBar;\n    this.currentUser = null;\n    this.twoFactorStatus = {\n      enabled: false\n    };\n    this.loading = false;\n    this.hideCurrentPassword = true;\n    this.hideNewPassword = true;\n    this.hideConfirmPassword = true;\n    this.showChangePassword = false;\n    this.changePasswordForm = this.formBuilder.group({\n      currentPassword: ['', [Validators.required]],\n      newPassword: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      twoFactorToken: ['']\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  ngOnInit() {\n    this.currentUser = this.authService.currentUserValue;\n    this.load2FAStatus();\n  }\n  load2FAStatus() {\n    this.twoFactorService.get2FAStatus().subscribe({\n      next: status => {\n        this.twoFactorStatus = status;\n      },\n      error: error => {\n        console.error('Failed to load 2FA status:', error);\n      }\n    });\n  }\n  toggleChangePassword() {\n    this.showChangePassword = !this.showChangePassword;\n    if (!this.showChangePassword) {\n      this.changePasswordForm.reset();\n    }\n  }\n  onChangePassword() {\n    if (this.changePasswordForm.invalid) {\n      this.markFormGroupTouched(this.changePasswordForm);\n      return;\n    }\n    this.loading = true;\n    const formValue = this.changePasswordForm.value;\n    this.authService.changePassword(formValue.currentPassword, formValue.newPassword, formValue.twoFactorToken || undefined).subscribe({\n      next: response => {\n        this.snackBar.open('Password changed successfully!', 'Close', {\n          duration: 3000\n        });\n        this.changePasswordForm.reset();\n        this.showChangePassword = false;\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Failed to change password', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  isOAuthUser() {\n    return this.oauthService.isOAuthUser(this.currentUser);\n  }\n  getOAuthProviderName() {\n    return this.oauthService.getOAuthProviderName(this.currentUser);\n  }\n  getOAuthProviderIcon() {\n    return this.oauthService.getOAuthProviderIcon(this.currentUser);\n  }\n  getOAuthProviderColor() {\n    return this.oauthService.getOAuthProviderColor(this.currentUser);\n  }\n  getFieldError(fieldName) {\n    const field = this.changePasswordForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n    }\n    return '';\n  }\n  passwordMatchValidator(form) {\n    const newPassword = form.get('newPassword');\n    const confirmPassword = form.get('confirmPassword');\n    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n    } else {\n      confirmPassword?.setErrors(null);\n    }\n    return null;\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  static #_ = this.ɵfac = function ProfileComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProfileComponent,\n    selectors: [[\"app-profile\"]],\n    standalone: false,\n    decls: 66,\n    vars: 25,\n    consts: [[1, \"profile-container\"], [1, \"container\"], [1, \"user-info\"], [\"class\", \"user-avatar\", 4, \"ngIf\"], [1, \"user-details\"], [\"class\", \"oauth-info\", 4, \"ngIf\"], [1, \"account-status\"], [1, \"status-chips\"], [1, \"status-chip\"], [\"mat-button\", \"\", \"color\", \"primary\"], [1, \"security-section\"], [1, \"section-header\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"oauth-password-notice\", 4, \"ngIf\"], [\"class\", \"change-password-form\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/two-factor\"], [1, \"user-avatar\"], [3, \"src\", \"alt\"], [1, \"oauth-info\"], [1, \"oauth-provider\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"oauth-password-notice\"], [\"color\", \"warn\"], [1, \"change-password-form\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"currentPassword\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"newPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"class\", \"form-field\", \"appearance\", \"outline\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"matSuffix\", \"\"], [\"diameter\", \"20\"]],\n    template: function ProfileComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n        i0.ɵɵtext(3, \"Profile & Security Settings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"mat-card\")(5, \"mat-card-header\")(6, \"mat-card-title\");\n        i0.ɵɵtext(7, \"User Information\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 2);\n        i0.ɵɵtemplate(10, ProfileComponent_div_10_Template, 2, 2, \"div\", 3);\n        i0.ɵɵelementStart(11, \"div\", 4)(12, \"p\")(13, \"strong\");\n        i0.ɵɵtext(14, \"Name:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"p\")(17, \"strong\");\n        i0.ɵɵtext(18, \"Email:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"p\")(21, \"strong\");\n        i0.ɵɵtext(22, \"Phone:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"p\")(25, \"strong\");\n        i0.ɵɵtext(26, \"Member since:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(27);\n        i0.ɵɵpipe(28, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(29, ProfileComponent_div_29_Template, 7, 5, \"div\", 5);\n        i0.ɵɵelementStart(30, \"div\", 6)(31, \"div\", 7)(32, \"div\", 8)(33, \"mat-icon\");\n        i0.ɵɵtext(34);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"span\");\n        i0.ɵɵtext(36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(37, \"div\", 8)(38, \"mat-icon\");\n        i0.ɵɵtext(39, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"span\");\n        i0.ɵɵtext(41);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(42, \"mat-card-actions\")(43, \"button\", 9)(44, \"mat-icon\");\n        i0.ɵɵtext(45, \"edit\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(46, \" Edit Profile \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(47, \"mat-card\")(48, \"mat-card-header\")(49, \"mat-card-title\");\n        i0.ɵɵtext(50, \"Security Settings\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(51, \"mat-card-content\")(52, \"p\");\n        i0.ɵɵtext(53, \"Manage your account security settings including two-factor authentication and password.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"div\", 10)(55, \"div\", 11)(56, \"h3\");\n        i0.ɵɵtext(57, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(58, ProfileComponent_button_58_Template, 4, 2, \"button\", 12)(59, ProfileComponent_div_59_Template, 5, 1, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(60, ProfileComponent_div_60_Template, 36, 14, \"div\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(61, \"mat-card-actions\")(62, \"button\", 15)(63, \"mat-icon\");\n        i0.ɵɵtext(64, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(65);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentUser == null ? null : ctx.currentUser.avatarUrl);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate2(\" \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" \", ctx.currentUser == null ? null : ctx.currentUser.lastName);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.email);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.phone) || \"Not provided\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(28, 22, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"mediumDate\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"verified\", ctx.currentUser == null ? null : ctx.currentUser.emailVerified)(\"unverified\", !(ctx.currentUser == null ? null : ctx.currentUser.emailVerified));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"warning\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"Email \", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"Verified\" : \"Unverified\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"enabled\", ctx.twoFactorStatus.enabled)(\"disabled\", !ctx.twoFactorStatus.enabled);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\"2FA \", ctx.twoFactorStatus.enabled ? \"Enabled\" : \"Disabled\");\n        i0.ɵɵadvance(17);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isOAuthUser());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showChangePassword && !ctx.isOAuthUser());\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\" \", ctx.twoFactorStatus.enabled ? \"Manage 2FA\" : \"Setup 2FA\", \" \");\n      }\n    },\n    dependencies: [i6.NgIf, i7.RouterLink, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.MaxLengthValidator, i4.FormGroupDirective, i4.FormControlName, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardTitle, i9.MatButton, i9.MatIconButton, i10.MatIcon, i11.MatFormField, i11.MatLabel, i11.MatHint, i11.MatError, i11.MatSuffix, i12.MatInput, i13.MatProgressSpinner, i6.DatePipe],\n    styles: [\".profile-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: #f5f5f5;\\n  padding: 2rem;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-bottom: 2rem;\\n}\\n\\nmat-card[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1.5rem;\\n  align-items: flex-start;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #e0e0e0;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin-right: 0.5rem;\\n}\\n\\n.oauth-info[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.25rem 0.75rem;\\n  background: #f5f5f5;\\n  border-radius: 20px;\\n  font-size: 0.875rem;\\n}\\n.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n\\n.account-status[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chips[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  flex-wrap: wrap;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  width: 16px;\\n  height: 16px;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.verified[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  color: #2e7d32;\\n  border: 1px solid #4caf50;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.unverified[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  color: #f57c00;\\n  border: 1px solid #ff9800;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.enabled[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  color: #1976d2;\\n  border: 1px solid #2196f3;\\n}\\n.account-status[_ngcontent-%COMP%]   .status-chip.disabled[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  color: #666;\\n  border: 1px solid #ccc;\\n}\\n\\n.security-section[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  color: #666;\\n  font-size: 0.875rem;\\n}\\n.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.change-password-form[_ngcontent-%COMP%] {\\n  background: #f9f9f9;\\n  padding: 1.5rem;\\n  border-radius: 8px;\\n  border: 1px solid #e0e0e0;\\n}\\n.change-password-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 1rem;\\n}\\n.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-top: 1.5rem;\\n}\\n.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "currentUser", "avatarUrl", "ɵɵsanitizeUrl", "firstName", "lastName", "ɵɵtext", "ɵɵclassMap", "getOAuthProviderIcon", "ɵɵstyleProp", "getOAuthProviderColor", "ɵɵtextInterpolate1", "getOAuthProviderName", "ɵɵlistener", "ProfileComponent_button_58_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "toggleChangePassword", "ɵɵtextInterpolate", "showChangePassword", "getFieldError", "ProfileComponent_div_60_Template_form_ngSubmit_1_listener", "_r3", "onChangePassword", "ProfileComponent_div_60_Template_button_click_6_listener", "hideCurrentPassword", "ProfileComponent_div_60_Template_button_click_15_listener", "hideNewPassword", "ProfileComponent_div_60_Template_button_click_24_listener", "hideConfirmPassword", "ɵɵtemplate", "ProfileComponent_div_60_mat_form_field_29_Template", "ProfileComponent_div_60_mat_spinner_32_Template", "ProfileComponent_div_60_span_33_Template", "ProfileComponent_div_60_Template_button_click_34_listener", "changePasswordForm", "twoFactorStatus", "enabled", "loading", "ProfileComponent", "constructor", "authService", "twoFactorService", "oauthService", "formBuilder", "snackBar", "group", "currentPassword", "required", "newPassword", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "twoFactorToken", "validators", "passwordMatchValidator", "ngOnInit", "currentUserValue", "load2FAStatus", "get2FAStatus", "subscribe", "next", "status", "error", "console", "reset", "invalid", "markFormGroupTouched", "formValue", "value", "changePassword", "undefined", "response", "open", "duration", "message", "isOAuthUser", "fieldName", "field", "get", "errors", "touched", "<PERSON><PERSON><PERSON><PERSON>", "form", "setErrors", "passwordMismatch", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "TwoFactorService", "i3", "OAuthService", "i4", "FormBuilder", "i5", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_10_Template", "ProfileComponent_div_29_Template", "ProfileComponent_button_58_Template", "ProfileComponent_div_59_Template", "ProfileComponent_div_60_Template", "ɵɵtextInterpolate2", "email", "phone", "ɵɵpipeBind2", "createdAt", "ɵɵclassProp", "emailVerified"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\modules\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\modules\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../services/auth.service';\nimport { TwoFactorService } from '../../services/two-factor.service';\nimport { OAuthService } from '../../services/oauth.service';\nimport { User, ChangePasswordRequest } from '../../models/user.model';\n\n@Component({\n  selector: 'app-profile',\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.scss'],\n  standalone: false\n})\nexport class ProfileComponent implements OnInit {\n  currentUser: User | null = null;\n  changePasswordForm: FormGroup;\n  twoFactorStatus = { enabled: false };\n\n  loading = false;\n  hideCurrentPassword = true;\n  hideNewPassword = true;\n  hideConfirmPassword = true;\n  showChangePassword = false;\n\n  constructor(\n    private authService: AuthService,\n    private twoFactorService: TwoFactorService,\n    private oauthService: OAuthService,\n    private formBuilder: FormBuilder,\n    private snackBar: MatSnackBar\n  ) {\n    this.changePasswordForm = this.formBuilder.group({\n      currentPassword: ['', [Validators.required]],\n      newPassword: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      twoFactorToken: ['']\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  ngOnInit(): void {\n    this.currentUser = this.authService.currentUserValue;\n    this.load2FAStatus();\n  }\n\n  load2FAStatus(): void {\n    this.twoFactorService.get2FAStatus().subscribe({\n      next: (status) => {\n        this.twoFactorStatus = status;\n      },\n      error: (error) => {\n        console.error('Failed to load 2FA status:', error);\n      }\n    });\n  }\n\n  toggleChangePassword(): void {\n    this.showChangePassword = !this.showChangePassword;\n    if (!this.showChangePassword) {\n      this.changePasswordForm.reset();\n    }\n  }\n\n  onChangePassword(): void {\n    if (this.changePasswordForm.invalid) {\n      this.markFormGroupTouched(this.changePasswordForm);\n      return;\n    }\n\n    this.loading = true;\n    const formValue = this.changePasswordForm.value;\n\n    this.authService.changePassword(\n      formValue.currentPassword,\n      formValue.newPassword,\n      formValue.twoFactorToken || undefined\n    ).subscribe({\n      next: (response) => {\n        this.snackBar.open('Password changed successfully!', 'Close', { duration: 3000 });\n        this.changePasswordForm.reset();\n        this.showChangePassword = false;\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Failed to change password', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  isOAuthUser(): boolean {\n    return this.oauthService.isOAuthUser(this.currentUser);\n  }\n\n  getOAuthProviderName(): string {\n    return this.oauthService.getOAuthProviderName(this.currentUser);\n  }\n\n  getOAuthProviderIcon(): string {\n    return this.oauthService.getOAuthProviderIcon(this.currentUser);\n  }\n\n  getOAuthProviderColor(): string {\n    return this.oauthService.getOAuthProviderColor(this.currentUser);\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.changePasswordForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['passwordMismatch']) return 'Passwords do not match';\n    }\n    return '';\n  }\n\n  private passwordMatchValidator(form: FormGroup) {\n    const newPassword = form.get('newPassword');\n    const confirmPassword = form.get('confirmPassword');\n\n    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n    } else {\n      confirmPassword?.setErrors(null);\n    }\n\n    return null;\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n}\n", "<div class=\"profile-container\">\n  <div class=\"container\">\n    <h1>Profile & Security Settings</h1>\n    \n    <mat-card>\n      <mat-card-header>\n        <mat-card-title>User Information</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"user-info\">\n          <div class=\"user-avatar\" *ngIf=\"currentUser?.avatarUrl\">\n            <img [src]=\"currentUser!.avatarUrl\" [alt]=\"(currentUser!.firstName || '') + ' ' + (currentUser!.lastName || '')\">\n          </div>\n          <div class=\"user-details\">\n            <p><strong>Name:</strong> {{ currentUser?.firstName }} {{ currentUser?.lastName }}</p>\n            <p><strong>Email:</strong> {{ currentUser?.email }}</p>\n            <p><strong>Phone:</strong> {{ currentUser?.phone || 'Not provided' }}</p>\n            <p><strong>Member since:</strong> {{ currentUser?.createdAt | date:'mediumDate' }}</p>\n\n            <!-- OAuth Provider Info -->\n            <div *ngIf=\"isOAuthUser()\" class=\"oauth-info\">\n              <p><strong>Connected via:</strong>\n                <span class=\"oauth-provider\">\n                  <i [class]=\"getOAuthProviderIcon()\" [style.color]=\"getOAuthProviderColor()\"></i>\n                  {{ getOAuthProviderName() }}\n                </span>\n              </p>\n            </div>\n\n            <!-- Account Status -->\n            <div class=\"account-status\">\n              <div class=\"status-chips\">\n                <div class=\"status-chip\" [class.verified]=\"currentUser?.emailVerified\" [class.unverified]=\"!currentUser?.emailVerified\">\n                  <mat-icon>{{ currentUser?.emailVerified ? 'verified' : 'warning' }}</mat-icon>\n                  <span>Email {{ currentUser?.emailVerified ? 'Verified' : 'Unverified' }}</span>\n                </div>\n                <div class=\"status-chip\" [class.enabled]=\"twoFactorStatus.enabled\" [class.disabled]=\"!twoFactorStatus.enabled\">\n                  <mat-icon>security</mat-icon>\n                  <span>2FA {{ twoFactorStatus.enabled ? 'Enabled' : 'Disabled' }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n      <mat-card-actions>\n        <button mat-button color=\"primary\">\n          <mat-icon>edit</mat-icon>\n          Edit Profile\n        </button>\n      </mat-card-actions>\n    </mat-card>\n\n    <mat-card>\n      <mat-card-header>\n        <mat-card-title>Security Settings</mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p>Manage your account security settings including two-factor authentication and password.</p>\n\n        <!-- Change Password Section -->\n        <div class=\"security-section\">\n          <div class=\"section-header\">\n            <h3>Password</h3>\n            <button mat-button color=\"primary\" (click)=\"toggleChangePassword()\" *ngIf=\"!isOAuthUser()\">\n              <mat-icon>{{ showChangePassword ? 'expand_less' : 'expand_more' }}</mat-icon>\n              {{ showChangePassword ? 'Cancel' : 'Change Password' }}\n            </button>\n            <div *ngIf=\"isOAuthUser()\" class=\"oauth-password-notice\">\n              <mat-icon color=\"warn\">info</mat-icon>\n              <span>Password managed by {{ getOAuthProviderName() }}</span>\n            </div>\n          </div>\n\n          <!-- Change Password Form -->\n          <div *ngIf=\"showChangePassword && !isOAuthUser()\" class=\"change-password-form\">\n            <form [formGroup]=\"changePasswordForm\" (ngSubmit)=\"onChangePassword()\">\n              <mat-form-field class=\"form-field\" appearance=\"outline\">\n                <mat-label>Current Password</mat-label>\n                <input matInput [type]=\"hideCurrentPassword ? 'password' : 'text'\"\n                       formControlName=\"currentPassword\" autocomplete=\"current-password\">\n                <button mat-icon-button matSuffix (click)=\"hideCurrentPassword = !hideCurrentPassword\" type=\"button\">\n                  <mat-icon>{{ hideCurrentPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n                </button>\n                <mat-error>{{ getFieldError('currentPassword') }}</mat-error>\n              </mat-form-field>\n\n              <mat-form-field class=\"form-field\" appearance=\"outline\">\n                <mat-label>New Password</mat-label>\n                <input matInput [type]=\"hideNewPassword ? 'password' : 'text'\"\n                       formControlName=\"newPassword\" autocomplete=\"new-password\">\n                <button mat-icon-button matSuffix (click)=\"hideNewPassword = !hideNewPassword\" type=\"button\">\n                  <mat-icon>{{ hideNewPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n                </button>\n                <mat-error>{{ getFieldError('newPassword') }}</mat-error>\n              </mat-form-field>\n\n              <mat-form-field class=\"form-field\" appearance=\"outline\">\n                <mat-label>Confirm New Password</mat-label>\n                <input matInput [type]=\"hideConfirmPassword ? 'password' : 'text'\"\n                       formControlName=\"confirmPassword\" autocomplete=\"new-password\">\n                <button mat-icon-button matSuffix (click)=\"hideConfirmPassword = !hideConfirmPassword\" type=\"button\">\n                  <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n                </button>\n                <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>\n              </mat-form-field>\n\n              <!-- 2FA Token Field (if 2FA is enabled) -->\n              <mat-form-field *ngIf=\"twoFactorStatus.enabled\" class=\"form-field\" appearance=\"outline\">\n                <mat-label>2FA Code (Required)</mat-label>\n                <input matInput formControlName=\"twoFactorToken\" placeholder=\"000000\"\n                       maxlength=\"6\" autocomplete=\"one-time-code\">\n                <mat-icon matSuffix>verified_user</mat-icon>\n                <mat-hint>Enter the 6-digit code from your authenticator app</mat-hint>\n                <mat-error>{{ getFieldError('twoFactorToken') }}</mat-error>\n              </mat-form-field>\n\n              <div class=\"form-actions\">\n                <button mat-raised-button color=\"primary\" type=\"submit\" [disabled]=\"loading\">\n                  <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n                  <span *ngIf=\"!loading\">Change Password</span>\n                </button>\n                <button mat-button type=\"button\" (click)=\"toggleChangePassword()\">\n                  Cancel\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </mat-card-content>\n      <mat-card-actions>\n        <button mat-button color=\"accent\" routerLink=\"/dashboard/two-factor\">\n          <mat-icon>security</mat-icon>\n          {{ twoFactorStatus.enabled ? 'Manage 2FA' : 'Setup 2FA' }}\n        </button>\n      </mat-card-actions>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;ICSzDC,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAE,SAAA,cAAiH;IACnHF,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAI,SAAA,EAA8B;IAACJ,EAA/B,CAAAK,UAAA,QAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,EAAAR,EAAA,CAAAS,aAAA,CAA8B,SAAAH,MAAA,CAAAC,WAAA,CAAAG,SAAA,iBAAAJ,MAAA,CAAAC,WAAA,CAAAI,QAAA,QAA6E;;;;;IAU3GX,EADL,CAAAC,cAAA,cAA8C,QACzC,aAAQ;IAAAD,EAAA,CAAAY,MAAA,qBAAc;IAAAZ,EAAA,CAAAG,YAAA,EAAS;IAChCH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,SAAA,QAAgF;IAChFF,EAAA,CAAAY,MAAA,GACF;IAEJZ,EAFI,CAAAG,YAAA,EAAO,EACL,EACA;;;;IAJGH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAa,UAAA,CAAAP,MAAA,CAAAQ,oBAAA,GAAgC;IAACd,EAAA,CAAAe,WAAA,UAAAT,MAAA,CAAAU,qBAAA,GAAuC;IAC3EhB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAAX,MAAA,CAAAY,oBAAA,QACF;;;;;;IAuCJlB,EAAA,CAAAC,cAAA,iBAA2F;IAAxDD,EAAA,CAAAmB,UAAA,mBAAAC,4DAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;MAAA,MAAAhB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAmB,oBAAA,EAAsB;IAAA,EAAC;IACjEzB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,GAAwD;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAC7EH,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAG,YAAA,EAAS;;;;IAFGH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAA0B,iBAAA,CAAApB,MAAA,CAAAqB,kBAAA,iCAAwD;IAClE3B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAAX,MAAA,CAAAqB,kBAAA,qCACF;;;;;IAEE3B,EADF,CAAAC,cAAA,cAAyD,mBAChC;IAAAD,EAAA,CAAAY,MAAA,WAAI;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAY,MAAA,GAAgD;IACxDZ,EADwD,CAAAG,YAAA,EAAO,EACzD;;;;IADEH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAiB,kBAAA,yBAAAX,MAAA,CAAAY,oBAAA,GAAgD;;;;;IAuCpDlB,EADF,CAAAC,cAAA,yBAAwF,gBAC3E;IAAAD,EAAA,CAAAY,MAAA,0BAAmB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAE,SAAA,gBACkD;IAClDF,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAY,MAAA,oBAAa;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IAC5CH,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,yDAAkD;IAAAZ,EAAA,CAAAG,YAAA,EAAW;IACvEH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAY,MAAA,GAAqC;IAClDZ,EADkD,CAAAG,YAAA,EAAY,EAC7C;;;;IADJH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAA0B,iBAAA,CAAApB,MAAA,CAAAsB,aAAA,mBAAqC;;;;;IAK9C5B,EAAA,CAAAE,SAAA,sBAAyD;;;;;IACzDF,EAAA,CAAAC,cAAA,WAAuB;IAAAD,EAAA,CAAAY,MAAA,sBAAe;IAAAZ,EAAA,CAAAG,YAAA,EAAO;;;;;;IA5CnDH,EADF,CAAAC,cAAA,cAA+E,eACN;IAAhCD,EAAA,CAAAmB,UAAA,sBAAAU,0DAAA;MAAA7B,EAAA,CAAAqB,aAAA,CAAAS,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAYlB,MAAA,CAAAyB,gBAAA,EAAkB;IAAA,EAAC;IAElE/B,EADF,CAAAC,cAAA,yBAAwD,gBAC3C;IAAAD,EAAA,CAAAY,MAAA,uBAAgB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAE,SAAA,gBACyE;IACzEF,EAAA,CAAAC,cAAA,iBAAqG;IAAnED,EAAA,CAAAmB,UAAA,mBAAAa,yDAAA;MAAAhC,EAAA,CAAAqB,aAAA,CAAAS,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAlB,MAAA,CAAA2B,mBAAA,IAAA3B,MAAA,CAAA2B,mBAAA;IAAA,EAAoD;IACpFjC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAY,MAAA,GAA2D;IACvEZ,EADuE,CAAAG,YAAA,EAAW,EACzE;IACTH,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAY,MAAA,IAAsC;IACnDZ,EADmD,CAAAG,YAAA,EAAY,EAC9C;IAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAY,MAAA,oBAAY;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAE,SAAA,iBACiE;IACjEF,EAAA,CAAAC,cAAA,kBAA6F;IAA3DD,EAAA,CAAAmB,UAAA,mBAAAe,0DAAA;MAAAlC,EAAA,CAAAqB,aAAA,CAAAS,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAlB,MAAA,CAAA6B,eAAA,IAAA7B,MAAA,CAAA6B,eAAA;IAAA,EAA4C;IAC5EnC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAY,MAAA,IAAuD;IACnEZ,EADmE,CAAAG,YAAA,EAAW,EACrE;IACTH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAY,MAAA,IAAkC;IAC/CZ,EAD+C,CAAAG,YAAA,EAAY,EAC1C;IAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAY,MAAA,4BAAoB;IAAAZ,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAE,SAAA,iBACqE;IACrEF,EAAA,CAAAC,cAAA,kBAAqG;IAAnED,EAAA,CAAAmB,UAAA,mBAAAiB,0DAAA;MAAApC,EAAA,CAAAqB,aAAA,CAAAS,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAAAlB,MAAA,CAAA+B,mBAAA,IAAA/B,MAAA,CAAA+B,mBAAA;IAAA,EAAoD;IACpFrC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAY,MAAA,IAA2D;IACvEZ,EADuE,CAAAG,YAAA,EAAW,EACzE;IACTH,EAAA,CAAAC,cAAA,iBAAW;IAAAD,EAAA,CAAAY,MAAA,IAAsC;IACnDZ,EADmD,CAAAG,YAAA,EAAY,EAC9C;IAGjBH,EAAA,CAAAsC,UAAA,KAAAC,kDAAA,8BAAwF;IAUtFvC,EADF,CAAAC,cAAA,eAA0B,kBACqD;IAE3ED,EADA,CAAAsC,UAAA,KAAAE,+CAAA,0BAA2C,KAAAC,wCAAA,mBACpB;IACzBzC,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAkE;IAAjCD,EAAA,CAAAmB,UAAA,mBAAAuB,0DAAA;MAAA1C,EAAA,CAAAqB,aAAA,CAAAS,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAAuB,aAAA;MAAA,OAAAvB,EAAA,CAAAwB,WAAA,CAASlB,MAAA,CAAAmB,oBAAA,EAAsB;IAAA,EAAC;IAC/DzB,EAAA,CAAAY,MAAA,gBACF;IAGNZ,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACH;;;;IAnDEH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,UAAA,cAAAC,MAAA,CAAAqC,kBAAA,CAAgC;IAGlB3C,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA2B,mBAAA,uBAAkD;IAGtDjC,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAA0B,iBAAA,CAAApB,MAAA,CAAA2B,mBAAA,mCAA2D;IAE5DjC,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAA0B,iBAAA,CAAApB,MAAA,CAAAsB,aAAA,oBAAsC;IAKjC5B,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA6B,eAAA,uBAA8C;IAGlDnC,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAA0B,iBAAA,CAAApB,MAAA,CAAA6B,eAAA,mCAAuD;IAExDnC,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA0B,iBAAA,CAAApB,MAAA,CAAAsB,aAAA,gBAAkC;IAK7B5B,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAA+B,mBAAA,uBAAkD;IAGtDrC,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAA0B,iBAAA,CAAApB,MAAA,CAAA+B,mBAAA,mCAA2D;IAE5DrC,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAA0B,iBAAA,CAAApB,MAAA,CAAAsB,aAAA,oBAAsC;IAIlC5B,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAsC,eAAA,CAAAC,OAAA,CAA6B;IAUY7C,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,UAAA,aAAAC,MAAA,CAAAwC,OAAA,CAAoB;IAC5D9C,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAAC,MAAA,CAAAwC,OAAA,CAAa;IACpB9C,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAAwC,OAAA,CAAc;;;AD1GvC,OAAM,MAAOC,gBAAgB;EAW3BC,YACUC,WAAwB,EACxBC,gBAAkC,EAClCC,YAA0B,EAC1BC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAflB,KAAA9C,WAAW,GAAgB,IAAI;IAE/B,KAAAqC,eAAe,GAAG;MAAEC,OAAO,EAAE;IAAK,CAAE;IAEpC,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAb,mBAAmB,GAAG,IAAI;IAC1B,KAAAE,eAAe,GAAG,IAAI;IACtB,KAAAE,mBAAmB,GAAG,IAAI;IAC1B,KAAAV,kBAAkB,GAAG,KAAK;IASxB,IAAI,CAACgB,kBAAkB,GAAG,IAAI,CAACS,WAAW,CAACE,KAAK,CAAC;MAC/CC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACxD,UAAU,CAACyD,QAAQ,CAAC,CAAC;MAC5CC,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC1D,UAAU,CAACyD,QAAQ,EAAEzD,UAAU,CAAC2D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACjEC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC5D,UAAU,CAACyD,QAAQ,CAAC,CAAC;MAC5CI,cAAc,EAAE,CAAC,EAAE;KACpB,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACxD,WAAW,GAAG,IAAI,CAAC0C,WAAW,CAACe,gBAAgB;IACpD,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,IAAI,CAACf,gBAAgB,CAACgB,YAAY,EAAE,CAACC,SAAS,CAAC;MAC7CC,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACzB,eAAe,GAAGyB,MAAM;MAC/B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;EACJ;EAEA7C,oBAAoBA,CAAA;IAClB,IAAI,CAACE,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,CAAC,IAAI,CAACA,kBAAkB,EAAE;MAC5B,IAAI,CAACgB,kBAAkB,CAAC6B,KAAK,EAAE;IACjC;EACF;EAEAzC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACY,kBAAkB,CAAC8B,OAAO,EAAE;MACnC,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC/B,kBAAkB,CAAC;MAClD;IACF;IAEA,IAAI,CAACG,OAAO,GAAG,IAAI;IACnB,MAAM6B,SAAS,GAAG,IAAI,CAAChC,kBAAkB,CAACiC,KAAK;IAE/C,IAAI,CAAC3B,WAAW,CAAC4B,cAAc,CAC7BF,SAAS,CAACpB,eAAe,EACzBoB,SAAS,CAAClB,WAAW,EACrBkB,SAAS,CAACf,cAAc,IAAIkB,SAAS,CACtC,CAACX,SAAS,CAAC;MACVC,IAAI,EAAGW,QAAQ,IAAI;QACjB,IAAI,CAAC1B,QAAQ,CAAC2B,IAAI,CAAC,gCAAgC,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACjF,IAAI,CAACtC,kBAAkB,CAAC6B,KAAK,EAAE;QAC/B,IAAI,CAAC7C,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACmB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDwB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjB,QAAQ,CAAC2B,IAAI,CAACV,KAAK,CAACY,OAAO,IAAI,2BAA2B,EAAE,OAAO,EAAE;UAAED,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC7F,IAAI,CAACnC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAqC,WAAWA,CAAA;IACT,OAAO,IAAI,CAAChC,YAAY,CAACgC,WAAW,CAAC,IAAI,CAAC5E,WAAW,CAAC;EACxD;EAEAW,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACiC,YAAY,CAACjC,oBAAoB,CAAC,IAAI,CAACX,WAAW,CAAC;EACjE;EAEAO,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACqC,YAAY,CAACrC,oBAAoB,CAAC,IAAI,CAACP,WAAW,CAAC;EACjE;EAEAS,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACmC,YAAY,CAACnC,qBAAqB,CAAC,IAAI,CAACT,WAAW,CAAC;EAClE;EAEAqB,aAAaA,CAACwD,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC1C,kBAAkB,CAAC2C,GAAG,CAACF,SAAS,CAAC;IACpD,IAAIC,KAAK,EAAEE,MAAM,IAAIF,KAAK,CAACG,OAAO,EAAE;MAClC,IAAIH,KAAK,CAACE,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGH,SAAS,cAAc;MAC/D,IAAIC,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGH,SAAS,qBAAqBC,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,CAACE,cAAc,aAAa;MAC5H,IAAIJ,KAAK,CAACE,MAAM,CAAC,kBAAkB,CAAC,EAAE,OAAO,wBAAwB;IACvE;IACA,OAAO,EAAE;EACX;EAEQzB,sBAAsBA,CAAC4B,IAAe;IAC5C,MAAMjC,WAAW,GAAGiC,IAAI,CAACJ,GAAG,CAAC,aAAa,CAAC;IAC3C,MAAM3B,eAAe,GAAG+B,IAAI,CAACJ,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAI7B,WAAW,IAAIE,eAAe,IAAIF,WAAW,CAACmB,KAAK,KAAKjB,eAAe,CAACiB,KAAK,EAAE;MACjFjB,eAAe,CAACgC,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;IACvD,CAAC,MAAM;MACLjC,eAAe,EAAEgC,SAAS,CAAC,IAAI,CAAC;IAClC;IAEA,OAAO,IAAI;EACb;EAEQjB,oBAAoBA,CAACmB,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAACP,GAAG,CAACY,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qCAxHUtD,gBAAgB,EAAA/C,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxG,EAAA,CAAAsG,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA1G,EAAA,CAAAsG,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA5G,EAAA,CAAAsG,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA9G,EAAA,CAAAsG,iBAAA,CAAAS,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhBlE,gBAAgB;IAAAmE,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZzBzH,EAFJ,CAAAC,cAAA,aAA+B,aACN,SACjB;QAAAD,EAAA,CAAAY,MAAA,kCAA2B;QAAAZ,EAAA,CAAAG,YAAA,EAAK;QAIhCH,EAFJ,CAAAC,cAAA,eAAU,sBACS,qBACC;QAAAD,EAAA,CAAAY,MAAA,uBAAgB;QAClCZ,EADkC,CAAAG,YAAA,EAAiB,EACjC;QAEhBH,EADF,CAAAC,cAAA,uBAAkB,aACO;QACrBD,EAAA,CAAAsC,UAAA,KAAAqF,gCAAA,iBAAwD;QAInD3H,EADL,CAAAC,cAAA,cAA0B,SACrB,cAAQ;QAAAD,EAAA,CAAAY,MAAA,aAAK;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAAwD;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QACnFH,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,cAAM;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAAwB;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QACpDH,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,cAAM;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAA0C;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QACtEH,EAAH,CAAAC,cAAA,SAAG,cAAQ;QAAAD,EAAA,CAAAY,MAAA,qBAAa;QAAAZ,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAY,MAAA,IAAgD;;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QAGtFH,EAAA,CAAAsC,UAAA,KAAAsF,gCAAA,iBAA8C;QAaxC5H,EAHN,CAAAC,cAAA,cAA4B,cACA,cACgG,gBAC5G;QAAAD,EAAA,CAAAY,MAAA,IAAyD;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC9EH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAY,MAAA,IAAkE;QAC1EZ,EAD0E,CAAAG,YAAA,EAAO,EAC3E;QAEJH,EADF,CAAAC,cAAA,cAA+G,gBACnG;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAY,MAAA,IAA0D;QAM5EZ,EAN4E,CAAAG,YAAA,EAAO,EACnE,EACF,EACF,EACF,EACF,EACW;QAGfH,EAFJ,CAAAC,cAAA,wBAAkB,iBACmB,gBACvB;QAAAD,EAAA,CAAAY,MAAA,YAAI;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAY,MAAA,sBACF;QAEJZ,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;QAIPH,EAFJ,CAAAC,cAAA,gBAAU,uBACS,sBACC;QAAAD,EAAA,CAAAY,MAAA,yBAAiB;QACnCZ,EADmC,CAAAG,YAAA,EAAiB,EAClC;QAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;QAAAD,EAAA,CAAAY,MAAA,+FAAuF;QAAAZ,EAAA,CAAAG,YAAA,EAAI;QAK1FH,EAFJ,CAAAC,cAAA,eAA8B,eACA,UACtB;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAG,YAAA,EAAK;QAKjBH,EAJA,CAAAsC,UAAA,KAAAuF,mCAAA,qBAA2F,KAAAC,gCAAA,kBAIlC;QAI3D9H,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAsC,UAAA,KAAAyF,gCAAA,oBAA+E;QAsDnF/H,EADE,CAAAG,YAAA,EAAM,EACW;QAGfH,EAFJ,CAAAC,cAAA,wBAAkB,kBACqD,gBACzD;QAAAD,EAAA,CAAAY,MAAA,gBAAQ;QAAAZ,EAAA,CAAAG,YAAA,EAAW;QAC7BH,EAAA,CAAAY,MAAA,IACF;QAIRZ,EAJQ,CAAAG,YAAA,EAAS,EACQ,EACV,EACP,EACF;;;QAhI8BH,EAAA,CAAAI,SAAA,IAA4B;QAA5BJ,EAAA,CAAAK,UAAA,SAAAqH,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAAC,SAAA,CAA4B;QAI1BR,EAAA,CAAAI,SAAA,GAAwD;QAAxDJ,EAAA,CAAAgI,kBAAA,MAAAN,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAAG,SAAA,OAAAgH,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAAI,QAAA,CAAwD;QACvDX,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAiB,kBAAA,MAAAyG,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAA0H,KAAA,CAAwB;QACxBjI,EAAA,CAAAI,SAAA,GAA0C;QAA1CJ,EAAA,CAAAiB,kBAAA,OAAAyG,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAA2H,KAAA,oBAA0C;QACnClI,EAAA,CAAAI,SAAA,GAAgD;QAAhDJ,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAmI,WAAA,SAAAT,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAA6H,SAAA,gBAAgD;QAG5EpI,EAAA,CAAAI,SAAA,GAAmB;QAAnBJ,EAAA,CAAAK,UAAA,SAAAqH,GAAA,CAAAvC,WAAA,GAAmB;QAYInF,EAAA,CAAAI,SAAA,GAA6C;QAACJ,EAA9C,CAAAqI,WAAA,aAAAX,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAA+H,aAAA,CAA6C,iBAAAZ,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAA+H,aAAA,EAAiD;QAC3GtI,EAAA,CAAAI,SAAA,GAAyD;QAAzDJ,EAAA,CAAA0B,iBAAA,EAAAgG,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAA+H,aAAA,2BAAyD;QAC7DtI,EAAA,CAAAI,SAAA,GAAkE;QAAlEJ,EAAA,CAAAiB,kBAAA,YAAAyG,GAAA,CAAAnH,WAAA,kBAAAmH,GAAA,CAAAnH,WAAA,CAAA+H,aAAA,8BAAkE;QAEjDtI,EAAA,CAAAI,SAAA,EAAyC;QAACJ,EAA1C,CAAAqI,WAAA,YAAAX,GAAA,CAAA9E,eAAA,CAAAC,OAAA,CAAyC,cAAA6E,GAAA,CAAA9E,eAAA,CAAAC,OAAA,CAA4C;QAEtG7C,EAAA,CAAAI,SAAA,GAA0D;QAA1DJ,EAAA,CAAAiB,kBAAA,SAAAyG,GAAA,CAAA9E,eAAA,CAAAC,OAAA,0BAA0D;QA0BD7C,EAAA,CAAAI,SAAA,IAAoB;QAApBJ,EAAA,CAAAK,UAAA,UAAAqH,GAAA,CAAAvC,WAAA,GAAoB;QAInFnF,EAAA,CAAAI,SAAA,EAAmB;QAAnBJ,EAAA,CAAAK,UAAA,SAAAqH,GAAA,CAAAvC,WAAA,GAAmB;QAOrBnF,EAAA,CAAAI,SAAA,EAA0C;QAA1CJ,EAAA,CAAAK,UAAA,SAAAqH,GAAA,CAAA/F,kBAAA,KAAA+F,GAAA,CAAAvC,WAAA,GAA0C;QA0DhDnF,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAiB,kBAAA,MAAAyG,GAAA,CAAA9E,eAAA,CAAAC,OAAA,mCACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}