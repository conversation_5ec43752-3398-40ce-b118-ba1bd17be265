/**
 * Module dependencies.
 */
var util = require('util')
  , OAuth2Strategy = require('passport-oauth2')
  , Profile = require('./profile')
  , InternalOAuthError = require('passport-oauth2').InternalOAuthError;

/**
 * `Strategy` constructor.
 *
 * The GitHub authentication strategy authenticates requests by delegating to
 * GitHub using the OAuth 2.0 protocol.
 *
 * Applications must supply a `verify` callback which accepts an `accessToken`,
 * `refreshToken` and service-specific `profile`, and then calls the `done`
 * callback supplying a `user`, which should be set to `false` if the
 * credentials are not valid.  If an exception occured, `err` should be set.
 *
 * Options:
 *   - `clientID`     your GitHub application's Client ID
 *   - `clientSecret` your GitHub application's Client Secret
 *   - `callbackURL`  URL to which GitHub will redirect the user after granting authorization
 *   - `scope`        array of permission scopes to request. Valid scopes include:
 *                    'user', 'public_repo', 'repo', 'gist', or none.
 *                    (see http://developer.github.com/v3/oauth/#scopes for more info)
 *   — `userAgent`    All API requests MUST include a valid User Agent string.
 *                    e.g: domain name of your application.
 *                    (see http://developer.github.com/v3/#user-agent-required for more info)
 *   — `allRawEmails` boolean to indicate whether to return all raw email addresses or just the primary
 *
 * Examples:
 *
 *     passport.use(new GitHubStrategy({
 *         clientID: '123-456-789',
 *         clientSecret: 'shhh-its-a-secret',
 *         callbackURL: 'https://www.example.net/auth/github/callback',
 *         userAgent: 'myapp.com'
 *       },
 *       function(accessToken, refreshToken, profile, done) {
 *         User.findOrCreate(..., function (err, user) {
 *           done(err, user);
 *         });
 *       }
 *     ));
 *
 * @param {Object} options
 * @param {Function} verify
 * @api public
 */
function Strategy(options, verify) {
  options = options || {};
  options.authorizationURL = options.authorizationURL || 'https://github.com/login/oauth/authorize';
  options.tokenURL = options.tokenURL || 'https://github.com/login/oauth/access_token';
  options.scopeSeparator = options.scopeSeparator || ',';
  options.customHeaders = options.customHeaders || {};

  if (!options.customHeaders['User-Agent']) {
    options.customHeaders['User-Agent'] = options.userAgent || 'passport-github';
  }

  OAuth2Strategy.call(this, options, verify);
  this.name = options.name || 'github';
  this._userProfileURL = options.userProfileURL || 'https://api.github.com/user';
  this._userEmailURL = options.userEmailURL || 'https://api.github.com/user/emails';
  this._oauth2.useAuthorizationHeaderforGET(true);
  this._allRawEmails = options.allRawEmails || false;
}

/**
 * Inherit from `OAuth2Strategy`.
 */
util.inherits(Strategy, OAuth2Strategy);

/**
 * Retrieve user profile from GitHub.
 *
 * This function constructs a normalized profile, with the following properties:
 *
 *   - `provider`         always set to `github`
 *   - `id`               the user's GitHub ID
 *   - `username`         the user's GitHub username
 *   - `displayName`      the user's full name
 *   - `profileUrl`       the URL of the profile for the user on GitHub
 *   - `emails`           the user's email addresses
 *
 * @param {String} accessToken
 * @param {Function} done
 * @api protected
 */
Strategy.prototype.userProfile = function(accessToken, done) {
  var self = this;

  this._oauth2.get(this._userProfileURL, accessToken, function (err, body, res) {
    var json;

    if (err) {
      return done(new InternalOAuthError('Failed to fetch user profile', err));
    }

    try {
      json = JSON.parse(body);
    } catch (ex) {
      return done(new Error('Failed to parse user profile'));
    }

    var profile = Profile.parse(json);
    profile.provider  = 'github';
    profile._raw = body;
    profile._json = json;

    var canAccessEmail = false;
    var scopes = self._scope;
    if (typeof scopes === 'string') {
      scopes = scopes.split(self._scopeSeparator);
    }
    if (Array.isArray(scopes)) {
      canAccessEmail = scopes.some(function(scope) {
        return scope === 'user' || scope === 'user:email';
      });
    }
    if (!canAccessEmail) {
      return done(null, profile);
    }

    // Getting emails
    self._oauth2.get(self._userEmailURL, accessToken, function (err, body, res) {
      if (err) {
        return done(new InternalOAuthError('Failed to fetch user emails', err));
      }

      var json = JSON.parse(body);

      if (!json || !json.length) {
        return done(new Error('Failed to fetch user emails'));
      }

      if (self._allRawEmails) {
        profile.emails = json.map(function (email) {
          email.value = email.email;
          delete email.email;
          return email;
        });
      } else {
        for (var index in json) {
          if (json[index].primary) {
            profile.emails = [{ value: json[index].email }];
            break;
          }
        }
      }

      done(null, profile);
    });
  });
};

/**
 * Expose `Strategy`.
 */
module.exports = Strategy;
