{"ast": null, "code": "import { map } from './map';\nexport function pluck(...properties) {\n  const length = properties.length;\n  if (length === 0) {\n    throw new Error('list of properties cannot be empty.');\n  }\n  return map(x => {\n    let currentProp = x;\n    for (let i = 0; i < length; i++) {\n      const p = currentProp === null || currentProp === void 0 ? void 0 : currentProp[properties[i]];\n      if (typeof p !== 'undefined') {\n        currentProp = p;\n      } else {\n        return undefined;\n      }\n    }\n    return currentProp;\n  });\n}\n//# sourceMappingURL=pluck.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}