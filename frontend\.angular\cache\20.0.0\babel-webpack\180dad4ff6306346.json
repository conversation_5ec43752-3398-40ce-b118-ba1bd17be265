{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatRippleModule } from './index-BFRo2fUq.mjs';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatOption, a as MatOptgroup } from './option-BzhYL_xC.mjs';\nimport { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module-4F8Up4PL.mjs';\nlet MatOptionModule = /*#__PURE__*/(() => {\n  class MatOptionModule {\n    static ɵfac = function MatOptionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatOptionModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatOptionModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatR<PERSON>pleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption]\n    });\n  }\n  return MatOptionModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatOptionModule as M };\n//# sourceMappingURL=index-DwiL-HGk.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}