import {Entity, model, property} from '@loopback/repository';

@model({
  settings: {
    strict: true,
    hiddenProperties: ['password'],
    indexes: {
      uniqueEmail: {
        keys: {
          email: 1,
        },
        options: {
          unique: true,
        },
      },
    },
    postgresql: {
      schema: 'public',
      table: 'user'
    }
  },
})
export class User extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    index: {
      unique: true,
    },
    jsonSchema: {
      format: 'email',
      minLength: 5,
      maxLength: 100,
      errorMessage: 'Email should be a valid email address',
    },
  })
  email: string;

  @property({
    type: 'string',
    required: true,
    postgresql: {
      columnName: 'first_name'
    },
    jsonSchema: {
      minLength: 2,
      maxLength: 50,
      pattern: '^[a-zA-Z\\s]+$',
      errorMessage: 'First name should contain only letters and spaces',
    },
  })
  firstName: string;

  @property({
    type: 'string',
    required: true,
    postgresql: {
      columnName: 'last_name'
    },
    jsonSchema: {
      minLength: 2,
      maxLength: 50,
      pattern: '^[a-zA-Z\\s]+$',
      errorMessage: 'Last name should contain only letters and spaces',
    },
  })
  lastName: string;

  @property({
    type: 'string',
    jsonSchema: {
      pattern: '^\\+?[1-9]\\d{1,14}$',
      errorMessage: 'Phone number should be a valid international format',
    },
  })
  phone?: string;

  @property({
    type: 'boolean',
    default: false,
    postgresql: {
      columnName: 'email_verified'
    }
  })
  emailVerified: boolean;

  @property({
    type: 'boolean',
    default: false,
    postgresql: {
      columnName: 'phone_verified'
    }
  })
  phoneVerified: boolean;

  @property({
    type: 'boolean',
    default: false,
    postgresql: {
      columnName: 'two_factor_enabled'
    }
  })
  twoFactorEnabled: boolean;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'two_factor_secret'
    }
  })
  twoFactorSecret?: string;

  @property({
    type: 'array',
    itemType: 'string',
    default: [],
  })
  roles: string[];

  @property({
    type: 'boolean',
    default: true,
    postgresql: {
      columnName: 'is_active'
    }
  })
  isActive: boolean;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'created_at'
    }
  })
  createdAt: Date;

  @property({
    type: 'date',
    default: () => new Date(),
    postgresql: {
      columnName: 'updated_at'
    }
  })
  updatedAt: Date;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'last_login_at'
    }
  })
  lastLoginAt?: Date;

  @property({
    type: 'number',
    default: 0,
    postgresql: {
      columnName: 'login_attempts'
    }
  })
  loginAttempts: number;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'lock_until'
    }
  })
  lockUntil?: Date;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'email_verification_token'
    }
  })
  emailVerificationToken?: string;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'email_verification_expires'
    }
  })
  emailVerificationExpires?: Date;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'password_reset_token'
    }
  })
  passwordResetToken?: string;

  @property({
    type: 'date',
    postgresql: {
      columnName: 'password_reset_expires'
    }
  })
  passwordResetExpires?: Date;

  @property({
    type: 'string',
    hidden: true,
  })
  password?: string;

  // OAuth fields
  @property({
    type: 'string',
    postgresql: {
      columnName: 'google_id'
    }
  })
  googleId?: string;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'github_id'
    }
  })
  githubId?: string;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'microsoft_id'
    }
  })
  microsoftId?: string;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'oauth_provider'
    }
  })
  oauthProvider?: string;

  @property({
    type: 'string',
    postgresql: {
      columnName: 'avatar_url'
    }
  })
  avatarUrl?: string;

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<User>) {
    super(data);
  }
}

export interface UserRelations {
  // describe navigational properties here
}

export type UserWithRelations = User & UserRelations;
