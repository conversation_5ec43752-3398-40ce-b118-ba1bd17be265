const axios = require('axios');

const BASE_URL = 'http://localhost:3002';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Email',
  lastName: 'Test',
  phone: '+1234567890'
};

let authToken = '';

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
}

async function setupTestUser() {
  console.log('\n🔧 Setting up test user...');
  
  // Try to create user
  const signupResult = await apiCall('POST', '/auth/signup', testUser);
  
  if (signupResult.success) {
    console.log('✅ Test user created');
  } else if (signupResult.status === 409) {
    console.log('ℹ️  Test user already exists');
  } else {
    console.log('❌ Failed to create test user:', signupResult.error);
    return false;
  }
  
  // Login to get token
  const loginResult = await apiCall('POST', '/auth/login', {
    email: testUser.email,
    password: testUser.password
  });
  
  if (loginResult.success) {
    authToken = loginResult.data.token;
    console.log('✅ Test user logged in');
    return true;
  } else {
    console.log('❌ Failed to login test user:', loginResult.error);
    return false;
  }
}

async function testEmailVerification() {
  console.log('\n🧪 Testing Email Verification...');
  
  // Create a new user to trigger verification email
  const newUser = {
    email: `verification-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    firstName: 'Verification',
    lastName: 'Test',
    phone: '+1234567890'
  };
  
  const result = await apiCall('POST', '/auth/signup', newUser);
  
  if (result.success) {
    console.log('✅ User created - verification email should be sent');
    console.log('📧 Check console logs for email content');
    return true;
  } else {
    console.log('❌ Failed to create user for verification test:', result.error);
    return false;
  }
}

async function testPasswordResetEmail() {
  console.log('\n🧪 Testing Password Reset Email...');
  
  const result = await apiCall('POST', '/auth/forgot-password', {
    email: testUser.email
  });
  
  if (result.success) {
    console.log('✅ Password reset email sent');
    console.log('📧 Check console logs for email content');
    return true;
  } else {
    console.log('❌ Failed to send password reset email:', result.error);
    return false;
  }
}

async function test2FAEmailOTP() {
  console.log('\n🧪 Testing 2FA Email OTP...');
  
  const result = await apiCall('POST', '/2fa/send-email', null, authToken);
  
  if (result.success) {
    console.log('✅ 2FA email OTP sent');
    console.log('📧 Check console logs for OTP email content');
    return true;
  } else {
    console.log('❌ Failed to send 2FA email OTP:', result.error);
    return false;
  }
}

async function testOTPLoginEmail() {
  console.log('\n🧪 Testing OTP Login Email...');
  
  // First, request OTP for login
  const otpResult = await apiCall('POST', '/otp/send', {
    email: testUser.email,
    type: 'login'
  });
  
  if (otpResult.success) {
    console.log('✅ OTP login email sent');
    console.log('📧 Check console logs for OTP email content');
    return true;
  } else {
    console.log('❌ Failed to send OTP login email:', otpResult.error);
    return false;
  }
}

async function testBrevoConfiguration() {
  console.log('\n🧪 Testing Brevo Configuration...');
  
  const brevoApiKey = process.env.BREVO_API_KEY;
  
  if (!brevoApiKey || brevoApiKey === 'your_brevo_api_key_here') {
    console.log('⚠️  Brevo API key not configured - using fallback mode');
    console.log('ℹ️  To test Brevo integration:');
    console.log('   1. Get API key from https://app.brevo.com/settings/keys/api');
    console.log('   2. Set BREVO_API_KEY in .env file');
    console.log('   3. Restart the server');
    return true;
  } else {
    console.log('✅ Brevo API key configured');
    console.log('🔑 Key:', brevoApiKey.substring(0, 10) + '...');
    
    // Test Brevo API directly
    try {
      const response = await axios.get('https://api.brevo.com/v3/account', {
        headers: {
          'api-key': brevoApiKey
        }
      });
      
      console.log('✅ Brevo API connection successful');
      console.log('📊 Account info:', {
        email: response.data.email,
        plan: response.data.plan?.type || 'Unknown'
      });
      return true;
    } catch (error) {
      console.log('❌ Brevo API connection failed:', error.response?.data || error.message);
      return false;
    }
  }
}

async function testEmailTemplates() {
  console.log('\n🧪 Testing Email Templates...');
  
  const templates = [
    { name: 'Verification Email', test: testEmailVerification },
    { name: 'Password Reset Email', test: testPasswordResetEmail },
    { name: '2FA Email OTP', test: test2FAEmailOTP },
    { name: 'OTP Login Email', test: testOTPLoginEmail }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const template of templates) {
    try {
      const result = await template.test();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${template.name} threw an error:`, error.message);
      failed++;
    }
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log(`\n📊 Email Template Tests: ${passed} passed, ${failed} failed`);
  return failed === 0;
}

// Main test runner
async function runEmailTests() {
  console.log('📧 Starting Email System Tests...');
  console.log('=' .repeat(50));
  
  const tests = [
    { name: 'Brevo Configuration', fn: testBrevoConfiguration },
    { name: 'Setup Test User', fn: setupTestUser },
    { name: 'Email Templates', fn: testEmailTemplates }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message);
      failed++;
    }
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Email Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('🎉 All email tests passed!');
  } else {
    console.log('⚠️  Some email tests failed. Check the logs above for details.');
  }
  
  console.log('\n📝 Notes:');
  console.log('- Email content is logged to console in demo mode');
  console.log('- Configure BREVO_API_KEY to test real email sending');
  console.log('- Check server logs for detailed email information');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runEmailTests().catch(console.error);
}

module.exports = { runEmailTests, testBrevoConfiguration };
