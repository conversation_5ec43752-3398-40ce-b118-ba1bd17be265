const { runAllTests } = require('./test-auth');
const { runEmailTests } = require('./test-email');
const { run2FATests } = require('./test-2fa');
const axios = require('axios');

const BASE_URL = 'http://localhost:3002';

// Check if server is running
async function checkServerHealth() {
  console.log('🏥 Checking server health...');
  
  try {
    const response = await axios.get(`${BASE_URL}/ping`);
    if (response.status === 200) {
      console.log('✅ Server is running and healthy');
      console.log('📊 Server info:', response.data);
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not responding');
    console.log('💡 Make sure to start the server with: npm run dev');
    return false;
  }
}

// Check environment configuration
async function checkEnvironment() {
  console.log('\n🔧 Checking environment configuration...');
  
  const requiredEnvVars = [
    'JWT_SECRET',
    'DATABASE_URL',
    'FRONTEND_URL'
  ];
  
  const optionalEnvVars = [
    'BREVO_API_KEY',
    'GOOGLE_CLIENT_ID',
    'GITHUB_CLIENT_ID',
    'MICROSOFT_CLIENT_ID'
  ];
  
  let allRequired = true;
  
  console.log('📋 Required environment variables:');
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      console.log(`   ✅ ${envVar}: Set`);
    } else {
      console.log(`   ❌ ${envVar}: Missing`);
      allRequired = false;
    }
  }
  
  console.log('\n📋 Optional environment variables:');
  for (const envVar of optionalEnvVars) {
    if (process.env[envVar] && process.env[envVar] !== `your_${envVar.toLowerCase()}`) {
      console.log(`   ✅ ${envVar}: Configured`);
    } else {
      console.log(`   ⚠️  ${envVar}: Not configured (using demo mode)`);
    }
  }
  
  return allRequired;
}

// Test database connection
async function testDatabaseConnection() {
  console.log('\n🗄️  Testing database connection...');
  
  try {
    // Try to make a simple API call that requires database
    const response = await axios.get(`${BASE_URL}/ping`);
    
    if (response.data.database) {
      console.log('✅ Database connection successful');
      console.log('📊 Database info:', response.data.database);
      return true;
    } else {
      console.log('⚠️  Database status unknown');
      return true; // Don't fail if we can't determine status
    }
  } catch (error) {
    console.log('❌ Database connection test failed:', error.message);
    return false;
  }
}

// Test API endpoints availability
async function testAPIEndpoints() {
  console.log('\n🔗 Testing API endpoints availability...');
  
  const endpoints = [
    { method: 'GET', path: '/ping', description: 'Health check' },
    { method: 'POST', path: '/auth/signup', description: 'User signup' },
    { method: 'POST', path: '/auth/login', description: 'User login' },
    { method: 'GET', path: '/auth/oauth/google/url', description: 'Google OAuth URL' },
    { method: 'GET', path: '/auth/oauth/github/url', description: 'GitHub OAuth URL' },
    { method: 'GET', path: '/auth/oauth/microsoft/url', description: 'Microsoft OAuth URL' }
  ];
  
  let available = 0;
  let total = endpoints.length;
  
  for (const endpoint of endpoints) {
    try {
      const config = {
        method: endpoint.method,
        url: `${BASE_URL}${endpoint.path}`,
        validateStatus: () => true // Don't throw on any status code
      };
      
      const response = await axios(config);
      
      // Consider 2xx, 4xx as available (endpoint exists)
      // 5xx might indicate server issues
      if (response.status < 500) {
        console.log(`   ✅ ${endpoint.method} ${endpoint.path} - ${endpoint.description}`);
        available++;
      } else {
        console.log(`   ❌ ${endpoint.method} ${endpoint.path} - Server error (${response.status})`);
      }
    } catch (error) {
      console.log(`   ❌ ${endpoint.method} ${endpoint.path} - Not available`);
    }
  }
  
  console.log(`\n📊 API Endpoints: ${available}/${total} available`);
  return available === total;
}

// Display test instructions
function displayTestInstructions() {
  console.log('\n📖 Test Instructions:');
  console.log('=' .repeat(50));
  console.log('🔧 Setup:');
  console.log('   1. Make sure the server is running: npm run dev');
  console.log('   2. Configure environment variables in .env file');
  console.log('   3. Ensure database is accessible');
  console.log('');
  console.log('📧 Email Testing:');
  console.log('   - Without Brevo: Emails logged to console');
  console.log('   - With Brevo: Real emails sent (configure BREVO_API_KEY)');
  console.log('');
  console.log('🔐 2FA Testing:');
  console.log('   - TOTP tokens generated programmatically');
  console.log('   - Compatible with Google/Microsoft Authenticator, Authy');
  console.log('   - QR codes provided for manual testing');
  console.log('');
  console.log('🔑 OAuth Testing:');
  console.log('   - URLs generated for all providers');
  console.log('   - Manual testing required for full OAuth flow');
  console.log('   - Configure client IDs/secrets for real testing');
  console.log('');
}

// Main test runner
async function runAllSystemTests() {
  console.log('🚀 Starting Comprehensive System Tests');
  console.log('=' .repeat(60));
  console.log(`📅 Test Date: ${new Date().toISOString()}`);
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log('=' .repeat(60));
  
  // Pre-flight checks
  console.log('\n🛫 Pre-flight Checks:');
  
  const serverHealthy = await checkServerHealth();
  if (!serverHealthy) {
    console.log('\n❌ Server is not running. Please start the server and try again.');
    process.exit(1);
  }
  
  const envConfigured = await checkEnvironment();
  const dbConnected = await testDatabaseConnection();
  const apiAvailable = await testAPIEndpoints();
  
  if (!envConfigured) {
    console.log('\n⚠️  Some required environment variables are missing.');
    console.log('💡 Check .env file and ensure all required variables are set.');
  }
  
  if (!dbConnected) {
    console.log('\n❌ Database connection issues detected.');
    console.log('💡 Check database configuration and connectivity.');
  }
  
  if (!apiAvailable) {
    console.log('\n⚠️  Some API endpoints are not available.');
    console.log('💡 Check server logs for any startup errors.');
  }
  
  // Display instructions
  displayTestInstructions();
  
  // Run test suites
  console.log('\n🧪 Running Test Suites:');
  console.log('=' .repeat(60));
  
  try {
    // Authentication tests
    console.log('\n1️⃣  AUTHENTICATION TESTS');
    console.log('-' .repeat(30));
    await runAllTests();
    
    // Wait between test suites
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Email tests
    console.log('\n2️⃣  EMAIL TESTS');
    console.log('-' .repeat(30));
    await runEmailTests();
    
    // Wait between test suites
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 2FA tests
    console.log('\n3️⃣  TWO-FACTOR AUTHENTICATION TESTS');
    console.log('-' .repeat(30));
    await run2FATests();
    
  } catch (error) {
    console.log('\n❌ Test suite execution failed:', error.message);
    console.log('💡 Check server logs for detailed error information.');
  }
  
  // Final summary
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 All Test Suites Completed');
  console.log('=' .repeat(60));
  console.log('📝 Next Steps:');
  console.log('   1. Review test results above');
  console.log('   2. Fix any failing tests');
  console.log('   3. Configure optional services (Brevo, OAuth)');
  console.log('   4. Test with frontend application');
  console.log('   5. Deploy to production environment');
  console.log('');
  console.log('💡 For production deployment:');
  console.log('   - Use strong JWT secrets');
  console.log('   - Configure real email service');
  console.log('   - Set up OAuth applications');
  console.log('   - Enable HTTPS');
  console.log('   - Configure proper CORS settings');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllSystemTests().catch(console.error);
}

module.exports = { 
  runAllSystemTests, 
  checkServerHealth, 
  checkEnvironment,
  testDatabaseConnection,
  testAPIEndpoints
};
