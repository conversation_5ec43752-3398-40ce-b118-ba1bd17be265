{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { RuntimeError, InjectionToken, getCurrentTNode, assertInInjectionContext, signalAsReadonlyFn, assertNgModuleType, Injector, inject, ɵɵdefineInjectable as __defineInjectable, formatRuntimeError, INTERNAL_APPLICATION_ERROR_HANDLER, PROVIDED_ZONELESS, stringify, ɵɵinject as __inject, ChangeDetectionScheduler, errorHandlerEnvironmentInitializer, makeEnvironmentProviders, runInInjectionContext, INJECTOR_SCOPE, provideEnvironmentInitializer, ErrorHandler, _global, isComponentHost, getComponentLViewByIndex, DECLARATION_COMPONENT_VIEW, getLView, ɵɵdefineInjector as __defineInjector, ENVIRONMENT_INITIALIZER, unwrapRNode, CLEANUP, is<PERSON><PERSON><PERSON>, unwrapLView, hasI18n, R<PERSON><PERSON><PERSON><PERSON>, HOS<PERSON>, getComponentDef, assertTNode, isProjectionTNode, PARENT, CONTEXT, HEADER_OFFSET, TVIEW, isRootView, CONTAINER_HEADER_OFFSET, isLView, getTNode, assertNotInReactiveContext, ViewContext, DestroyRef, getNullInjector } from './root_effect_scheduler-0BxwqIgm.mjs';\nexport { DOCUMENT, EnvironmentInjector, INJECTOR, PendingTasks, defineInjectable, forwardRef, importProvidersFrom, isSignal, isStandalone, provideBrowserGlobalErrorListeners, resolveForwardRef, signal, EffectScheduler as ɵEffectScheduler, NG_COMP_DEF as ɵNG_COMP_DEF, NG_DIR_DEF as ɵNG_DIR_DEF, NG_ELEMENT_ID as ɵNG_ELEMENT_ID, NG_INJ_DEF as ɵNG_INJ_DEF, NG_MOD_DEF as ɵNG_MOD_DEF, NG_PIPE_DEF as ɵNG_PIPE_DEF, NG_PROV_DEF as ɵNG_PROV_DEF, PendingTasksInternal as ɵPendingTasksInternal, R3Injector as ɵR3Injector, XSS_SECURITY_URL as ɵXSS_SECURITY_URL, ZONELESS_ENABLED as ɵZONELESS_ENABLED, convertToBitFlags as ɵconvertToBitFlags, createInjector as ɵcreateInjector, getInjectableDef as ɵgetInjectableDef, isEnvironmentProviders as ɵisEnvironmentProviders, isInjectable as ɵisInjectable, setInjectorProfilerContext as ɵsetInjectorProfilerContext, store as ɵstore, truncateMiddle as ɵtruncateMiddle, ɵunwrapWritableSignal, ɵɵdisableBindings, ɵɵenableBindings, ɵɵinvalidFactoryDep, ɵɵnamespaceHTML, ɵɵnamespaceMathML, ɵɵnamespaceSVG, ɵɵresetView, ɵɵrestoreView } from './root_effect_scheduler-0BxwqIgm.mjs';\nimport { SIGNAL_NODE, signalSetFn, SIGNAL, producerAccessed, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from './signal-ePSl6jXn.mjs';\nimport { ɵɵinjectAttribute as __injectAttribute, createMultiResultQuerySignalFn, createSingleResultRequiredQuerySignalFn, createSingleResultOptionalQuerySignalFn, makePropDecorator, NgModuleFactory, COMPILER_OPTIONS, setJitOptions, isComponentResourceResolutionQueueEmpty, getCompilerFacade, resolveComponentResources, IMAGE_CONFIG, getDocument, setClassMetadata, Injectable, NgZone, PROVIDED_NG_ZONE, remove, isPromise, ApplicationInitStatus, LOCALE_ID, DEFAULT_LOCALE_ID, setLocaleId, ApplicationRef, createNgModuleRefWithProviders, optionsReducer, internalProvideZoneChangeDetection, ChangeDetectionSchedulerImpl, getNgZone, getNgZoneOptions, publishDefaultGlobalUtils, PLATFORM_INITIALIZER, publishSignalConfiguration, checkNoChangesInternal, UseExhaustiveCheckNoChanges, getRegisteredNgModuleType, ViewRef as ViewRef$1, isListLikeIterable, iterateListLike, isJsObject, SkipSelf, Optional, ɵɵdefineNgModule as __defineNgModule, NgModule, profiler, assertStandaloneComponentType, EnvironmentNgModuleRefAdapter, IS_EVENT_REPLAY_ENABLED, JSACTION_BLOCK_ELEMENT_MAP, APP_ID, setStashFn, APP_BOOTSTRAP_LISTENER, JSACTION_EVENT_CONTRACT, removeListeners, isIncrementalHydrationEnabled, performanceMarkFeature, EVENT_REPLAY_ENABLED_DEFAULT, sharedStashFunction, sharedMapFunction, DEFER_BLOCK_SSR_ID_ATTRIBUTE, invokeListeners, triggerHydrationFromBlockName, enableStashEventListenerImpl, isI18nHydrationEnabled, TransferState, NGH_DATA_KEY, NGH_DEFER_BLOCKS_KEY, getLNodeForHydration, NGH_ATTR_NAME, SKIP_HYDRATION_ATTR_NAME, isI18nHydrationSupportEnabled, ViewEncapsulation as ViewEncapsulation$1, getOrComputeI18nChildren, trySerializeI18nBlock, I18N_DATA, isTNodeShape, isDetachedByI18n, isDisconnectedNode, isInSkipHydrationBlock, unsupportedProjectionOfDomNodes, TEMPLATES, CONTAINERS, isLetDeclaration, ELEMENT_CONTAINERS, processTextNodeBeforeSerialization, setJSActionAttributes, DISCONNECTED_NODES, NODES, calcPathForNode, NUM_ROOT_NODES, TEMPLATE_ID, isDeferBlock, getLDeferBlockDetails, getTDeferBlockDetails, collectNativeNodesInLContainer, validateNodeExists, validateMatchingNode, DEFER_BLOCK_ID, DEFER_BLOCK_STATE, DEFER_BLOCK_STATE$1, MULTIPLIER, collectNativeNodes, convertHydrateTriggersToJsAction, DEFER_HYDRATE_TRIGGERS, DEFER_PARENT_BLOCK_ID, IS_HYDRATION_DOM_REUSE_ENABLED, IS_I18N_HYDRATION_ENABLED, IS_INCREMENTAL_HYDRATION_ENABLED, DehydratedBlockRegistry, DEHYDRATED_BLOCK_REGISTRY, processBlockData, gatherDeferBlocksCommentNodes, processAndInitTriggers, appendDeferBlocksToJSActionMap, verifySsrContentsIntegrity, Console, enableRetrieveHydrationInfoImpl, enableLocateOrCreateElementNodeImpl, enableLocateOrCreateTextNodeImpl, enableLocateOrCreateElementContainerNodeImpl, enableLocateOrCreateContainerAnchorImpl, enableLocateOrCreateContainerRefImpl, enableFindMatchingDehydratedViewImpl, enableApplyRootElementTransformImpl, setIsI18nHydrationSupportEnabled, PRESERVE_HOST_CONTENT, cleanupDehydratedViews, countBlocksSkippedByHydration, enableLocateOrCreateI18nNodeImpl, enablePrepareI18nBlockForHydrationImpl, enableClaimDehydratedIcuCaseImpl, enableRetrieveDeferBlockDataImpl, readPatchedLView, setClassMetadataAsync, angularCoreEnv, NOOP_AFTER_RENDER_REF, AfterRenderManager, TracingService, AfterRenderImpl, AfterRenderSequence, AFTER_RENDER_PHASES, assertComponentDef, ComponentFactory } from './debug_node-Dn-GvQJo.mjs';\nexport { ANIMATION_MODULE_TYPE, APP_INITIALIZER, Attribute, CSP_NONCE, CUSTOM_ELEMENTS_SCHEMA, ChangeDetectionStrategy, Compiler, CompilerFactory, Component, ComponentFactory$1 as ComponentFactory, ComponentFactoryResolver, ComponentRef, DEFAULT_CURRENCY_CODE, DebugElement, DebugEventListener, DebugNode, Directive, ElementRef, EventEmitter, Host, HostBinding, HostListener, Inject, Input, MissingTranslationStrategy, ModuleWithComponentFactories, NO_ERRORS_SCHEMA, NgModuleFactory$1 as NgModuleFactory, NgModuleRef$1 as NgModuleRef, NgProbeToken, Output, PACKAGE_ROOT_URL, PLATFORM_ID, Pipe, QueryList, Renderer2, RendererFactory2, RendererStyleFlags2, Sanitizer, SecurityContext, Self, SimpleChange, TRANSLATIONS, TRANSLATIONS_FORMAT, TemplateRef, Testability, TestabilityRegistry, Type, ViewContainerRef, afterEveryRender, afterNextRender, asNativeElements, createEnvironmentInjector, createNgModule, createNgModuleRef, enableProfiling, getDebugNode, inputBinding, makeStateKey, outputBinding, provideAppInitializer, provideNgReflectAttributes, provideZoneChangeDetection, provideZonelessChangeDetection, setTestabilityGetter, twoWayBinding, AcxChangeDetectionStrategy as ɵAcxChangeDetectionStrategy, AcxViewEncapsulation as ɵAcxViewEncapsulation, ComponentFactory$1 as ɵComponentFactory, DEFER_BLOCK_CONFIG as ɵDEFER_BLOCK_CONFIG, DEFER_BLOCK_DEPENDENCY_INTERCEPTOR as ɵDEFER_BLOCK_DEPENDENCY_INTERCEPTOR, DeferBlockBehavior as ɵDeferBlockBehavior, DeferBlockState as ɵDeferBlockState, Framework as ɵFramework, HydrationStatus as ɵHydrationStatus, IMAGE_CONFIG_DEFAULTS as ɵIMAGE_CONFIG_DEFAULTS, LContext as ɵLContext, LocaleDataIndex as ɵLocaleDataIndex, NOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR as ɵNOT_FOUND_CHECK_ONLY_ELEMENT_INJECTOR, NO_CHANGE as ɵNO_CHANGE, NoopNgZone as ɵNoopNgZone, ReflectionCapabilities as ɵReflectionCapabilities, ComponentRef$1 as ɵRender3ComponentRef, NgModuleRef as ɵRender3NgModuleRef, SSR_CONTENT_INTEGRITY_MARKER as ɵSSR_CONTENT_INTEGRITY_MARKER, TESTABILITY as ɵTESTABILITY, TESTABILITY_GETTER as ɵTESTABILITY_GETTER, TimerScheduler as ɵTimerScheduler, TracingAction as ɵTracingAction, _sanitizeHtml as ɵ_sanitizeHtml, _sanitizeUrl as ɵ_sanitizeUrl, allowSanitizationBypassAndThrow as ɵallowSanitizationBypassAndThrow, bypassSanitizationTrustHtml as ɵbypassSanitizationTrustHtml, bypassSanitizationTrustResourceUrl as ɵbypassSanitizationTrustResourceUrl, bypassSanitizationTrustScript as ɵbypassSanitizationTrustScript, bypassSanitizationTrustStyle as ɵbypassSanitizationTrustStyle, bypassSanitizationTrustUrl as ɵbypassSanitizationTrustUrl, clearResolutionOfComponentResourcesQueue as ɵclearResolutionOfComponentResourcesQueue, compileComponent as ɵcompileComponent, compileDirective as ɵcompileDirective, compileNgModule as ɵcompileNgModule, compileNgModuleDefs as ɵcompileNgModuleDefs, compilePipe as ɵcompilePipe, depsTracker as ɵdepsTracker, devModeEqual as ɵdevModeEqual, findLocaleData as ɵfindLocaleData, flushModuleScopingQueueAsMuchAsPossible as ɵflushModuleScopingQueueAsMuchAsPossible, generateStandaloneInDeclarationsError as ɵgenerateStandaloneInDeclarationsError, getAsyncClassMetadataFn as ɵgetAsyncClassMetadataFn, getDebugNode as ɵgetDebugNode, getDeferBlocks as ɵgetDeferBlocks, getDirectives as ɵgetDirectives, getHostElement as ɵgetHostElement, getLContext as ɵgetLContext, getLocaleCurrencyCode as ɵgetLocaleCurrencyCode, getLocalePluralCase as ɵgetLocalePluralCase, getSanitizationBypassType as ɵgetSanitizationBypassType, ɵgetUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode, isBoundToModule as ɵisBoundToModule, isComponentDefPendingResolution as ɵisComponentDefPendingResolution, isNgModule as ɵisNgModule, isSubscribable as ɵisSubscribable, isViewDirty as ɵisViewDirty, markForRefresh as ɵmarkForRefresh, noSideEffects as ɵnoSideEffects, patchComponentDefWithScope as ɵpatchComponentDefWithScope, publishExternalGlobalUtil as ɵpublishExternalGlobalUtil, readHydrationInfo as ɵreadHydrationInfo, registerLocaleData as ɵregisterLocaleData, renderDeferBlockState as ɵrenderDeferBlockState, resetCompiledComponents as ɵresetCompiledComponents, resetJitOptions as ɵresetJitOptions, restoreComponentResolutionQueue as ɵrestoreComponentResolutionQueue, setAllowDuplicateNgModuleIdsForTest as ɵsetAllowDuplicateNgModuleIdsForTest, ɵsetClassDebugInfo, setDocument as ɵsetDocument, ɵsetUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode, transitiveScopesFor as ɵtransitiveScopesFor, triggerResourceLoading as ɵtriggerResourceLoading, unregisterAllLocaleData as ɵunregisterLocaleData, unwrapSafeValue as ɵunwrapSafeValue, ɵɵCopyDefinitionFeature, ɵɵExternalStylesFeature, ɵɵHostDirectivesFeature, ɵɵInheritDefinitionFeature, ɵɵNgOnChangesFeature, ɵɵProvidersFeature, ɵɵadvance, ɵɵattachSourceLocations, ɵɵattribute, ɵɵclassMap, ɵɵclassProp, ɵɵcomponentInstance, ɵɵconditional, ɵɵconditionalBranchCreate, ɵɵconditionalCreate, ɵɵcontentQuery, ɵɵcontentQuerySignal, ɵɵdeclareLet, ɵɵdefer, ɵɵdeferEnableTimerScheduling, ɵɵdeferHydrateNever, ɵɵdeferHydrateOnHover, ɵɵdeferHydrateOnIdle, ɵɵdeferHydrateOnImmediate, ɵɵdeferHydrateOnInteraction, ɵɵdeferHydrateOnTimer, ɵɵdeferHydrateOnViewport, ɵɵdeferHydrateWhen, ɵɵdeferOnHover, ɵɵdeferOnIdle, ɵɵdeferOnImmediate, ɵɵdeferOnInteraction, ɵɵdeferOnTimer, ɵɵdeferOnViewport, ɵɵdeferPrefetchOnHover, ɵɵdeferPrefetchOnIdle, ɵɵdeferPrefetchOnImmediate, ɵɵdeferPrefetchOnInteraction, ɵɵdeferPrefetchOnTimer, ɵɵdeferPrefetchOnViewport, ɵɵdeferPrefetchWhen, ɵɵdeferWhen, ɵɵdefineComponent, ɵɵdefineDirective, ɵɵdefinePipe, ɵɵdirectiveInject, ɵɵdomProperty, ɵɵelement, ɵɵelementContainer, ɵɵelementContainerEnd, ɵɵelementContainerStart, ɵɵelementEnd, ɵɵelementStart, ɵɵgetComponentDepsFactory, ɵɵgetCurrentView, ɵɵgetInheritedFactory, ɵɵgetReplaceMetadataURL, ɵɵi18n, ɵɵi18nApply, ɵɵi18nAttributes, ɵɵi18nEnd, ɵɵi18nExp, ɵɵi18nPostprocess, ɵɵi18nStart, ɵɵinterpolate, ɵɵinterpolate1, ɵɵinterpolate2, ɵɵinterpolate3, ɵɵinterpolate4, ɵɵinterpolate5, ɵɵinterpolate6, ɵɵinterpolate7, ɵɵinterpolate8, ɵɵinterpolateV, ɵɵinvalidFactory, ɵɵlistener, ɵɵloadQuery, ɵɵnextContext, ɵɵpipe, ɵɵpipeBind1, ɵɵpipeBind2, ɵɵpipeBind3, ɵɵpipeBind4, ɵɵpipeBindV, ɵɵprojection, ɵɵprojectionDef, ɵɵproperty, ɵɵpureFunction0, ɵɵpureFunction1, ɵɵpureFunction2, ɵɵpureFunction3, ɵɵpureFunction4, ɵɵpureFunction5, ɵɵpureFunction6, ɵɵpureFunction7, ɵɵpureFunction8, ɵɵpureFunctionV, ɵɵqueryAdvance, ɵɵqueryRefresh, ɵɵreadContextLet, ɵɵreference, registerNgModuleType as ɵɵregisterNgModuleType, ɵɵrepeater, ɵɵrepeaterCreate, ɵɵrepeaterTrackByIdentity, ɵɵrepeaterTrackByIndex, ɵɵreplaceMetadata, ɵɵresolveBody, ɵɵresolveDocument, ɵɵresolveWindow, ɵɵsanitizeHtml, ɵɵsanitizeResourceUrl, ɵɵsanitizeScript, ɵɵsanitizeStyle, ɵɵsanitizeUrl, ɵɵsanitizeUrlOrResourceUrl, ɵɵsetComponentScope, ɵɵsetNgModuleScope, ɵɵstoreLet, ɵɵstyleMap, ɵɵstyleProp, ɵɵsyntheticHostListener, ɵɵsyntheticHostProperty, ɵɵtemplate, ɵɵtemplateRefExtractor, ɵɵtext, ɵɵtextInterpolate, ɵɵtextInterpolate1, ɵɵtextInterpolate2, ɵɵtextInterpolate3, ɵɵtextInterpolate4, ɵɵtextInterpolate5, ɵɵtextInterpolate6, ɵɵtextInterpolate7, ɵɵtextInterpolate8, ɵɵtextInterpolateV, ɵɵtrustConstantHtml, ɵɵtrustConstantResourceUrl, ɵɵtwoWayBindingSet, ɵɵtwoWayListener, ɵɵtwoWayProperty, ɵɵvalidateIframeAttribute, ɵɵviewQuery, ɵɵviewQuerySignal } from './debug_node-Dn-GvQJo.mjs';\nimport { OutputEmitterRef } from './resource-BPCh38bN.mjs';\nexport { computed, effect, linkedSignal, resource, untracked, ResourceImpl as ɵResourceImpl, encapsulateResourceError as ɵencapsulateResourceError, getOutputDestroyRef as ɵgetOutputDestroyRef } from './resource-BPCh38bN.mjs';\nexport { setAlternateWeakRefImpl as ɵsetAlternateWeakRefImpl } from './weak_ref-BaIq-pgY.mjs';\nexport { setCurrentInjector as ɵsetCurrentInjector } from './primitives/di.mjs';\nimport { clearAppScopedEarlyEventContract, EventContract, EventContractContainer, getAppScopedQueuedEventInfos, EventDispatcher, registerDispatcher, EventPhase, isEarlyEventType, isCaptureEventType } from './primitives/event-dispatch.mjs';\nimport 'rxjs';\nimport '@angular/core/primitives/di';\nimport '@angular/core/primitives/signals';\nimport 'rxjs/operators';\nimport './attribute-BWp59EjE.mjs';\nimport './untracked-2ouAFbCz.mjs';\nconst REQUIRED_UNSET_VALUE = /* @__PURE__ */Symbol('InputSignalNode#UNSET');\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst INPUT_SIGNAL_NODE = /* @__PURE__ */(() => {\n  return {\n    ...SIGNAL_NODE,\n    transformFn: undefined,\n    applyValueToInputSignal(node, value) {\n      signalSetFn(node, value);\n    }\n  };\n})();\nconst ɵINPUT_SIGNAL_BRAND_WRITE_TYPE = /* @__PURE__ */Symbol();\n/**\n * Creates an input signal.\n *\n * @param initialValue The initial value.\n *   Can be set to {@link REQUIRED_UNSET_VALUE} for required inputs.\n * @param options Additional options for the input. e.g. a transform, or an alias.\n */\nfunction createInputSignal(initialValue, options) {\n  const node = Object.create(INPUT_SIGNAL_NODE);\n  node.value = initialValue;\n  // Perf note: Always set `transformFn` here to ensure that `node` always\n  // has the same v8 class shape, allowing monomorphic reads on input signals.\n  node.transformFn = options?.transform;\n  function inputValueFn() {\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n    if (node.value === REQUIRED_UNSET_VALUE) {\n      let message = null;\n      if (ngDevMode) {\n        const name = options?.debugName ?? options?.alias;\n        message = `Input${name ? ` \"${name}\"` : ''} is required but no value is available yet.`;\n      }\n      throw new RuntimeError(-950 /* RuntimeErrorCode.REQUIRED_INPUT_NO_VALUE */, message);\n    }\n    return node.value;\n  }\n  inputValueFn[SIGNAL] = node;\n  if (ngDevMode) {\n    inputValueFn.toString = () => `[Input Signal: ${inputValueFn()}]`;\n    node.debugName = options?.debugName;\n  }\n  return inputValueFn;\n}\nvar FactoryTarget = /*#__PURE__*/function (FactoryTarget) {\n  FactoryTarget[FactoryTarget[\"Directive\"] = 0] = \"Directive\";\n  FactoryTarget[FactoryTarget[\"Component\"] = 1] = \"Component\";\n  FactoryTarget[FactoryTarget[\"Injectable\"] = 2] = \"Injectable\";\n  FactoryTarget[FactoryTarget[\"Pipe\"] = 3] = \"Pipe\";\n  FactoryTarget[FactoryTarget[\"NgModule\"] = 4] = \"NgModule\";\n  return FactoryTarget;\n}(FactoryTarget || {});\nvar R3TemplateDependencyKind = /*#__PURE__*/function (R3TemplateDependencyKind) {\n  R3TemplateDependencyKind[R3TemplateDependencyKind[\"Directive\"] = 0] = \"Directive\";\n  R3TemplateDependencyKind[R3TemplateDependencyKind[\"Pipe\"] = 1] = \"Pipe\";\n  R3TemplateDependencyKind[R3TemplateDependencyKind[\"NgModule\"] = 2] = \"NgModule\";\n  return R3TemplateDependencyKind;\n}(R3TemplateDependencyKind || {});\nvar ViewEncapsulation = /*#__PURE__*/function (ViewEncapsulation) {\n  ViewEncapsulation[ViewEncapsulation[\"Emulated\"] = 0] = \"Emulated\";\n  // Historically the 1 value was for `Native` encapsulation which has been removed as of v11.\n  ViewEncapsulation[ViewEncapsulation[\"None\"] = 2] = \"None\";\n  ViewEncapsulation[ViewEncapsulation[\"ShadowDom\"] = 3] = \"ShadowDom\";\n  return ViewEncapsulation;\n}(ViewEncapsulation || {});\n/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n/**\n * Creates a token that can be used to inject static attributes of the host node.\n *\n * @usageNotes\n * ### Injecting an attribute that is known to exist\n * ```ts\n * @Directive()\n * class MyDir {\n *   attr: string = inject(new HostAttributeToken('some-attr'));\n * }\n * ```\n *\n * ### Optionally injecting an attribute\n * ```ts\n * @Directive()\n * class MyDir {\n *   attr: string | null = inject(new HostAttributeToken('some-attr'), {optional: true});\n * }\n * ```\n * @publicApi\n */\nclass HostAttributeToken {\n  attributeName;\n  constructor(attributeName) {\n    this.attributeName = attributeName;\n  }\n  /** @internal */\n  __NG_ELEMENT_ID__ = () => __injectAttribute(this.attributeName);\n  toString() {\n    return `HostAttributeToken ${this.attributeName}`;\n  }\n}\n\n/**\n * A token that can be used to inject the tag name of the host node.\n *\n * @usageNotes\n * ### Injecting a tag name that is known to exist\n * ```ts\n * @Directive()\n * class MyDir {\n *   tagName: string = inject(HOST_TAG_NAME);\n * }\n * ```\n *\n * ### Optionally injecting a tag name\n * ```ts\n * @Directive()\n * class MyDir {\n *   tagName: string | null = inject(HOST_TAG_NAME, {optional: true});\n * }\n * ```\n * @publicApi\n */\nconst HOST_TAG_NAME = /*#__PURE__*/new InjectionToken(ngDevMode ? 'HOST_TAG_NAME' : '');\n// HOST_TAG_NAME should be resolved at the current node, similar to e.g. ElementRef,\n// so we manually specify __NG_ELEMENT_ID__ here, instead of using a factory.\n// tslint:disable-next-line:no-toplevel-property-access\nHOST_TAG_NAME.__NG_ELEMENT_ID__ = flags => {\n  const tNode = getCurrentTNode();\n  if (tNode === null) {\n    throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && 'HOST_TAG_NAME can only be injected in directives and components ' + 'during construction time (in a class constructor or as a class field initializer)');\n  }\n  if (tNode.type & 2 /* TNodeType.Element */) {\n    return tNode.value;\n  }\n  if (flags & 8 /* InternalInjectFlags.Optional */) {\n    return null;\n  }\n  throw new RuntimeError(204 /* RuntimeErrorCode.INVALID_INJECTION_TOKEN */, ngDevMode && `HOST_TAG_NAME was used on ${getDevModeNodeName(tNode)} which doesn't have an underlying element in the DOM. ` + `This is invalid, and so the dependency should be marked as optional.`);\n};\nfunction getDevModeNodeName(tNode) {\n  if (tNode.type & 8 /* TNodeType.ElementContainer */) {\n    return 'an <ng-container>';\n  } else if (tNode.type & 4 /* TNodeType.Container */) {\n    return 'an <ng-template>';\n  } else if (tNode.type & 128 /* TNodeType.LetDeclaration */) {\n    return 'an @let declaration';\n  } else {\n    return 'a node';\n  }\n}\n\n/**\n * The `output` function allows declaration of Angular outputs in\n * directives and components.\n *\n * You can use outputs to emit values to parent directives and component.\n * Parents can subscribe to changes via:\n *\n * - template event bindings. For example, `(myOutput)=\"doSomething($event)\"`\n * - programmatic subscription by using `OutputRef#subscribe`.\n *\n * @usageNotes\n *\n * To use `output()`, import the function from `@angular/core`.\n *\n * ```ts\n * import {output} from '@angular/core';\n * ```\n *\n * Inside your component, introduce a new class member and initialize\n * it with a call to `output`.\n *\n * ```ts\n * @Directive({\n *   ...\n * })\n * export class MyDir {\n *   nameChange = output<string>();    // OutputEmitterRef<string>\n *   onClick    = output();            // OutputEmitterRef<void>\n * }\n * ```\n *\n * You can emit values to consumers of your directive, by using\n * the `emit` method from `OutputEmitterRef`.\n *\n * ```ts\n * updateName(newName: string): void {\n *   this.nameChange.emit(newName);\n * }\n * ```\n * @initializerApiFunction {\"showTypesInSignaturePreview\": true}\n * @publicApi 19.0\n */\nfunction output(opts) {\n  ngDevMode && assertInInjectionContext(output);\n  return new OutputEmitterRef();\n}\nfunction inputFunction(initialValue, opts) {\n  ngDevMode && assertInInjectionContext(input);\n  return createInputSignal(initialValue, opts);\n}\nfunction inputRequiredFunction(opts) {\n  ngDevMode && assertInInjectionContext(input);\n  return createInputSignal(REQUIRED_UNSET_VALUE, opts);\n}\n/**\n * The `input` function allows declaration of Angular inputs in directives\n * and components.\n *\n * There are two variants of inputs that can be declared:\n *\n *   1. **Optional inputs** with an initial value.\n *   2. **Required inputs** that consumers need to set.\n *\n * By default, the `input` function will declare optional inputs that\n * always have an initial value. Required inputs can be declared\n * using the `input.required()` function.\n *\n * Inputs are signals. The values of an input are exposed as a `Signal`.\n * The signal always holds the latest value of the input that is bound\n * from the parent.\n *\n * @usageNotes\n * To use signal-based inputs, import `input` from `@angular/core`.\n *\n * ```ts\n * import {input} from '@angular/core`;\n * ```\n *\n * Inside your component, introduce a new class member and initialize\n * it with a call to `input` or `input.required`.\n *\n * ```ts\n * @Component({\n *   ...\n * })\n * export class UserProfileComponent {\n *   firstName = input<string>();             // Signal<string|undefined>\n *   lastName  = input.required<string>();    // Signal<string>\n *   age       = input(0)                     // Signal<number>\n * }\n * ```\n *\n * Inside your component template, you can display values of the inputs\n * by calling the signal.\n *\n * ```html\n * <span>{{firstName()}}</span>\n * ```\n *\n * @publicAPI\n * @initializerApiFunction\n */\nconst input = /*#__PURE__*/(() => {\n  // Note: This may be considered a side-effect, but nothing will depend on\n  // this assignment, unless this `input` constant export is accessed. It's a\n  // self-contained side effect that is local to the user facing`input` export.\n  inputFunction.required = inputRequiredFunction;\n  return inputFunction;\n})();\nfunction viewChildFn(locator, opts) {\n  ngDevMode && assertInInjectionContext(viewChild);\n  return createSingleResultOptionalQuerySignalFn(opts);\n}\nfunction viewChildRequiredFn(locator, opts) {\n  ngDevMode && assertInInjectionContext(viewChild);\n  return createSingleResultRequiredQuerySignalFn(opts);\n}\n/**\n * Initializes a view child query.\n *\n * Consider using `viewChild.required` for queries that should always match.\n *\n * @usageNotes\n * Create a child query in your component by declaring a\n * class field and initializing it with the `viewChild()` function.\n *\n * ```angular-ts\n * @Component({template: '<div #el></div><my-component #cmp />'})\n * export class TestComponent {\n *   divEl = viewChild<ElementRef>('el');                   // Signal<ElementRef|undefined>\n *   divElRequired = viewChild.required<ElementRef>('el');  // Signal<ElementRef>\n *   cmp = viewChild(MyComponent);                          // Signal<MyComponent|undefined>\n *   cmpRequired = viewChild.required(MyComponent);         // Signal<MyComponent>\n * }\n * ```\n *\n * @publicApi 19.0\n * @initializerApiFunction\n */\nconst viewChild = /*#__PURE__*/(() => {\n  // Note: This may be considered a side-effect, but nothing will depend on\n  // this assignment, unless this `viewChild` constant export is accessed. It's a\n  // self-contained side effect that is local to the user facing `viewChild` export.\n  viewChildFn.required = viewChildRequiredFn;\n  return viewChildFn;\n})();\n/**\n * Initializes a view children query.\n *\n * Query results are represented as a signal of a read-only collection containing all matched\n * elements.\n *\n * @usageNotes\n * Create a children query in your component by declaring a\n * class field and initializing it with the `viewChildren()` function.\n *\n * ```ts\n * @Component({...})\n * export class TestComponent {\n *   divEls = viewChildren<ElementRef>('el');   // Signal<ReadonlyArray<ElementRef>>\n * }\n * ```\n *\n * @initializerApiFunction\n * @publicApi 19.0\n */\nfunction viewChildren(locator, opts) {\n  ngDevMode && assertInInjectionContext(viewChildren);\n  return createMultiResultQuerySignalFn(opts);\n}\nfunction contentChildFn(locator, opts) {\n  ngDevMode && assertInInjectionContext(contentChild);\n  return createSingleResultOptionalQuerySignalFn(opts);\n}\nfunction contentChildRequiredFn(locator, opts) {\n  ngDevMode && assertInInjectionContext(contentChildren);\n  return createSingleResultRequiredQuerySignalFn(opts);\n}\n/**\n * Initializes a content child query. Consider using `contentChild.required` for queries that should\n * always match.\n *\n * @usageNotes\n * Create a child query in your component by declaring a\n * class field and initializing it with the `contentChild()` function.\n *\n * ```ts\n * @Component({...})\n * export class TestComponent {\n *   headerEl = contentChild<ElementRef>('h');                    // Signal<ElementRef|undefined>\n *   headerElElRequired = contentChild.required<ElementRef>('h'); // Signal<ElementRef>\n *   header = contentChild(MyHeader);                             // Signal<MyHeader|undefined>\n *   headerRequired = contentChild.required(MyHeader);            // Signal<MyHeader>\n * }\n * ```\n *\n * Note: By default `descendants` is `true` which means the query will traverse all descendants in the same template.\n *\n * @initializerApiFunction\n * @publicApi 19.0\n */\nconst contentChild = /*#__PURE__*/(() => {\n  // Note: This may be considered a side-effect, but nothing will depend on\n  // this assignment, unless this `viewChild` constant export is accessed. It's a\n  // self-contained side effect that is local to the user facing `viewChild` export.\n  contentChildFn.required = contentChildRequiredFn;\n  return contentChildFn;\n})();\n/**\n * Initializes a content children query.\n *\n * Query results are represented as a signal of a read-only collection containing all matched\n * elements.\n *\n * @usageNotes\n * Create a children query in your component by declaring a\n * class field and initializing it with the `contentChildren()` function.\n *\n * ```ts\n * @Component({...})\n * export class TestComponent {\n *   headerEl = contentChildren<ElementRef>('h');   // Signal<ReadonlyArray<ElementRef>>\n * }\n * ```\n *\n * Note: By default `descendants` is `false` which means the query will not traverse all descendants in the same template.\n *\n * @initializerApiFunction\n * @publicApi 19.0\n */\nfunction contentChildren(locator, opts) {\n  return createMultiResultQuerySignalFn(opts);\n}\n\n/**\n * Creates a model signal.\n *\n * @param initialValue The initial value.\n *   Can be set to {@link REQUIRED_UNSET_VALUE} for required model signals.\n * @param options Additional options for the model.\n */\nfunction createModelSignal(initialValue, opts) {\n  const node = Object.create(INPUT_SIGNAL_NODE);\n  const emitterRef = new OutputEmitterRef();\n  node.value = initialValue;\n  function getter() {\n    producerAccessed(node);\n    assertModelSet(node.value);\n    return node.value;\n  }\n  getter[SIGNAL] = node;\n  getter.asReadonly = signalAsReadonlyFn.bind(getter);\n  // TODO: Should we throw an error when updating a destroyed model?\n  getter.set = newValue => {\n    if (!node.equal(node.value, newValue)) {\n      signalSetFn(node, newValue);\n      emitterRef.emit(newValue);\n    }\n  };\n  getter.update = updateFn => {\n    assertModelSet(node.value);\n    getter.set(updateFn(node.value));\n  };\n  getter.subscribe = emitterRef.subscribe.bind(emitterRef);\n  getter.destroyRef = emitterRef.destroyRef;\n  if (ngDevMode) {\n    getter.toString = () => `[Model Signal: ${getter()}]`;\n    node.debugName = opts?.debugName;\n  }\n  return getter;\n}\n/** Asserts that a model's value is set. */\nfunction assertModelSet(value) {\n  if (value === REQUIRED_UNSET_VALUE) {\n    throw new RuntimeError(952 /* RuntimeErrorCode.REQUIRED_MODEL_NO_VALUE */, ngDevMode && 'Model is required but no value is available yet.');\n  }\n}\nfunction modelFunction(initialValue, opts) {\n  ngDevMode && assertInInjectionContext(model);\n  return createModelSignal(initialValue, opts);\n}\nfunction modelRequiredFunction(opts) {\n  ngDevMode && assertInInjectionContext(model);\n  return createModelSignal(REQUIRED_UNSET_VALUE, opts);\n}\n/**\n * `model` declares a writeable signal that is exposed as an input/output\n * pair on the containing directive.\n *\n * The input name is taken either from the class member or from the `alias` option.\n * The output name is generated by taking the input name and appending `Change`.\n *\n * @usageNotes\n *\n * To use `model()`, import the function from `@angular/core`.\n *\n * ```ts\n * import {model} from '@angular/core`;\n * ```\n *\n * Inside your component, introduce a new class member and initialize\n * it with a call to `model` or `model.required`.\n *\n * ```ts\n * @Directive({\n *   ...\n * })\n * export class MyDir {\n *   firstName = model<string>();            // ModelSignal<string|undefined>\n *   lastName  = model.required<string>();   // ModelSignal<string>\n *   age       = model(0);                   // ModelSignal<number>\n * }\n * ```\n *\n * Inside your component template, you can display the value of a `model`\n * by calling the signal.\n *\n * ```html\n * <span>{{firstName()}}</span>\n * ```\n *\n * Updating the `model` is equivalent to updating a writable signal.\n *\n * ```ts\n * updateName(newFirstName: string): void {\n *   this.firstName.set(newFirstName);\n * }\n * ```\n *\n * @publicApi 19.0\n * @initializerApiFunction\n */\nconst model = /*#__PURE__*/(() => {\n  // Note: This may be considered a side-effect, but nothing will depend on\n  // this assignment, unless this `model` constant export is accessed. It's a\n  // self-contained side effect that is local to the user facing `model` export.\n  modelFunction.required = modelRequiredFunction;\n  return modelFunction;\n})();\n\n// Stores the default value of `emitDistinctChangesOnly` when the `emitDistinctChangesOnly` is not\n// explicitly set.\nconst emitDistinctChangesOnlyDefaultValue = true;\n/**\n * Base class for query metadata.\n *\n * @see {@link ContentChildren}\n * @see {@link ContentChild}\n * @see {@link ViewChildren}\n * @see {@link ViewChild}\n *\n * @publicApi\n */\nclass Query {}\n/**\n * ContentChildren decorator and metadata.\n *\n *\n * @Annotation\n * @publicApi\n */\nconst ContentChildren = /*#__PURE__*/makePropDecorator('ContentChildren', (selector, opts = {}) => ({\n  selector,\n  first: false,\n  isViewQuery: false,\n  descendants: false,\n  emitDistinctChangesOnly: emitDistinctChangesOnlyDefaultValue,\n  ...opts\n}), Query);\n/**\n * ContentChild decorator and metadata.\n *\n *\n * @Annotation\n *\n * @publicApi\n */\nconst ContentChild = /*#__PURE__*/makePropDecorator('ContentChild', (selector, opts = {}) => ({\n  selector,\n  first: true,\n  isViewQuery: false,\n  descendants: true,\n  ...opts\n}), Query);\n/**\n * ViewChildren decorator and metadata.\n *\n * @Annotation\n * @publicApi\n */\nconst ViewChildren = /*#__PURE__*/makePropDecorator('ViewChildren', (selector, opts = {}) => ({\n  selector,\n  first: false,\n  isViewQuery: true,\n  descendants: true,\n  emitDistinctChangesOnly: emitDistinctChangesOnlyDefaultValue,\n  ...opts\n}), Query);\n/**\n * ViewChild decorator and metadata.\n *\n * @Annotation\n * @publicApi\n */\nconst ViewChild = /*#__PURE__*/makePropDecorator('ViewChild', (selector, opts) => ({\n  selector,\n  first: true,\n  isViewQuery: true,\n  descendants: true,\n  ...opts\n}), Query);\n\n/**\n * @description Represents the version of Angular\n *\n * @publicApi\n */\nclass Version {\n  full;\n  major;\n  minor;\n  patch;\n  constructor(full) {\n    this.full = full;\n    const parts = full.split('.');\n    this.major = parts[0];\n    this.minor = parts[1];\n    this.patch = parts.slice(2).join('.');\n  }\n}\n/**\n * @publicApi\n */\nconst VERSION = /*#__PURE__*/new Version('20.0.0');\nfunction compileNgModuleFactory(injector, options, moduleType) {\n  ngDevMode && assertNgModuleType(moduleType);\n  const moduleFactory = new NgModuleFactory(moduleType);\n  // All of the logic below is irrelevant for AOT-compiled code.\n  if (typeof ngJitMode !== 'undefined' && !ngJitMode) {\n    return Promise.resolve(moduleFactory);\n  }\n  const compilerOptions = injector.get(COMPILER_OPTIONS, []).concat(options);\n  // Configure the compiler to use the provided options. This call may fail when multiple modules\n  // are bootstrapped with incompatible options, as a component can only be compiled according to\n  // a single set of options.\n  setJitOptions({\n    defaultEncapsulation: _lastDefined(compilerOptions.map(opts => opts.defaultEncapsulation)),\n    preserveWhitespaces: _lastDefined(compilerOptions.map(opts => opts.preserveWhitespaces))\n  });\n  if (isComponentResourceResolutionQueueEmpty()) {\n    return Promise.resolve(moduleFactory);\n  }\n  const compilerProviders = compilerOptions.flatMap(option => option.providers ?? []);\n  // In case there are no compiler providers, we just return the module factory as\n  // there won't be any resource loader. This can happen with Ivy, because AOT compiled\n  // modules can be still passed through \"bootstrapModule\". In that case we shouldn't\n  // unnecessarily require the JIT compiler.\n  if (compilerProviders.length === 0) {\n    return Promise.resolve(moduleFactory);\n  }\n  const compiler = getCompilerFacade({\n    usage: 0 /* JitCompilerUsage.Decorator */,\n    kind: 'NgModule',\n    type: moduleType\n  });\n  const compilerInjector = Injector.create({\n    providers: compilerProviders\n  });\n  const resourceLoader = compilerInjector.get(compiler.ResourceLoader);\n  // The resource loader can also return a string while the \"resolveComponentResources\"\n  // always expects a promise. Therefore we need to wrap the returned value in a promise.\n  return resolveComponentResources(url => Promise.resolve(resourceLoader.get(url))).then(() => moduleFactory);\n}\nfunction _lastDefined(args) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\n\n// A delay in milliseconds before the scan is run after onLoad, to avoid any\n// potential race conditions with other LCP-related functions. This delay\n// happens outside of the main JavaScript execution and will only effect the timing\n// on when the warning becomes visible in the console.\nconst SCAN_DELAY = 200;\nconst OVERSIZED_IMAGE_TOLERANCE = 1200;\nlet ImagePerformanceWarning = /*#__PURE__*/(() => {\n  class ImagePerformanceWarning {\n    // Map of full image URLs -> original `ngSrc` values.\n    window = null;\n    observer = null;\n    options = inject(IMAGE_CONFIG);\n    lcpImageUrl;\n    start() {\n      if (typeof ngServerMode !== 'undefined' && ngServerMode || typeof PerformanceObserver === 'undefined' || this.options?.disableImageSizeWarning && this.options?.disableImageLazyLoadWarning) {\n        return;\n      }\n      this.observer = this.initPerformanceObserver();\n      const doc = getDocument();\n      const win = doc.defaultView;\n      if (win) {\n        this.window = win;\n        // Wait to avoid race conditions where LCP image triggers\n        // load event before it's recorded by the performance observer\n        const waitToScan = () => {\n          setTimeout(this.scanImages.bind(this), SCAN_DELAY);\n        };\n        const setup = () => {\n          // Consider the case when the application is created and destroyed multiple times.\n          // Typically, applications are created instantly once the page is loaded, and the\n          // `window.load` listener is always triggered. However, the `window.load` event will never\n          // be fired if the page is loaded, and the application is created later. Checking for\n          // `readyState` is the easiest way to determine whether the page has been loaded or not.\n          if (doc.readyState === 'complete') {\n            waitToScan();\n          } else {\n            this.window?.addEventListener('load', waitToScan, {\n              once: true\n            });\n          }\n        };\n        // Angular doesn't have to run change detection whenever any asynchronous tasks are invoked in\n        // the scope of this functionality.\n        if (typeof Zone !== 'undefined') {\n          Zone.root.run(() => setup());\n        } else {\n          setup();\n        }\n      }\n    }\n    ngOnDestroy() {\n      this.observer?.disconnect();\n    }\n    initPerformanceObserver() {\n      if (typeof PerformanceObserver === 'undefined') {\n        return null;\n      }\n      const observer = new PerformanceObserver(entryList => {\n        const entries = entryList.getEntries();\n        if (entries.length === 0) return;\n        // We use the latest entry produced by the `PerformanceObserver` as the best\n        // signal on which element is actually an LCP one. As an example, the first image to load on\n        // a page, by virtue of being the only thing on the page so far, is often a LCP candidate\n        // and gets reported by PerformanceObserver, but isn't necessarily the LCP element.\n        const lcpElement = entries[entries.length - 1];\n        // Cast to `any` due to missing `element` on the `LargestContentfulPaint` type of entry.\n        // See https://developer.mozilla.org/en-US/docs/Web/API/LargestContentfulPaint\n        const imgSrc = lcpElement.element?.src ?? '';\n        // Exclude `data:` and `blob:` URLs, since they are fetched resources.\n        if (imgSrc.startsWith('data:') || imgSrc.startsWith('blob:')) return;\n        this.lcpImageUrl = imgSrc;\n      });\n      observer.observe({\n        type: 'largest-contentful-paint',\n        buffered: true\n      });\n      return observer;\n    }\n    scanImages() {\n      const images = getDocument().querySelectorAll('img');\n      let lcpElementFound,\n        lcpElementLoadedCorrectly = false;\n      images.forEach(image => {\n        if (!this.options?.disableImageSizeWarning) {\n          // Image elements using the NgOptimizedImage directive are excluded,\n          // as that directive has its own version of this check.\n          if (!image.getAttribute('ng-img') && this.isOversized(image)) {\n            logOversizedImageWarning(image.src);\n          }\n        }\n        if (!this.options?.disableImageLazyLoadWarning && this.lcpImageUrl) {\n          if (image.src === this.lcpImageUrl) {\n            lcpElementFound = true;\n            if (image.loading !== 'lazy' || image.getAttribute('ng-img')) {\n              // This variable is set to true and never goes back to false to account\n              // for the case where multiple images have the same src url, and some\n              // have lazy loading while others don't.\n              // Also ignore NgOptimizedImage because there's a different warning for that.\n              lcpElementLoadedCorrectly = true;\n            }\n          }\n        }\n      });\n      if (lcpElementFound && !lcpElementLoadedCorrectly && this.lcpImageUrl && !this.options?.disableImageLazyLoadWarning) {\n        logLazyLCPWarning(this.lcpImageUrl);\n      }\n    }\n    isOversized(image) {\n      if (!this.window) {\n        return false;\n      }\n      // The `isOversized` check may not be applicable or may require adjustments\n      // for several types of image formats or scenarios. Currently, we specify only\n      // `svg`, but this may also include `gif` since their quality isn’t tied to\n      // dimensions in the same way as raster images.\n      const nonOversizedImageExtentions = [\n      // SVG images are vector-based, which means they can scale\n      // to any size without losing quality.\n      '.svg'];\n      // Convert it to lowercase because this may have uppercase\n      // extensions, such as `IMAGE.SVG`.\n      // We fallback to an empty string because `src` may be `undefined`\n      // if it is explicitly set to `null` by some third-party code\n      // (e.g., `image.src = null`).\n      const imageSource = (image.src || '').toLowerCase();\n      if (nonOversizedImageExtentions.some(extension => imageSource.endsWith(extension))) {\n        return false;\n      }\n      const computedStyle = this.window.getComputedStyle(image);\n      let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n      let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n      const boxSizing = computedStyle.getPropertyValue('box-sizing');\n      const objectFit = computedStyle.getPropertyValue('object-fit');\n      if (objectFit === `cover`) {\n        // Object fit cover may indicate a use case such as a sprite sheet where\n        // this warning does not apply.\n        return false;\n      }\n      if (boxSizing === 'border-box') {\n        // If the image `box-sizing` is set to `border-box`, we adjust the rendered\n        // dimensions by subtracting padding values.\n        const paddingTop = computedStyle.getPropertyValue('padding-top');\n        const paddingRight = computedStyle.getPropertyValue('padding-right');\n        const paddingBottom = computedStyle.getPropertyValue('padding-bottom');\n        const paddingLeft = computedStyle.getPropertyValue('padding-left');\n        renderedWidth -= parseFloat(paddingRight) + parseFloat(paddingLeft);\n        renderedHeight -= parseFloat(paddingTop) + parseFloat(paddingBottom);\n      }\n      const intrinsicWidth = image.naturalWidth;\n      const intrinsicHeight = image.naturalHeight;\n      const recommendedWidth = this.window.devicePixelRatio * renderedWidth;\n      const recommendedHeight = this.window.devicePixelRatio * renderedHeight;\n      const oversizedWidth = intrinsicWidth - recommendedWidth >= OVERSIZED_IMAGE_TOLERANCE;\n      const oversizedHeight = intrinsicHeight - recommendedHeight >= OVERSIZED_IMAGE_TOLERANCE;\n      return oversizedWidth || oversizedHeight;\n    }\n    static ɵfac = function ImagePerformanceWarning_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ImagePerformanceWarning)();\n    };\n    static ɵprov = /*@__PURE__*/__defineInjectable({\n      token: ImagePerformanceWarning,\n      factory: ImagePerformanceWarning.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ImagePerformanceWarning;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && setClassMetadata(ImagePerformanceWarning, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nfunction logLazyLCPWarning(src) {\n  console.warn(formatRuntimeError(-913 /* RuntimeErrorCode.IMAGE_PERFORMANCE_WARNING */, `An image with src ${src} is the Largest Contentful Paint (LCP) element ` + `but was given a \"loading\" value of \"lazy\", which can negatively impact ` + `application loading performance. This warning can be addressed by ` + `changing the loading value of the LCP image to \"eager\", or by using the ` + `NgOptimizedImage directive's prioritization utilities. For more ` + `information about addressing or disabling this warning, see ` + `https://angular.dev/errors/NG0913`));\n}\nfunction logOversizedImageWarning(src) {\n  console.warn(formatRuntimeError(-913 /* RuntimeErrorCode.IMAGE_PERFORMANCE_WARNING */, `An image with src ${src} has intrinsic file dimensions much larger than its ` + `rendered size. This can negatively impact application loading performance. ` + `For more information about addressing or disabling this warning, see ` + `https://angular.dev/errors/NG0913`));\n}\n\n/**\n * Internal token that allows to register extra callbacks that should be invoked during the\n * `PlatformRef.destroy` operation. This token is needed to avoid a direct reference to the\n * `PlatformRef` class (i.e. register the callback via `PlatformRef.onDestroy`), thus making the\n * entire class tree-shakeable.\n */\nconst PLATFORM_DESTROY_LISTENERS = /*#__PURE__*/new InjectionToken(ngDevMode ? 'PlatformDestroyListeners' : '');\n\n/**\n * InjectionToken to control root component bootstrap behavior.\n *\n * This token is primarily used in Angular's server-side rendering (SSR) scenarios,\n * particularly by the `@angular/ssr` package, to manage whether the root component\n * should be bootstrapped during the application initialization process.\n *\n * ## Purpose:\n * During SSR route extraction, setting this token to `false` prevents Angular from\n * bootstrapping the root component. This avoids unnecessary component rendering,\n * enabling route extraction without requiring additional APIs or triggering\n * component logic.\n *\n * ## Behavior:\n * - **`false`**: Prevents the root component from being bootstrapped.\n * - **`true`** (default): Proceeds with the normal root component bootstrap process.\n *\n * This mechanism ensures SSR can efficiently separate route extraction logic\n * from component rendering.\n */\nconst ENABLE_ROOT_COMPONENT_BOOTSTRAP = /*#__PURE__*/new InjectionToken(ngDevMode ? 'ENABLE_ROOT_COMPONENT_BOOTSTRAP' : '');\nfunction isApplicationBootstrapConfig(config) {\n  return !config.moduleRef;\n}\nfunction bootstrap(config) {\n  const envInjector = isApplicationBootstrapConfig(config) ? config.r3Injector : config.moduleRef.injector;\n  const ngZone = envInjector.get(NgZone);\n  return ngZone.run(() => {\n    if (isApplicationBootstrapConfig(config)) {\n      config.r3Injector.resolveInjectorInitializers();\n    } else {\n      config.moduleRef.resolveInjectorInitializers();\n    }\n    const exceptionHandler = envInjector.get(INTERNAL_APPLICATION_ERROR_HANDLER);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (envInjector.get(PROVIDED_ZONELESS) && envInjector.get(PROVIDED_NG_ZONE)) {\n        throw new RuntimeError(408 /* RuntimeErrorCode.PROVIDED_BOTH_ZONE_AND_ZONELESS */, 'Invalid change detection configuration: ' + 'provideZoneChangeDetection and provideZonelessChangeDetection cannot be used together.');\n      }\n    }\n    let onErrorSubscription;\n    ngZone.runOutsideAngular(() => {\n      onErrorSubscription = ngZone.onError.subscribe({\n        next: exceptionHandler\n      });\n    });\n    // If the whole platform is destroyed, invoke the `destroy` method\n    // for all bootstrapped applications as well.\n    if (isApplicationBootstrapConfig(config)) {\n      const destroyListener = () => envInjector.destroy();\n      const onPlatformDestroyListeners = config.platformInjector.get(PLATFORM_DESTROY_LISTENERS);\n      onPlatformDestroyListeners.add(destroyListener);\n      envInjector.onDestroy(() => {\n        onErrorSubscription.unsubscribe();\n        onPlatformDestroyListeners.delete(destroyListener);\n      });\n    } else {\n      const destroyListener = () => config.moduleRef.destroy();\n      const onPlatformDestroyListeners = config.platformInjector.get(PLATFORM_DESTROY_LISTENERS);\n      onPlatformDestroyListeners.add(destroyListener);\n      config.moduleRef.onDestroy(() => {\n        remove(config.allPlatformModules, config.moduleRef);\n        onErrorSubscription.unsubscribe();\n        onPlatformDestroyListeners.delete(destroyListener);\n      });\n    }\n    return _callAndReportToErrorHandler(exceptionHandler, ngZone, () => {\n      const initStatus = envInjector.get(ApplicationInitStatus);\n      initStatus.runInitializers();\n      return initStatus.donePromise.then(() => {\n        // If the `LOCALE_ID` provider is defined at bootstrap then we set the value for ivy\n        const localeId = envInjector.get(LOCALE_ID, DEFAULT_LOCALE_ID);\n        setLocaleId(localeId || DEFAULT_LOCALE_ID);\n        const enableRootComponentBoostrap = envInjector.get(ENABLE_ROOT_COMPONENT_BOOTSTRAP, true);\n        if (!enableRootComponentBoostrap) {\n          if (isApplicationBootstrapConfig(config)) {\n            return envInjector.get(ApplicationRef);\n          }\n          config.allPlatformModules.push(config.moduleRef);\n          return config.moduleRef;\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          const imagePerformanceService = envInjector.get(ImagePerformanceWarning);\n          imagePerformanceService.start();\n        }\n        if (isApplicationBootstrapConfig(config)) {\n          const appRef = envInjector.get(ApplicationRef);\n          if (config.rootComponent !== undefined) {\n            appRef.bootstrap(config.rootComponent);\n          }\n          return appRef;\n        } else {\n          moduleBootstrapImpl?.(config.moduleRef, config.allPlatformModules);\n          return config.moduleRef;\n        }\n      });\n    });\n  });\n}\n/**\n * Having a separate symbol for the module boostrap implementation allows us to\n * tree shake the module based boostrap implementation in standalone apps.\n */\nlet moduleBootstrapImpl;\n/**\n * Set the implementation of the module based bootstrap.\n */\nfunction setModuleBootstrapImpl() {\n  moduleBootstrapImpl = _moduleDoBootstrap;\n}\nfunction _moduleDoBootstrap(moduleRef, allPlatformModules) {\n  const appRef = moduleRef.injector.get(ApplicationRef);\n  if (moduleRef._bootstrapComponents.length > 0) {\n    moduleRef._bootstrapComponents.forEach(f => appRef.bootstrap(f));\n  } else if (moduleRef.instance.ngDoBootstrap) {\n    moduleRef.instance.ngDoBootstrap(appRef);\n  } else {\n    throw new RuntimeError(-403 /* RuntimeErrorCode.BOOTSTRAP_COMPONENTS_NOT_FOUND */, ngDevMode && `The module ${stringify(moduleRef.instance.constructor)} was bootstrapped, ` + `but it does not declare \"@NgModule.bootstrap\" components nor a \"ngDoBootstrap\" method. ` + `Please define one of these.`);\n  }\n  allPlatformModules.push(moduleRef);\n}\nfunction _callAndReportToErrorHandler(errorHandler, ngZone, callback) {\n  try {\n    const result = callback();\n    if (isPromise(result)) {\n      return result.catch(e => {\n        ngZone.runOutsideAngular(() => errorHandler(e));\n        // rethrow as the exception handler might not do it\n        throw e;\n      });\n    }\n    return result;\n  } catch (e) {\n    ngZone.runOutsideAngular(() => errorHandler(e));\n    // rethrow as the exception handler might not do it\n    throw e;\n  }\n}\n\n/**\n * The Angular platform is the entry point for Angular on a web page.\n * Each page has exactly one platform. Services (such as reflection) which are common\n * to every Angular application running on the page are bound in its scope.\n * A page's platform is initialized implicitly when a platform is created using a platform\n * factory such as `PlatformBrowser`, or explicitly by calling the `createPlatform()` function.\n *\n * @publicApi\n */\nlet PlatformRef = /*#__PURE__*/(() => {\n  class PlatformRef {\n    _injector;\n    _modules = [];\n    _destroyListeners = [];\n    _destroyed = false;\n    /** @internal */\n    constructor(_injector) {\n      this._injector = _injector;\n    }\n    /**\n     * Creates an instance of an `@NgModule` for the given platform.\n     *\n     * @deprecated Passing NgModule factories as the `PlatformRef.bootstrapModuleFactory` function\n     *     argument is deprecated. Use the `PlatformRef.bootstrapModule` API instead.\n     */\n    bootstrapModuleFactory(moduleFactory, options) {\n      const scheduleInRootZone = options?.scheduleInRootZone;\n      const ngZoneFactory = () => getNgZone(options?.ngZone, {\n        ...getNgZoneOptions({\n          eventCoalescing: options?.ngZoneEventCoalescing,\n          runCoalescing: options?.ngZoneRunCoalescing\n        }),\n        scheduleInRootZone\n      });\n      const ignoreChangesOutsideZone = options?.ignoreChangesOutsideZone;\n      const allAppProviders = [internalProvideZoneChangeDetection({\n        ngZoneFactory,\n        ignoreChangesOutsideZone\n      }), {\n        provide: ChangeDetectionScheduler,\n        useExisting: ChangeDetectionSchedulerImpl\n      }, errorHandlerEnvironmentInitializer];\n      const moduleRef = createNgModuleRefWithProviders(moduleFactory.moduleType, this.injector, allAppProviders);\n      setModuleBootstrapImpl();\n      return bootstrap({\n        moduleRef,\n        allPlatformModules: this._modules,\n        platformInjector: this.injector\n      });\n    }\n    /**\n     * Creates an instance of an `@NgModule` for a given platform.\n     *\n     * @usageNotes\n     * ### Simple Example\n     *\n     * ```ts\n     * @NgModule({\n     *   imports: [BrowserModule]\n     * })\n     * class MyModule {}\n     *\n     * let moduleRef = platformBrowser().bootstrapModule(MyModule);\n     * ```\n     *\n     */\n    bootstrapModule(moduleType, compilerOptions = []) {\n      const options = optionsReducer({}, compilerOptions);\n      setModuleBootstrapImpl();\n      return compileNgModuleFactory(this.injector, options, moduleType).then(moduleFactory => this.bootstrapModuleFactory(moduleFactory, options));\n    }\n    /**\n     * Registers a listener to be called when the platform is destroyed.\n     */\n    onDestroy(callback) {\n      this._destroyListeners.push(callback);\n    }\n    /**\n     * Retrieves the platform {@link Injector}, which is the parent injector for\n     * every Angular application on the page and provides singleton providers.\n     */\n    get injector() {\n      return this._injector;\n    }\n    /**\n     * Destroys the current Angular platform and all Angular applications on the page.\n     * Destroys all modules and listeners registered with the platform.\n     */\n    destroy() {\n      if (this._destroyed) {\n        throw new RuntimeError(404 /* RuntimeErrorCode.PLATFORM_ALREADY_DESTROYED */, ngDevMode && 'The platform has already been destroyed!');\n      }\n      this._modules.slice().forEach(module => module.destroy());\n      this._destroyListeners.forEach(listener => listener());\n      const destroyListeners = this._injector.get(PLATFORM_DESTROY_LISTENERS, null);\n      if (destroyListeners) {\n        destroyListeners.forEach(listener => listener());\n        destroyListeners.clear();\n      }\n      this._destroyed = true;\n    }\n    /**\n     * Indicates whether this instance was destroyed.\n     */\n    get destroyed() {\n      return this._destroyed;\n    }\n    static ɵfac = function PlatformRef_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlatformRef)(__inject(Injector));\n    };\n    static ɵprov = /*@__PURE__*/__defineInjectable({\n      token: PlatformRef,\n      factory: PlatformRef.ɵfac,\n      providedIn: 'platform'\n    });\n  }\n  return PlatformRef;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && setClassMetadata(PlatformRef, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform'\n    }]\n  }], () => [{\n    type: Injector\n  }], null);\n})();\nlet _platformInjector = null;\n/**\n * Internal token to indicate whether having multiple bootstrapped platform should be allowed (only\n * one bootstrapped platform is allowed by default). This token helps to support SSR scenarios.\n */\nconst ALLOW_MULTIPLE_PLATFORMS = /*#__PURE__*/new InjectionToken(ngDevMode ? 'AllowMultipleToken' : '');\n/**\n * Creates a platform.\n * Platforms must be created on launch using this function.\n *\n * @publicApi\n */\nfunction createPlatform(injector) {\n  if (_platformInjector && !_platformInjector.get(ALLOW_MULTIPLE_PLATFORMS, false)) {\n    throw new RuntimeError(400 /* RuntimeErrorCode.MULTIPLE_PLATFORMS */, ngDevMode && 'There can be only one platform. Destroy the previous one to create a new one.');\n  }\n  publishDefaultGlobalUtils();\n  publishSignalConfiguration();\n  _platformInjector = injector;\n  const platform = injector.get(PlatformRef);\n  runPlatformInitializers(injector);\n  return platform;\n}\n/**\n * Creates a factory for a platform. Can be used to provide or override `Providers` specific to\n * your application's runtime needs, such as `PLATFORM_INITIALIZER` and `PLATFORM_ID`.\n * @param parentPlatformFactory Another platform factory to modify. Allows you to compose factories\n * to build up configurations that might be required by different libraries or parts of the\n * application.\n * @param name Identifies the new platform factory.\n * @param providers A set of dependency providers for platforms created with the new factory.\n *\n * @publicApi\n */\nfunction createPlatformFactory(parentPlatformFactory, name, providers = []) {\n  const desc = `Platform: ${name}`;\n  const marker = new InjectionToken(desc);\n  return (extraProviders = []) => {\n    let platform = getPlatform();\n    if (!platform || platform.injector.get(ALLOW_MULTIPLE_PLATFORMS, false)) {\n      const platformProviders = [...providers, ...extraProviders, {\n        provide: marker,\n        useValue: true\n      }];\n      if (parentPlatformFactory) {\n        parentPlatformFactory(platformProviders);\n      } else {\n        createPlatform(createPlatformInjector(platformProviders, desc));\n      }\n    }\n    return assertPlatform(marker);\n  };\n}\n/**\n * Helper function to create an instance of a platform injector (that maintains the 'platform'\n * scope).\n */\nfunction createPlatformInjector(providers = [], name) {\n  return Injector.create({\n    name,\n    providers: [{\n      provide: INJECTOR_SCOPE,\n      useValue: 'platform'\n    }, {\n      provide: PLATFORM_DESTROY_LISTENERS,\n      useValue: new Set([() => _platformInjector = null])\n    }, ...providers]\n  });\n}\n/**\n * Checks that there is currently a platform that contains the given token as a provider.\n *\n * @publicApi\n */\nfunction assertPlatform(requiredToken) {\n  const platform = getPlatform();\n  if (!platform) {\n    throw new RuntimeError(401 /* RuntimeErrorCode.PLATFORM_NOT_FOUND */, ngDevMode && 'No platform exists!');\n  }\n  if ((typeof ngDevMode === 'undefined' || ngDevMode) && !platform.injector.get(requiredToken, null)) {\n    throw new RuntimeError(400 /* RuntimeErrorCode.MULTIPLE_PLATFORMS */, 'A platform with a different configuration has been created. Please destroy it first.');\n  }\n  return platform;\n}\n/**\n * Returns the current platform.\n *\n * @publicApi\n */\nfunction getPlatform() {\n  return _platformInjector?.get(PlatformRef) ?? null;\n}\n/**\n * Destroys the current Angular platform and all Angular applications on the page.\n * Destroys all modules and listeners registered with the platform.\n *\n * @publicApi\n */\nfunction destroyPlatform() {\n  getPlatform()?.destroy();\n}\n/**\n * The goal of this function is to bootstrap a platform injector,\n * but avoid referencing `PlatformRef` class.\n * This function is needed for bootstrapping a Standalone Component.\n */\nfunction createOrReusePlatformInjector(providers = []) {\n  // If a platform injector already exists, it means that the platform\n  // is already bootstrapped and no additional actions are required.\n  if (_platformInjector) return _platformInjector;\n  publishDefaultGlobalUtils();\n  // Otherwise, setup a new platform injector and run platform initializers.\n  const injector = createPlatformInjector(providers);\n  _platformInjector = injector;\n  publishSignalConfiguration();\n  runPlatformInitializers(injector);\n  return injector;\n}\n/**\n * @description\n * This function is used to provide initialization functions that will be executed upon\n * initialization of the platform injector.\n *\n * Note that the provided initializer is run in the injection context.\n *\n * Previously, this was achieved using the `PLATFORM_INITIALIZER` token which is now deprecated.\n *\n * @see {@link PLATFORM_INITIALIZER}\n *\n * @publicApi\n */\nfunction providePlatformInitializer(initializerFn) {\n  return makeEnvironmentProviders([{\n    provide: PLATFORM_INITIALIZER,\n    useValue: initializerFn,\n    multi: true\n  }]);\n}\nfunction runPlatformInitializers(injector) {\n  const inits = injector.get(PLATFORM_INITIALIZER, null);\n  runInInjectionContext(injector, () => {\n    inits?.forEach(init => init());\n  });\n}\nfunction exhaustiveCheckNoChangesInterval(interval) {\n  return provideEnvironmentInitializer(() => {\n    const applicationRef = inject(ApplicationRef);\n    const errorHandler = inject(ErrorHandler);\n    const scheduler = inject(ChangeDetectionSchedulerImpl);\n    const ngZone = inject(NgZone);\n    function scheduleCheckNoChanges() {\n      ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          if (applicationRef.destroyed) {\n            return;\n          }\n          if (scheduler.pendingRenderTaskId || scheduler.runningTick) {\n            scheduleCheckNoChanges();\n            return;\n          }\n          for (const view of applicationRef.allViews) {\n            try {\n              checkNoChangesInternal(view._lView, true /** exhaustive */);\n            } catch (e) {\n              errorHandler.handleError(e);\n            }\n          }\n          scheduleCheckNoChanges();\n        }, interval);\n      });\n    }\n    scheduleCheckNoChanges();\n  });\n}\nfunction provideCheckNoChangesConfig(options) {\n  return makeEnvironmentProviders(typeof ngDevMode === 'undefined' || ngDevMode ? [{\n    provide: UseExhaustiveCheckNoChanges,\n    useValue: options.exhaustive\n  }, options?.interval !== undefined ? exhaustiveCheckNoChangesInterval(options.interval) : []] : []);\n}\n\n/**\n * Returns whether Angular is in development mode.\n *\n * By default, this is true, unless `enableProdMode` is invoked prior to calling this method or the\n * application is built using the Angular CLI with the `optimization` option.\n * @see {@link /cli/build ng build}\n *\n * @publicApi\n */\nfunction isDevMode() {\n  return typeof ngDevMode === 'undefined' || !!ngDevMode;\n}\n/**\n * Disable Angular's development mode, which turns off assertions and other\n * checks within the framework.\n *\n * One important assertion this disables verifies that a change detection pass\n * does not result in additional changes to any bindings (also known as\n * unidirectional data flow).\n *\n * Using this method is discouraged as the Angular CLI will set production mode when using the\n * `optimization` option.\n * @see {@link /cli/build ng build}\n *\n * @publicApi\n */\nfunction enableProdMode() {\n  // The below check is there so when ngDevMode is set via terser\n  // `global['ngDevMode'] = false;` is also dropped.\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    _global['ngDevMode'] = false;\n  }\n}\n\n/**\n * Returns the NgModuleFactory with the given id (specified using [@NgModule.id\n * field](api/core/NgModule#id)), if it exists and has been loaded. Factories for NgModules that do\n * not specify an `id` cannot be retrieved. Throws if an NgModule cannot be found.\n * @publicApi\n * @deprecated Use `getNgModuleById` instead.\n */\nfunction getModuleFactory(id) {\n  const type = getRegisteredNgModuleType(id);\n  if (!type) throw noModuleError(id);\n  return new NgModuleFactory(type);\n}\n/**\n * Returns the NgModule class with the given id (specified using [@NgModule.id\n * field](api/core/NgModule#id)), if it exists and has been loaded. Classes for NgModules that do\n * not specify an `id` cannot be retrieved. Throws if an NgModule cannot be found.\n * @publicApi\n */\nfunction getNgModuleById(id) {\n  const type = getRegisteredNgModuleType(id);\n  if (!type) throw noModuleError(id);\n  return type;\n}\nfunction noModuleError(id) {\n  return new Error(`No module with ID ${id} loaded`);\n}\n\n/**\n * Base class that provides change detection functionality.\n * A change-detection tree collects all views that are to be checked for changes.\n * Use the methods to add and remove views from the tree, initiate change-detection,\n * and explicitly mark views as _dirty_, meaning that they have changed and need to be re-rendered.\n *\n * @see [Using change detection hooks](guide/components/lifecycle#using-change-detection-hooks)\n * @see [Defining custom change detection](guide/components/lifecycle#defining-custom-change-detection)\n *\n * @usageNotes\n *\n * The following examples demonstrate how to modify default change-detection behavior\n * to perform explicit detection when needed.\n *\n * ### Use `markForCheck()` with `CheckOnce` strategy\n *\n * The following example sets the `OnPush` change-detection strategy for a component\n * (`CheckOnce`, rather than the default `CheckAlways`), then forces a second check\n * after an interval.\n *\n * {@example core/ts/change_detect/change-detection.ts region='mark-for-check'}\n *\n * ### Detach change detector to limit how often check occurs\n *\n * The following example defines a component with a large list of read-only data\n * that is expected to change constantly, many times per second.\n * To improve performance, we want to check and update the list\n * less often than the changes actually occur. To do that, we detach\n * the component's change detector and perform an explicit local check every five seconds.\n *\n * {@example core/ts/change_detect/change-detection.ts region='detach'}\n *\n *\n * ### Reattaching a detached component\n *\n * The following example creates a component displaying live data.\n * The component detaches its change detector from the main change detector tree\n * when the `live` property is set to false, and reattaches it when the property\n * becomes true.\n *\n * {@example core/ts/change_detect/change-detection.ts region='reattach'}\n *\n * @publicApi\n */\nlet ChangeDetectorRef = /*#__PURE__*/(() => {\n  class ChangeDetectorRef {\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static __NG_ELEMENT_ID__ = injectChangeDetectorRef;\n  }\n  return ChangeDetectorRef;\n})();\n/** Returns a ChangeDetectorRef (a.k.a. a ViewRef) */\nfunction injectChangeDetectorRef(flags) {\n  return createViewRef(getCurrentTNode(), getLView(), (flags & 16 /* InternalInjectFlags.ForPipe */) === 16 /* InternalInjectFlags.ForPipe */);\n}\n/**\n * Creates a ViewRef and stores it on the injector as ChangeDetectorRef (public alias).\n *\n * @param tNode The node that is requesting a ChangeDetectorRef\n * @param lView The view to which the node belongs\n * @param isPipe Whether the view is being injected into a pipe.\n * @returns The ChangeDetectorRef to use\n */\nfunction createViewRef(tNode, lView, isPipe) {\n  if (isComponentHost(tNode) && !isPipe) {\n    // The LView represents the location where the component is declared.\n    // Instead we want the LView for the component View and so we need to look it up.\n    const componentView = getComponentLViewByIndex(tNode.index, lView); // look down\n    return new ViewRef$1(componentView, componentView);\n  } else if (tNode.type & (3 /* TNodeType.AnyRNode */ | 12 /* TNodeType.AnyContainer */ | 32 /* TNodeType.Icu */ | 128 /* TNodeType.LetDeclaration */)) {\n    // The LView represents the location where the injection is requested from.\n    // We need to locate the containing LView (in case where the `lView` is an embedded view)\n    const hostComponentView = lView[DECLARATION_COMPONENT_VIEW]; // look up\n    return new ViewRef$1(hostComponentView, lView);\n  }\n  return null;\n}\n\n/**\n * Represents an Angular view.\n *\n * @see {@link /api/core/ChangeDetectorRef?tab=usage-notes Change detection usage}\n *\n * @publicApi\n */\nclass ViewRef extends ChangeDetectorRef {}\n/**\n * Represents an Angular view in a view container.\n * An embedded view can be referenced from a component\n * other than the hosting component whose template defines it, or it can be defined\n * independently by a `TemplateRef`.\n *\n * Properties of elements in a view can change, but the structure (number and order) of elements in\n * a view cannot. Change the structure of elements by inserting, moving, or\n * removing nested views in a view container.\n *\n * @see {@link ViewContainerRef}\n *\n * @usageNotes\n *\n * The following template breaks down into two separate `TemplateRef` instances,\n * an outer one and an inner one.\n *\n * ```html\n * Count: {{items.length}}\n * <ul>\n *   <li *ngFor=\"let  item of items\">{{item}}</li>\n * </ul>\n * ```\n *\n * This is the outer `TemplateRef`:\n *\n * ```html\n * Count: {{items.length}}\n * <ul>\n *   <ng-template ngFor let-item [ngForOf]=\"items\"></ng-template>\n * </ul>\n * ```\n *\n * This is the inner `TemplateRef`:\n *\n * ```html\n *   <li>{{item}}</li>\n * ```\n *\n * The outer and inner `TemplateRef` instances are assembled into views as follows:\n *\n * ```html\n * <!-- ViewRef: outer-0 -->\n * Count: 2\n * <ul>\n *   <ng-template view-container-ref></ng-template>\n *   <!-- ViewRef: inner-1 --><li>first</li><!-- /ViewRef: inner-1 -->\n *   <!-- ViewRef: inner-2 --><li>second</li><!-- /ViewRef: inner-2 -->\n * </ul>\n * <!-- /ViewRef: outer-0 -->\n * ```\n * @publicApi\n */\nclass EmbeddedViewRef extends ViewRef {}\nclass DefaultIterableDifferFactory {\n  constructor() {}\n  supports(obj) {\n    return isListLikeIterable(obj);\n  }\n  create(trackByFn) {\n    return new DefaultIterableDiffer(trackByFn);\n  }\n}\nconst trackByIdentity = (index, item) => item;\n/**\n * @deprecated v4.0.0 - Should not be part of public API.\n * @publicApi\n */\nclass DefaultIterableDiffer {\n  length = 0;\n  // TODO: confirm the usage of `collection` as it's unused, readonly and on a non public API.\n  collection;\n  // Keeps track of the used records at any point in time (during & across `_check()` calls)\n  _linkedRecords = null;\n  // Keeps track of the removed records at any point in time during `_check()` calls.\n  _unlinkedRecords = null;\n  _previousItHead = null;\n  _itHead = null;\n  _itTail = null;\n  _additionsHead = null;\n  _additionsTail = null;\n  _movesHead = null;\n  _movesTail = null;\n  _removalsHead = null;\n  _removalsTail = null;\n  // Keeps track of records where custom track by is the same, but item identity has changed\n  _identityChangesHead = null;\n  _identityChangesTail = null;\n  _trackByFn;\n  constructor(trackByFn) {\n    this._trackByFn = trackByFn || trackByIdentity;\n  }\n  forEachItem(fn) {\n    let record;\n    for (record = this._itHead; record !== null; record = record._next) {\n      fn(record);\n    }\n  }\n  forEachOperation(fn) {\n    let nextIt = this._itHead;\n    let nextRemove = this._removalsHead;\n    let addRemoveOffset = 0;\n    let moveOffsets = null;\n    while (nextIt || nextRemove) {\n      // Figure out which is the next record to process\n      // Order: remove, add, move\n      const record = !nextRemove || nextIt && nextIt.currentIndex < getPreviousIndex(nextRemove, addRemoveOffset, moveOffsets) ? nextIt : nextRemove;\n      const adjPreviousIndex = getPreviousIndex(record, addRemoveOffset, moveOffsets);\n      const currentIndex = record.currentIndex;\n      // consume the item, and adjust the addRemoveOffset and update moveDistance if necessary\n      if (record === nextRemove) {\n        addRemoveOffset--;\n        nextRemove = nextRemove._nextRemoved;\n      } else {\n        nextIt = nextIt._next;\n        if (record.previousIndex == null) {\n          addRemoveOffset++;\n        } else {\n          // INVARIANT:  currentIndex < previousIndex\n          if (!moveOffsets) moveOffsets = [];\n          const localMovePreviousIndex = adjPreviousIndex - addRemoveOffset;\n          const localCurrentIndex = currentIndex - addRemoveOffset;\n          if (localMovePreviousIndex != localCurrentIndex) {\n            for (let i = 0; i < localMovePreviousIndex; i++) {\n              const offset = i < moveOffsets.length ? moveOffsets[i] : moveOffsets[i] = 0;\n              const index = offset + i;\n              if (localCurrentIndex <= index && index < localMovePreviousIndex) {\n                moveOffsets[i] = offset + 1;\n              }\n            }\n            const previousIndex = record.previousIndex;\n            moveOffsets[previousIndex] = localCurrentIndex - localMovePreviousIndex;\n          }\n        }\n      }\n      if (adjPreviousIndex !== currentIndex) {\n        fn(record, adjPreviousIndex, currentIndex);\n      }\n    }\n  }\n  forEachPreviousItem(fn) {\n    let record;\n    for (record = this._previousItHead; record !== null; record = record._nextPrevious) {\n      fn(record);\n    }\n  }\n  forEachAddedItem(fn) {\n    let record;\n    for (record = this._additionsHead; record !== null; record = record._nextAdded) {\n      fn(record);\n    }\n  }\n  forEachMovedItem(fn) {\n    let record;\n    for (record = this._movesHead; record !== null; record = record._nextMoved) {\n      fn(record);\n    }\n  }\n  forEachRemovedItem(fn) {\n    let record;\n    for (record = this._removalsHead; record !== null; record = record._nextRemoved) {\n      fn(record);\n    }\n  }\n  forEachIdentityChange(fn) {\n    let record;\n    for (record = this._identityChangesHead; record !== null; record = record._nextIdentityChange) {\n      fn(record);\n    }\n  }\n  diff(collection) {\n    if (collection == null) collection = [];\n    if (!isListLikeIterable(collection)) {\n      throw new RuntimeError(900 /* RuntimeErrorCode.INVALID_DIFFER_INPUT */, ngDevMode && `Error trying to diff '${stringify(collection)}'. Only arrays and iterables are allowed`);\n    }\n    if (this.check(collection)) {\n      return this;\n    } else {\n      return null;\n    }\n  }\n  onDestroy() {}\n  check(collection) {\n    this._reset();\n    let record = this._itHead;\n    let mayBeDirty = false;\n    let index;\n    let item;\n    let itemTrackBy;\n    if (Array.isArray(collection)) {\n      this.length = collection.length;\n      for (let index = 0; index < this.length; index++) {\n        item = collection[index];\n        itemTrackBy = this._trackByFn(index, item);\n        if (record === null || !Object.is(record.trackById, itemTrackBy)) {\n          record = this._mismatch(record, item, itemTrackBy, index);\n          mayBeDirty = true;\n        } else {\n          if (mayBeDirty) {\n            // TODO(misko): can we limit this to duplicates only?\n            record = this._verifyReinsertion(record, item, itemTrackBy, index);\n          }\n          if (!Object.is(record.item, item)) this._addIdentityChange(record, item);\n        }\n        record = record._next;\n      }\n    } else {\n      index = 0;\n      iterateListLike(collection, item => {\n        itemTrackBy = this._trackByFn(index, item);\n        if (record === null || !Object.is(record.trackById, itemTrackBy)) {\n          record = this._mismatch(record, item, itemTrackBy, index);\n          mayBeDirty = true;\n        } else {\n          if (mayBeDirty) {\n            // TODO(misko): can we limit this to duplicates only?\n            record = this._verifyReinsertion(record, item, itemTrackBy, index);\n          }\n          if (!Object.is(record.item, item)) this._addIdentityChange(record, item);\n        }\n        record = record._next;\n        index++;\n      });\n      this.length = index;\n    }\n    this._truncate(record);\n    this.collection = collection;\n    return this.isDirty;\n  }\n  /* CollectionChanges is considered dirty if it has any additions, moves, removals, or identity\n   * changes.\n   */\n  get isDirty() {\n    return this._additionsHead !== null || this._movesHead !== null || this._removalsHead !== null || this._identityChangesHead !== null;\n  }\n  /**\n   * Reset the state of the change objects to show no changes. This means set previousKey to\n   * currentKey, and clear all of the queues (additions, moves, removals).\n   * Set the previousIndexes of moved and added items to their currentIndexes\n   * Reset the list of additions, moves and removals\n   *\n   * @internal\n   */\n  _reset() {\n    if (this.isDirty) {\n      let record;\n      for (record = this._previousItHead = this._itHead; record !== null; record = record._next) {\n        record._nextPrevious = record._next;\n      }\n      for (record = this._additionsHead; record !== null; record = record._nextAdded) {\n        record.previousIndex = record.currentIndex;\n      }\n      this._additionsHead = this._additionsTail = null;\n      for (record = this._movesHead; record !== null; record = record._nextMoved) {\n        record.previousIndex = record.currentIndex;\n      }\n      this._movesHead = this._movesTail = null;\n      this._removalsHead = this._removalsTail = null;\n      this._identityChangesHead = this._identityChangesTail = null;\n      // TODO(vicb): when assert gets supported\n      // assert(!this.isDirty);\n    }\n  }\n  /**\n   * This is the core function which handles differences between collections.\n   *\n   * - `record` is the record which we saw at this position last time. If null then it is a new\n   *   item.\n   * - `item` is the current item in the collection\n   * - `index` is the position of the item in the collection\n   *\n   * @internal\n   */\n  _mismatch(record, item, itemTrackBy, index) {\n    // The previous record after which we will append the current one.\n    let previousRecord;\n    if (record === null) {\n      previousRecord = this._itTail;\n    } else {\n      previousRecord = record._prev;\n      // Remove the record from the collection since we know it does not match the item.\n      this._remove(record);\n    }\n    // See if we have evicted the item, which used to be at some anterior position of _itHead list.\n    record = this._unlinkedRecords === null ? null : this._unlinkedRecords.get(itemTrackBy, null);\n    if (record !== null) {\n      // It is an item which we have evicted earlier: reinsert it back into the list.\n      // But first we need to check if identity changed, so we can update in view if necessary.\n      if (!Object.is(record.item, item)) this._addIdentityChange(record, item);\n      this._reinsertAfter(record, previousRecord, index);\n    } else {\n      // Attempt to see if the item is at some posterior position of _itHead list.\n      record = this._linkedRecords === null ? null : this._linkedRecords.get(itemTrackBy, index);\n      if (record !== null) {\n        // We have the item in _itHead at/after `index` position. We need to move it forward in the\n        // collection.\n        // But first we need to check if identity changed, so we can update in view if necessary.\n        if (!Object.is(record.item, item)) this._addIdentityChange(record, item);\n        this._moveAfter(record, previousRecord, index);\n      } else {\n        // It is a new item: add it.\n        record = this._addAfter(new IterableChangeRecord_(item, itemTrackBy), previousRecord, index);\n      }\n    }\n    return record;\n  }\n  /**\n   * This check is only needed if an array contains duplicates. (Short circuit of nothing dirty)\n   *\n   * Use case: `[a, a]` => `[b, a, a]`\n   *\n   * If we did not have this check then the insertion of `b` would:\n   *   1) evict first `a`\n   *   2) insert `b` at `0` index.\n   *   3) leave `a` at index `1` as is. <-- this is wrong!\n   *   3) reinsert `a` at index 2. <-- this is wrong!\n   *\n   * The correct behavior is:\n   *   1) evict first `a`\n   *   2) insert `b` at `0` index.\n   *   3) reinsert `a` at index 1.\n   *   3) move `a` at from `1` to `2`.\n   *\n   *\n   * Double check that we have not evicted a duplicate item. We need to check if the item type may\n   * have already been removed:\n   * The insertion of b will evict the first 'a'. If we don't reinsert it now it will be reinserted\n   * at the end. Which will show up as the two 'a's switching position. This is incorrect, since a\n   * better way to think of it is as insert of 'b' rather then switch 'a' with 'b' and then add 'a'\n   * at the end.\n   *\n   * @internal\n   */\n  _verifyReinsertion(record, item, itemTrackBy, index) {\n    let reinsertRecord = this._unlinkedRecords === null ? null : this._unlinkedRecords.get(itemTrackBy, null);\n    if (reinsertRecord !== null) {\n      record = this._reinsertAfter(reinsertRecord, record._prev, index);\n    } else if (record.currentIndex != index) {\n      record.currentIndex = index;\n      this._addToMoves(record, index);\n    }\n    return record;\n  }\n  /**\n   * Get rid of any excess {@link IterableChangeRecord_}s from the previous collection\n   *\n   * - `record` The first excess {@link IterableChangeRecord_}.\n   *\n   * @internal\n   */\n  _truncate(record) {\n    // Anything after that needs to be removed;\n    while (record !== null) {\n      const nextRecord = record._next;\n      this._addToRemovals(this._unlink(record));\n      record = nextRecord;\n    }\n    if (this._unlinkedRecords !== null) {\n      this._unlinkedRecords.clear();\n    }\n    if (this._additionsTail !== null) {\n      this._additionsTail._nextAdded = null;\n    }\n    if (this._movesTail !== null) {\n      this._movesTail._nextMoved = null;\n    }\n    if (this._itTail !== null) {\n      this._itTail._next = null;\n    }\n    if (this._removalsTail !== null) {\n      this._removalsTail._nextRemoved = null;\n    }\n    if (this._identityChangesTail !== null) {\n      this._identityChangesTail._nextIdentityChange = null;\n    }\n  }\n  /** @internal */\n  _reinsertAfter(record, prevRecord, index) {\n    if (this._unlinkedRecords !== null) {\n      this._unlinkedRecords.remove(record);\n    }\n    const prev = record._prevRemoved;\n    const next = record._nextRemoved;\n    if (prev === null) {\n      this._removalsHead = next;\n    } else {\n      prev._nextRemoved = next;\n    }\n    if (next === null) {\n      this._removalsTail = prev;\n    } else {\n      next._prevRemoved = prev;\n    }\n    this._insertAfter(record, prevRecord, index);\n    this._addToMoves(record, index);\n    return record;\n  }\n  /** @internal */\n  _moveAfter(record, prevRecord, index) {\n    this._unlink(record);\n    this._insertAfter(record, prevRecord, index);\n    this._addToMoves(record, index);\n    return record;\n  }\n  /** @internal */\n  _addAfter(record, prevRecord, index) {\n    this._insertAfter(record, prevRecord, index);\n    if (this._additionsTail === null) {\n      // TODO(vicb):\n      // assert(this._additionsHead === null);\n      this._additionsTail = this._additionsHead = record;\n    } else {\n      // TODO(vicb):\n      // assert(_additionsTail._nextAdded === null);\n      // assert(record._nextAdded === null);\n      this._additionsTail = this._additionsTail._nextAdded = record;\n    }\n    return record;\n  }\n  /** @internal */\n  _insertAfter(record, prevRecord, index) {\n    // TODO(vicb):\n    // assert(record != prevRecord);\n    // assert(record._next === null);\n    // assert(record._prev === null);\n    const next = prevRecord === null ? this._itHead : prevRecord._next;\n    // TODO(vicb):\n    // assert(next != record);\n    // assert(prevRecord != record);\n    record._next = next;\n    record._prev = prevRecord;\n    if (next === null) {\n      this._itTail = record;\n    } else {\n      next._prev = record;\n    }\n    if (prevRecord === null) {\n      this._itHead = record;\n    } else {\n      prevRecord._next = record;\n    }\n    if (this._linkedRecords === null) {\n      this._linkedRecords = new _DuplicateMap();\n    }\n    this._linkedRecords.put(record);\n    record.currentIndex = index;\n    return record;\n  }\n  /** @internal */\n  _remove(record) {\n    return this._addToRemovals(this._unlink(record));\n  }\n  /** @internal */\n  _unlink(record) {\n    if (this._linkedRecords !== null) {\n      this._linkedRecords.remove(record);\n    }\n    const prev = record._prev;\n    const next = record._next;\n    // TODO(vicb):\n    // assert((record._prev = null) === null);\n    // assert((record._next = null) === null);\n    if (prev === null) {\n      this._itHead = next;\n    } else {\n      prev._next = next;\n    }\n    if (next === null) {\n      this._itTail = prev;\n    } else {\n      next._prev = prev;\n    }\n    return record;\n  }\n  /** @internal */\n  _addToMoves(record, toIndex) {\n    // TODO(vicb):\n    // assert(record._nextMoved === null);\n    if (record.previousIndex === toIndex) {\n      return record;\n    }\n    if (this._movesTail === null) {\n      // TODO(vicb):\n      // assert(_movesHead === null);\n      this._movesTail = this._movesHead = record;\n    } else {\n      // TODO(vicb):\n      // assert(_movesTail._nextMoved === null);\n      this._movesTail = this._movesTail._nextMoved = record;\n    }\n    return record;\n  }\n  _addToRemovals(record) {\n    if (this._unlinkedRecords === null) {\n      this._unlinkedRecords = new _DuplicateMap();\n    }\n    this._unlinkedRecords.put(record);\n    record.currentIndex = null;\n    record._nextRemoved = null;\n    if (this._removalsTail === null) {\n      // TODO(vicb):\n      // assert(_removalsHead === null);\n      this._removalsTail = this._removalsHead = record;\n      record._prevRemoved = null;\n    } else {\n      // TODO(vicb):\n      // assert(_removalsTail._nextRemoved === null);\n      // assert(record._nextRemoved === null);\n      record._prevRemoved = this._removalsTail;\n      this._removalsTail = this._removalsTail._nextRemoved = record;\n    }\n    return record;\n  }\n  /** @internal */\n  _addIdentityChange(record, item) {\n    record.item = item;\n    if (this._identityChangesTail === null) {\n      this._identityChangesTail = this._identityChangesHead = record;\n    } else {\n      this._identityChangesTail = this._identityChangesTail._nextIdentityChange = record;\n    }\n    return record;\n  }\n}\nclass IterableChangeRecord_ {\n  item;\n  trackById;\n  currentIndex = null;\n  previousIndex = null;\n  /** @internal */\n  _nextPrevious = null;\n  /** @internal */\n  _prev = null;\n  /** @internal */\n  _next = null;\n  /** @internal */\n  _prevDup = null;\n  /** @internal */\n  _nextDup = null;\n  /** @internal */\n  _prevRemoved = null;\n  /** @internal */\n  _nextRemoved = null;\n  /** @internal */\n  _nextAdded = null;\n  /** @internal */\n  _nextMoved = null;\n  /** @internal */\n  _nextIdentityChange = null;\n  constructor(item, trackById) {\n    this.item = item;\n    this.trackById = trackById;\n  }\n}\n// A linked list of IterableChangeRecords with the same IterableChangeRecord_.item\nclass _DuplicateItemRecordList {\n  /** @internal */\n  _head = null;\n  /** @internal */\n  _tail = null;\n  /**\n   * Append the record to the list of duplicates.\n   *\n   * Note: by design all records in the list of duplicates hold the same value in record.item.\n   */\n  add(record) {\n    if (this._head === null) {\n      this._head = this._tail = record;\n      record._nextDup = null;\n      record._prevDup = null;\n    } else {\n      // TODO(vicb):\n      // assert(record.item ==  _head.item ||\n      //       record.item is num && record.item.isNaN && _head.item is num && _head.item.isNaN);\n      this._tail._nextDup = record;\n      record._prevDup = this._tail;\n      record._nextDup = null;\n      this._tail = record;\n    }\n  }\n  // Returns a IterableChangeRecord_ having IterableChangeRecord_.trackById == trackById and\n  // IterableChangeRecord_.currentIndex >= atOrAfterIndex\n  get(trackById, atOrAfterIndex) {\n    let record;\n    for (record = this._head; record !== null; record = record._nextDup) {\n      if ((atOrAfterIndex === null || atOrAfterIndex <= record.currentIndex) && Object.is(record.trackById, trackById)) {\n        return record;\n      }\n    }\n    return null;\n  }\n  /**\n   * Remove one {@link IterableChangeRecord_} from the list of duplicates.\n   *\n   * Returns whether the list of duplicates is empty.\n   */\n  remove(record) {\n    // TODO(vicb):\n    // assert(() {\n    //  // verify that the record being removed is in the list.\n    //  for (IterableChangeRecord_ cursor = _head; cursor != null; cursor = cursor._nextDup) {\n    //    if (identical(cursor, record)) return true;\n    //  }\n    //  return false;\n    //});\n    const prev = record._prevDup;\n    const next = record._nextDup;\n    if (prev === null) {\n      this._head = next;\n    } else {\n      prev._nextDup = next;\n    }\n    if (next === null) {\n      this._tail = prev;\n    } else {\n      next._prevDup = prev;\n    }\n    return this._head === null;\n  }\n}\nclass _DuplicateMap {\n  map = /*#__PURE__*/new Map();\n  put(record) {\n    const key = record.trackById;\n    let duplicates = this.map.get(key);\n    if (!duplicates) {\n      duplicates = new _DuplicateItemRecordList();\n      this.map.set(key, duplicates);\n    }\n    duplicates.add(record);\n  }\n  /**\n   * Retrieve the `value` using key. Because the IterableChangeRecord_ value may be one which we\n   * have already iterated over, we use the `atOrAfterIndex` to pretend it is not there.\n   *\n   * Use case: `[a, b, c, a, a]` if we are at index `3` which is the second `a` then asking if we\n   * have any more `a`s needs to return the second `a`.\n   */\n  get(trackById, atOrAfterIndex) {\n    const key = trackById;\n    const recordList = this.map.get(key);\n    return recordList ? recordList.get(trackById, atOrAfterIndex) : null;\n  }\n  /**\n   * Removes a {@link IterableChangeRecord_} from the list of duplicates.\n   *\n   * The list of duplicates also is removed from the map if it gets empty.\n   */\n  remove(record) {\n    const key = record.trackById;\n    const recordList = this.map.get(key);\n    // Remove the list of duplicates when it gets empty\n    if (recordList.remove(record)) {\n      this.map.delete(key);\n    }\n    return record;\n  }\n  get isEmpty() {\n    return this.map.size === 0;\n  }\n  clear() {\n    this.map.clear();\n  }\n}\nfunction getPreviousIndex(item, addRemoveOffset, moveOffsets) {\n  const previousIndex = item.previousIndex;\n  if (previousIndex === null) return previousIndex;\n  let moveOffset = 0;\n  if (moveOffsets && previousIndex < moveOffsets.length) {\n    moveOffset = moveOffsets[previousIndex];\n  }\n  return previousIndex + addRemoveOffset + moveOffset;\n}\nclass DefaultKeyValueDifferFactory {\n  constructor() {}\n  supports(obj) {\n    return obj instanceof Map || isJsObject(obj);\n  }\n  create() {\n    return new DefaultKeyValueDiffer();\n  }\n}\nclass DefaultKeyValueDiffer {\n  _records = /*#__PURE__*/new Map();\n  _mapHead = null;\n  // _appendAfter is used in the check loop\n  _appendAfter = null;\n  _previousMapHead = null;\n  _changesHead = null;\n  _changesTail = null;\n  _additionsHead = null;\n  _additionsTail = null;\n  _removalsHead = null;\n  _removalsTail = null;\n  get isDirty() {\n    return this._additionsHead !== null || this._changesHead !== null || this._removalsHead !== null;\n  }\n  forEachItem(fn) {\n    let record;\n    for (record = this._mapHead; record !== null; record = record._next) {\n      fn(record);\n    }\n  }\n  forEachPreviousItem(fn) {\n    let record;\n    for (record = this._previousMapHead; record !== null; record = record._nextPrevious) {\n      fn(record);\n    }\n  }\n  forEachChangedItem(fn) {\n    let record;\n    for (record = this._changesHead; record !== null; record = record._nextChanged) {\n      fn(record);\n    }\n  }\n  forEachAddedItem(fn) {\n    let record;\n    for (record = this._additionsHead; record !== null; record = record._nextAdded) {\n      fn(record);\n    }\n  }\n  forEachRemovedItem(fn) {\n    let record;\n    for (record = this._removalsHead; record !== null; record = record._nextRemoved) {\n      fn(record);\n    }\n  }\n  diff(map) {\n    if (!map) {\n      map = new Map();\n    } else if (!(map instanceof Map || isJsObject(map))) {\n      throw new RuntimeError(900 /* RuntimeErrorCode.INVALID_DIFFER_INPUT */, ngDevMode && `Error trying to diff '${stringify(map)}'. Only maps and objects are allowed`);\n    }\n    return this.check(map) ? this : null;\n  }\n  onDestroy() {}\n  /**\n   * Check the current state of the map vs the previous.\n   * The algorithm is optimised for when the keys do no change.\n   */\n  check(map) {\n    this._reset();\n    let insertBefore = this._mapHead;\n    this._appendAfter = null;\n    this._forEach(map, (value, key) => {\n      if (insertBefore && insertBefore.key === key) {\n        this._maybeAddToChanges(insertBefore, value);\n        this._appendAfter = insertBefore;\n        insertBefore = insertBefore._next;\n      } else {\n        const record = this._getOrCreateRecordForKey(key, value);\n        insertBefore = this._insertBeforeOrAppend(insertBefore, record);\n      }\n    });\n    // Items remaining at the end of the list have been deleted\n    if (insertBefore) {\n      if (insertBefore._prev) {\n        insertBefore._prev._next = null;\n      }\n      this._removalsHead = insertBefore;\n      for (let record = insertBefore; record !== null; record = record._nextRemoved) {\n        if (record === this._mapHead) {\n          this._mapHead = null;\n        }\n        this._records.delete(record.key);\n        record._nextRemoved = record._next;\n        record.previousValue = record.currentValue;\n        record.currentValue = null;\n        record._prev = null;\n        record._next = null;\n      }\n    }\n    // Make sure tails have no next records from previous runs\n    if (this._changesTail) this._changesTail._nextChanged = null;\n    if (this._additionsTail) this._additionsTail._nextAdded = null;\n    return this.isDirty;\n  }\n  /**\n   * Inserts a record before `before` or append at the end of the list when `before` is null.\n   *\n   * Notes:\n   * - This method appends at `this._appendAfter`,\n   * - This method updates `this._appendAfter`,\n   * - The return value is the new value for the insertion pointer.\n   */\n  _insertBeforeOrAppend(before, record) {\n    if (before) {\n      const prev = before._prev;\n      record._next = before;\n      record._prev = prev;\n      before._prev = record;\n      if (prev) {\n        prev._next = record;\n      }\n      if (before === this._mapHead) {\n        this._mapHead = record;\n      }\n      this._appendAfter = before;\n      return before;\n    }\n    if (this._appendAfter) {\n      this._appendAfter._next = record;\n      record._prev = this._appendAfter;\n    } else {\n      this._mapHead = record;\n    }\n    this._appendAfter = record;\n    return null;\n  }\n  _getOrCreateRecordForKey(key, value) {\n    if (this._records.has(key)) {\n      const record = this._records.get(key);\n      this._maybeAddToChanges(record, value);\n      const prev = record._prev;\n      const next = record._next;\n      if (prev) {\n        prev._next = next;\n      }\n      if (next) {\n        next._prev = prev;\n      }\n      record._next = null;\n      record._prev = null;\n      return record;\n    }\n    const record = new KeyValueChangeRecord_(key);\n    this._records.set(key, record);\n    record.currentValue = value;\n    this._addToAdditions(record);\n    return record;\n  }\n  /** @internal */\n  _reset() {\n    if (this.isDirty) {\n      let record;\n      // let `_previousMapHead` contain the state of the map before the changes\n      this._previousMapHead = this._mapHead;\n      for (record = this._previousMapHead; record !== null; record = record._next) {\n        record._nextPrevious = record._next;\n      }\n      // Update `record.previousValue` with the value of the item before the changes\n      // We need to update all changed items (that's those which have been added and changed)\n      for (record = this._changesHead; record !== null; record = record._nextChanged) {\n        record.previousValue = record.currentValue;\n      }\n      for (record = this._additionsHead; record != null; record = record._nextAdded) {\n        record.previousValue = record.currentValue;\n      }\n      this._changesHead = this._changesTail = null;\n      this._additionsHead = this._additionsTail = null;\n      this._removalsHead = null;\n    }\n  }\n  // Add the record or a given key to the list of changes only when the value has actually changed\n  _maybeAddToChanges(record, newValue) {\n    if (!Object.is(newValue, record.currentValue)) {\n      record.previousValue = record.currentValue;\n      record.currentValue = newValue;\n      this._addToChanges(record);\n    }\n  }\n  _addToAdditions(record) {\n    if (this._additionsHead === null) {\n      this._additionsHead = this._additionsTail = record;\n    } else {\n      this._additionsTail._nextAdded = record;\n      this._additionsTail = record;\n    }\n  }\n  _addToChanges(record) {\n    if (this._changesHead === null) {\n      this._changesHead = this._changesTail = record;\n    } else {\n      this._changesTail._nextChanged = record;\n      this._changesTail = record;\n    }\n  }\n  /** @internal */\n  _forEach(obj, fn) {\n    if (obj instanceof Map) {\n      obj.forEach(fn);\n    } else {\n      Object.keys(obj).forEach(k => fn(obj[k], k));\n    }\n  }\n}\nclass KeyValueChangeRecord_ {\n  key;\n  previousValue = null;\n  currentValue = null;\n  /** @internal */\n  _nextPrevious = null;\n  /** @internal */\n  _next = null;\n  /** @internal */\n  _prev = null;\n  /** @internal */\n  _nextAdded = null;\n  /** @internal */\n  _nextRemoved = null;\n  /** @internal */\n  _nextChanged = null;\n  constructor(key) {\n    this.key = key;\n  }\n}\nfunction defaultIterableDiffersFactory() {\n  return new IterableDiffers([new DefaultIterableDifferFactory()]);\n}\n/**\n * A repository of different iterable diffing strategies used by NgFor, NgClass, and others.\n *\n * @publicApi\n */\nlet IterableDiffers = /*#__PURE__*/(() => {\n  class IterableDiffers {\n    factories;\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */__defineInjectable({\n      token: IterableDiffers,\n      providedIn: 'root',\n      factory: defaultIterableDiffersFactory\n    });\n    constructor(factories) {\n      this.factories = factories;\n    }\n    static create(factories, parent) {\n      if (parent != null) {\n        const copied = parent.factories.slice();\n        factories = factories.concat(copied);\n      }\n      return new IterableDiffers(factories);\n    }\n    /**\n     * Takes an array of {@link IterableDifferFactory} and returns a provider used to extend the\n     * inherited {@link IterableDiffers} instance with the provided factories and return a new\n     * {@link IterableDiffers} instance.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * The following example shows how to extend an existing list of factories,\n     * which will only be applied to the injector for this component and its children.\n     * This step is all that's required to make a new {@link IterableDiffer} available.\n     *\n     * ```ts\n     * @Component({\n     *   viewProviders: [\n     *     IterableDiffers.extend([new ImmutableListDiffer()])\n     *   ]\n     * })\n     * ```\n     */\n    static extend(factories) {\n      return {\n        provide: IterableDiffers,\n        useFactory: parent => {\n          // if parent is null, it means that we are in the root injector and we have just overridden\n          // the default injection mechanism for IterableDiffers, in such a case just assume\n          // `defaultIterableDiffersFactory`.\n          return IterableDiffers.create(factories, parent || defaultIterableDiffersFactory());\n        },\n        // Dependency technically isn't optional, but we can provide a better error message this way.\n        deps: [[IterableDiffers, new SkipSelf(), new Optional()]]\n      };\n    }\n    find(iterable) {\n      const factory = this.factories.find(f => f.supports(iterable));\n      if (factory != null) {\n        return factory;\n      } else {\n        throw new RuntimeError(901 /* RuntimeErrorCode.NO_SUPPORTING_DIFFER_FACTORY */, ngDevMode && `Cannot find a differ supporting object '${iterable}' of type '${getTypeNameForDebugging(iterable)}'`);\n      }\n    }\n  }\n  return IterableDiffers;\n})();\nfunction getTypeNameForDebugging(type) {\n  return type['name'] || typeof type;\n}\nfunction defaultKeyValueDiffersFactory() {\n  return new KeyValueDiffers([new DefaultKeyValueDifferFactory()]);\n}\n/**\n * A repository of different Map diffing strategies used by NgClass, NgStyle, and others.\n *\n * @publicApi\n */\nlet KeyValueDiffers = /*#__PURE__*/(() => {\n  class KeyValueDiffers {\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */__defineInjectable({\n      token: KeyValueDiffers,\n      providedIn: 'root',\n      factory: defaultKeyValueDiffersFactory\n    });\n    factories;\n    constructor(factories) {\n      this.factories = factories;\n    }\n    static create(factories, parent) {\n      if (parent) {\n        const copied = parent.factories.slice();\n        factories = factories.concat(copied);\n      }\n      return new KeyValueDiffers(factories);\n    }\n    /**\n     * Takes an array of {@link KeyValueDifferFactory} and returns a provider used to extend the\n     * inherited {@link KeyValueDiffers} instance with the provided factories and return a new\n     * {@link KeyValueDiffers} instance.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * The following example shows how to extend an existing list of factories,\n     * which will only be applied to the injector for this component and its children.\n     * This step is all that's required to make a new {@link KeyValueDiffer} available.\n     *\n     * ```ts\n     * @Component({\n     *   viewProviders: [\n     *     KeyValueDiffers.extend([new ImmutableMapDiffer()])\n     *   ]\n     * })\n     * ```\n     */\n    static extend(factories) {\n      return {\n        provide: KeyValueDiffers,\n        useFactory: parent => {\n          // if parent is null, it means that we are in the root injector and we have just overridden\n          // the default injection mechanism for KeyValueDiffers, in such a case just assume\n          // `defaultKeyValueDiffersFactory`.\n          return KeyValueDiffers.create(factories, parent || defaultKeyValueDiffersFactory());\n        },\n        // Dependency technically isn't optional, but we can provide a better error message this way.\n        deps: [[KeyValueDiffers, new SkipSelf(), new Optional()]]\n      };\n    }\n    find(kv) {\n      const factory = this.factories.find(f => f.supports(kv));\n      if (factory) {\n        return factory;\n      }\n      throw new RuntimeError(901 /* RuntimeErrorCode.NO_SUPPORTING_DIFFER_FACTORY */, ngDevMode && `Cannot find a differ supporting object '${kv}'`);\n    }\n  }\n  return KeyValueDiffers;\n})();\n/**\n * Structural diffing for `Object`s and `Map`s.\n */\nconst keyValDiff = [/*#__PURE__*/new DefaultKeyValueDifferFactory()];\n/**\n * Structural diffing for `Iterable` types such as `Array`s.\n */\nconst iterableDiff = [/*#__PURE__*/new DefaultIterableDifferFactory()];\nconst defaultIterableDiffers = /*#__PURE__*/new IterableDiffers(iterableDiff);\nconst defaultKeyValueDiffers = /*#__PURE__*/new KeyValueDiffers(keyValDiff);\n\n/**\n * This platform has to be included in any other platform\n *\n * @publicApi\n */\nconst platformCore = /*#__PURE__*/createPlatformFactory(null, 'core', []);\n\n/**\n * Re-exported by `BrowserModule`, which is included automatically in the root\n * `AppModule` when you create a new app with the CLI `new` command. Eagerly injects\n * `ApplicationRef` to instantiate it.\n *\n * @publicApi\n */\nlet ApplicationModule = /*#__PURE__*/(() => {\n  class ApplicationModule {\n    // Inject ApplicationRef to make it eager...\n    constructor(appRef) {}\n    static ɵfac = function ApplicationModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ApplicationModule)(__inject(ApplicationRef));\n    };\n    static ɵmod = /*@__PURE__*/__defineNgModule({\n      type: ApplicationModule\n    });\n    static ɵinj = /*@__PURE__*/__defineInjector({});\n  }\n  return ApplicationModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && setClassMetadata(ApplicationModule, [{\n    type: NgModule\n  }], () => [{\n    type: ApplicationRef\n  }], null);\n})();\n\n/**\n * Internal create application API that implements the core application creation logic and optional\n * bootstrap logic.\n *\n * Platforms (such as `platform-browser`) may require different set of application and platform\n * providers for an application to function correctly. As a result, platforms may use this function\n * internally and supply the necessary providers during the bootstrap, while exposing\n * platform-specific APIs as a part of their public API.\n *\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n */\nfunction internalCreateApplication(config) {\n  profiler(8 /* ProfilerEvent.BootstrapApplicationStart */);\n  try {\n    const {\n      rootComponent,\n      appProviders,\n      platformProviders\n    } = config;\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && rootComponent !== undefined) {\n      assertStandaloneComponentType(rootComponent);\n    }\n    const platformInjector = createOrReusePlatformInjector(platformProviders);\n    // Create root application injector based on a set of providers configured at the platform\n    // bootstrap level as well as providers passed to the bootstrap call by a user.\n    const allAppProviders = [internalProvideZoneChangeDetection({}), {\n      provide: ChangeDetectionScheduler,\n      useExisting: ChangeDetectionSchedulerImpl\n    }, errorHandlerEnvironmentInitializer, ...(appProviders || [])];\n    const adapter = new EnvironmentNgModuleRefAdapter({\n      providers: allAppProviders,\n      parent: platformInjector,\n      debugName: typeof ngDevMode === 'undefined' || ngDevMode ? 'Environment Injector' : '',\n      // We skip environment initializers because we need to run them inside the NgZone, which\n      // happens after we get the NgZone instance from the Injector.\n      runEnvironmentInitializers: false\n    });\n    return bootstrap({\n      r3Injector: adapter.injector,\n      platformInjector,\n      rootComponent\n    });\n  } catch (e) {\n    return Promise.reject(e);\n  } finally {\n    profiler(9 /* ProfilerEvent.BootstrapApplicationEnd */);\n  }\n}\n\n/** Apps in which we've enabled event replay.\n *  This is to prevent initializing event replay more than once per app.\n */\nconst appsWithEventReplay = /*#__PURE__*/new WeakSet();\n/**\n * The key that represents all replayable elements that are not in defer blocks.\n */\nconst EAGER_CONTENT_LISTENERS_KEY = '';\n/**\n * A list of block events that need to be replayed\n */\nlet blockEventQueue = [];\n/**\n * Determines whether Event Replay feature should be activated on the client.\n */\nfunction shouldEnableEventReplay(injector) {\n  return injector.get(IS_EVENT_REPLAY_ENABLED, EVENT_REPLAY_ENABLED_DEFAULT);\n}\n/**\n * Returns a set of providers required to setup support for event replay.\n * Requires hydration to be enabled separately.\n */\nfunction withEventReplay() {\n  const providers = [{\n    provide: IS_EVENT_REPLAY_ENABLED,\n    useFactory: () => {\n      let isEnabled = true;\n      if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n        // Note: globalThis[CONTRACT_PROPERTY] may be undefined in case Event Replay feature\n        // is enabled, but there are no events configured in this application, in which case\n        // we don't activate this feature, since there are no events to replay.\n        const appId = inject(APP_ID);\n        isEnabled = !!window._ejsas?.[appId];\n      }\n      if (isEnabled) {\n        performanceMarkFeature('NgEventReplay');\n      }\n      return isEnabled;\n    }\n  }];\n  if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n    providers.push({\n      provide: ENVIRONMENT_INITIALIZER,\n      useValue: () => {\n        const appRef = inject(ApplicationRef);\n        const {\n          injector\n        } = appRef;\n        // We have to check for the appRef here due to the possibility of multiple apps\n        // being present on the same page. We only want to enable event replay for the\n        // apps that actually want it.\n        if (!appsWithEventReplay.has(appRef)) {\n          const jsActionMap = inject(JSACTION_BLOCK_ELEMENT_MAP);\n          if (shouldEnableEventReplay(injector)) {\n            enableStashEventListenerImpl();\n            const appId = injector.get(APP_ID);\n            const clearStashFn = setStashFn(appId, (rEl, eventName, listenerFn) => {\n              // If a user binds to a ng-container and uses a directive that binds using a host listener,\n              // this element could be a comment node. So we need to ensure we have an actual element\n              // node before stashing anything.\n              if (rEl.nodeType !== Node.ELEMENT_NODE) return;\n              sharedStashFunction(rEl, eventName, listenerFn);\n              sharedMapFunction(rEl, jsActionMap);\n            });\n            // Clean up the reference to the function set by the environment initializer,\n            // as the function closure may capture injected elements and prevent them\n            // from being properly garbage collected.\n            appRef.onDestroy(clearStashFn);\n          }\n        }\n      },\n      multi: true\n    }, {\n      provide: APP_BOOTSTRAP_LISTENER,\n      useFactory: () => {\n        const appRef = inject(ApplicationRef);\n        const {\n          injector\n        } = appRef;\n        return () => {\n          // We have to check for the appRef here due to the possibility of multiple apps\n          // being present on the same page. We only want to enable event replay for the\n          // apps that actually want it.\n          if (!shouldEnableEventReplay(injector) || appsWithEventReplay.has(appRef)) {\n            return;\n          }\n          appsWithEventReplay.add(appRef);\n          appRef.onDestroy(() => {\n            appsWithEventReplay.delete(appRef);\n            // Ensure that we're always safe calling this in the browser.\n            if (typeof ngServerMode !== 'undefined' && !ngServerMode) {\n              const appId = injector.get(APP_ID);\n              // `_ejsa` should be deleted when the app is destroyed, ensuring that\n              // no elements are still captured in the global list and are not prevented\n              // from being garbage collected.\n              clearAppScopedEarlyEventContract(appId);\n            }\n          });\n          // Kick off event replay logic once hydration for the initial part\n          // of the application is completed. This timing is similar to the unclaimed\n          // dehydrated views cleanup timing.\n          appRef.whenStable().then(() => {\n            // Note: we have to check whether the application is destroyed before\n            // performing other operations with the `injector`.\n            // The application may be destroyed **before** it becomes stable, so when\n            // the `whenStable` resolves, the injector might already be in\n            // a destroyed state. Thus, calling `injector.get` would throw an error\n            // indicating that the injector has already been destroyed.\n            if (appRef.destroyed) {\n              return;\n            }\n            const eventContractDetails = injector.get(JSACTION_EVENT_CONTRACT);\n            initEventReplay(eventContractDetails, injector);\n            const jsActionMap = injector.get(JSACTION_BLOCK_ELEMENT_MAP);\n            jsActionMap.get(EAGER_CONTENT_LISTENERS_KEY)?.forEach(removeListeners);\n            jsActionMap.delete(EAGER_CONTENT_LISTENERS_KEY);\n            const eventContract = eventContractDetails.instance;\n            // This removes event listeners registered through the container manager,\n            // as listeners registered on `document.body` might never be removed if we\n            // don't clean up the contract.\n            if (isIncrementalHydrationEnabled(injector)) {\n              // When incremental hydration is enabled, we cannot clean up the event\n              // contract immediately because we're unaware if there are any deferred\n              // blocks to hydrate. We can only schedule a contract cleanup when the\n              // app is destroyed.\n              appRef.onDestroy(() => eventContract.cleanUp());\n            } else {\n              eventContract.cleanUp();\n            }\n          });\n        };\n      },\n      multi: true\n    });\n  }\n  return providers;\n}\nconst initEventReplay = (eventDelegation, injector) => {\n  const appId = injector.get(APP_ID);\n  // This is set in packages/platform-server/src/utils.ts\n  const earlyJsactionData = window._ejsas[appId];\n  const eventContract = eventDelegation.instance = new EventContract(new EventContractContainer(earlyJsactionData.c));\n  for (const et of earlyJsactionData.et) {\n    eventContract.addEvent(et);\n  }\n  for (const et of earlyJsactionData.etc) {\n    eventContract.addEvent(et);\n  }\n  const eventInfos = getAppScopedQueuedEventInfos(appId);\n  eventContract.replayEarlyEventInfos(eventInfos);\n  clearAppScopedEarlyEventContract(appId);\n  const dispatcher = new EventDispatcher(event => {\n    invokeRegisteredReplayListeners(injector, event, event.currentTarget);\n  });\n  registerDispatcher(eventContract, dispatcher);\n};\n/**\n * Extracts information about all DOM events (added in a template) registered on elements in a give\n * LView. Maps collected events to a corresponding DOM element (an element is used as a key).\n */\nfunction collectDomEventsInfo(tView, lView, eventTypesToReplay) {\n  const domEventsInfo = new Map();\n  const lCleanup = lView[CLEANUP];\n  const tCleanup = tView.cleanup;\n  if (!tCleanup || !lCleanup) {\n    return domEventsInfo;\n  }\n  for (let i = 0; i < tCleanup.length;) {\n    const firstParam = tCleanup[i++];\n    const secondParam = tCleanup[i++];\n    if (typeof firstParam !== 'string') {\n      continue;\n    }\n    const eventType = firstParam;\n    if (!isEarlyEventType(eventType)) {\n      continue;\n    }\n    if (isCaptureEventType(eventType)) {\n      eventTypesToReplay.capture.add(eventType);\n    } else {\n      eventTypesToReplay.regular.add(eventType);\n    }\n    const listenerElement = unwrapRNode(lView[secondParam]);\n    i++; // move the cursor to the next position (location of the listener idx)\n    const useCaptureOrIndx = tCleanup[i++];\n    // if useCaptureOrIndx is boolean then report it as is.\n    // if useCaptureOrIndx is positive number then it in unsubscribe method\n    // if useCaptureOrIndx is negative number then it is a Subscription\n    const isDomEvent = typeof useCaptureOrIndx === 'boolean' || useCaptureOrIndx >= 0;\n    if (!isDomEvent) {\n      continue;\n    }\n    if (!domEventsInfo.has(listenerElement)) {\n      domEventsInfo.set(listenerElement, [eventType]);\n    } else {\n      domEventsInfo.get(listenerElement).push(eventType);\n    }\n  }\n  return domEventsInfo;\n}\nfunction invokeRegisteredReplayListeners(injector, event, currentTarget) {\n  const blockName = (currentTarget && currentTarget.getAttribute(DEFER_BLOCK_SSR_ID_ATTRIBUTE)) ?? '';\n  if (/d\\d+/.test(blockName)) {\n    hydrateAndInvokeBlockListeners(blockName, injector, event, currentTarget);\n  } else if (event.eventPhase === EventPhase.REPLAY) {\n    invokeListeners(event, currentTarget);\n  }\n}\nfunction hydrateAndInvokeBlockListeners(blockName, injector, event, currentTarget) {\n  blockEventQueue.push({\n    event,\n    currentTarget\n  });\n  triggerHydrationFromBlockName(injector, blockName, replayQueuedBlockEvents);\n}\nfunction replayQueuedBlockEvents(hydratedBlocks) {\n  // clone the queue\n  const queue = [...blockEventQueue];\n  const hydrated = new Set(hydratedBlocks);\n  // empty it\n  blockEventQueue = [];\n  for (let {\n    event,\n    currentTarget\n  } of queue) {\n    const blockName = currentTarget.getAttribute(DEFER_BLOCK_SSR_ID_ATTRIBUTE);\n    if (hydrated.has(blockName)) {\n      invokeListeners(event, currentTarget);\n    } else {\n      // requeue events that weren't yet hydrated\n      blockEventQueue.push({\n        event,\n        currentTarget\n      });\n    }\n  }\n}\n\n/**\n * A collection that tracks all serialized views (`ngh` DOM annotations)\n * to avoid duplication. An attempt to add a duplicate view results in the\n * collection returning the index of the previously collected serialized view.\n * This reduces the number of annotations needed for a given page.\n */\nclass SerializedViewCollection {\n  views = [];\n  indexByContent = /*#__PURE__*/new Map();\n  add(serializedView) {\n    const viewAsString = JSON.stringify(serializedView);\n    if (!this.indexByContent.has(viewAsString)) {\n      const index = this.views.length;\n      this.views.push(serializedView);\n      this.indexByContent.set(viewAsString, index);\n      return index;\n    }\n    return this.indexByContent.get(viewAsString);\n  }\n  getAll() {\n    return this.views;\n  }\n}\n/**\n * Global counter that is used to generate a unique id for TViews\n * during the serialization process.\n */\nlet tViewSsrId = 0;\n/**\n * Generates a unique id for a given TView and returns this id.\n * The id is also stored on this instance of a TView and reused in\n * subsequent calls.\n *\n * This id is needed to uniquely identify and pick up dehydrated views\n * at runtime.\n */\nfunction getSsrId(tView) {\n  if (!tView.ssrId) {\n    tView.ssrId = `t${tViewSsrId++}`;\n  }\n  return tView.ssrId;\n}\n/**\n * Computes the number of root nodes in a given view\n * (or child nodes in a given container if a tNode is provided).\n */\nfunction calcNumRootNodes(tView, lView, tNode) {\n  const rootNodes = [];\n  collectNativeNodes(tView, lView, tNode, rootNodes);\n  return rootNodes.length;\n}\n/**\n * Computes the number of root nodes in all views in a given LContainer.\n */\nfunction calcNumRootNodesInLContainer(lContainer) {\n  const rootNodes = [];\n  collectNativeNodesInLContainer(lContainer, rootNodes);\n  return rootNodes.length;\n}\n/**\n * Annotates root level component's LView for hydration,\n * see `annotateHostElementForHydration` for additional information.\n */\nfunction annotateComponentLViewForHydration(lView, context, injector) {\n  const hostElement = lView[HOST];\n  // Root elements might also be annotated with the `ngSkipHydration` attribute,\n  // check if it's present before starting the serialization process.\n  if (hostElement && !hostElement.hasAttribute(SKIP_HYDRATION_ATTR_NAME)) {\n    return annotateHostElementForHydration(hostElement, lView, null, context);\n  }\n  return null;\n}\n/**\n * Annotates root level LContainer for hydration. This happens when a root component\n * injects ViewContainerRef, thus making the component an anchor for a view container.\n * This function serializes the component itself as well as all views from the view\n * container.\n */\nfunction annotateLContainerForHydration(lContainer, context, injector) {\n  const componentLView = unwrapLView(lContainer[HOST]);\n  // Serialize the root component itself.\n  const componentLViewNghIndex = annotateComponentLViewForHydration(componentLView, context);\n  if (componentLViewNghIndex === null) {\n    // Component was not serialized (for example, if hydration was skipped by adding\n    // the `ngSkipHydration` attribute or this component uses i18n blocks in the template,\n    // but `withI18nSupport()` was not added), avoid annotating host element with the `ngh`\n    // attribute.\n    return;\n  }\n  const hostElement = unwrapRNode(componentLView[HOST]);\n  // Serialize all views within this view container.\n  const rootLView = lContainer[PARENT];\n  const rootLViewNghIndex = annotateHostElementForHydration(hostElement, rootLView, null, context);\n  const renderer = componentLView[RENDERER];\n  // For cases when a root component also acts as an anchor node for a ViewContainerRef\n  // (for example, when ViewContainerRef is injected in a root component), there is a need\n  // to serialize information about the component itself, as well as an LContainer that\n  // represents this ViewContainerRef. Effectively, we need to serialize 2 pieces of info:\n  // (1) hydration info for the root component itself and (2) hydration info for the\n  // ViewContainerRef instance (an LContainer). Each piece of information is included into\n  // the hydration data (in the TransferState object) separately, thus we end up with 2 ids.\n  // Since we only have 1 root element, we encode both bits of info into a single string:\n  // ids are separated by the `|` char (e.g. `10|25`, where `10` is the ngh for a component view\n  // and 25 is the `ngh` for a root view which holds LContainer).\n  const finalIndex = `${componentLViewNghIndex}|${rootLViewNghIndex}`;\n  renderer.setAttribute(hostElement, NGH_ATTR_NAME, finalIndex);\n}\n/**\n * Annotates all components bootstrapped in a given ApplicationRef\n * with info needed for hydration.\n *\n * @param appRef An instance of an ApplicationRef.\n * @param doc A reference to the current Document instance.\n * @return event types that need to be replayed\n */\nfunction annotateForHydration(appRef, doc) {\n  const injector = appRef.injector;\n  const isI18nHydrationEnabledVal = isI18nHydrationEnabled(injector);\n  const isIncrementalHydrationEnabledVal = isIncrementalHydrationEnabled(injector);\n  const serializedViewCollection = new SerializedViewCollection();\n  const corruptedTextNodes = new Map();\n  const viewRefs = appRef._views;\n  const shouldReplayEvents = injector.get(IS_EVENT_REPLAY_ENABLED, EVENT_REPLAY_ENABLED_DEFAULT);\n  const eventTypesToReplay = {\n    regular: new Set(),\n    capture: new Set()\n  };\n  const deferBlocks = new Map();\n  appRef.injector.get(APP_ID);\n  for (const viewRef of viewRefs) {\n    const lNode = getLNodeForHydration(viewRef);\n    // An `lView` might be `null` if a `ViewRef` represents\n    // an embedded view (not a component view).\n    if (lNode !== null) {\n      const context = {\n        serializedViewCollection,\n        corruptedTextNodes,\n        isI18nHydrationEnabled: isI18nHydrationEnabledVal,\n        isIncrementalHydrationEnabled: isIncrementalHydrationEnabledVal,\n        i18nChildren: new Map(),\n        eventTypesToReplay,\n        shouldReplayEvents,\n        deferBlocks\n      };\n      if (isLContainer(lNode)) {\n        annotateLContainerForHydration(lNode, context);\n      } else {\n        annotateComponentLViewForHydration(lNode, context);\n      }\n      insertCorruptedTextNodeMarkers(corruptedTextNodes, doc);\n    }\n  }\n  // Note: we *always* include hydration info key and a corresponding value\n  // into the TransferState, even if the list of serialized views is empty.\n  // This is needed as a signal to the client that the server part of the\n  // hydration logic was setup and enabled correctly. Otherwise, if a client\n  // hydration doesn't find a key in the transfer state - an error is produced.\n  const serializedViews = serializedViewCollection.getAll();\n  const transferState = injector.get(TransferState);\n  transferState.set(NGH_DATA_KEY, serializedViews);\n  if (deferBlocks.size > 0) {\n    const blocks = {};\n    for (const [id, info] of deferBlocks.entries()) {\n      blocks[id] = info;\n    }\n    transferState.set(NGH_DEFER_BLOCKS_KEY, blocks);\n  }\n  return eventTypesToReplay;\n}\n/**\n * Serializes the lContainer data into a list of SerializedView objects,\n * that represent views within this lContainer.\n *\n * @param lContainer the lContainer we are serializing\n * @param tNode the TNode that contains info about this LContainer\n * @param lView that hosts this LContainer\n * @param parentDeferBlockId the defer block id of the parent if it exists\n * @param context the hydration context\n * @returns an array of the `SerializedView` objects\n */\nfunction serializeLContainer(lContainer, tNode, lView, parentDeferBlockId, context) {\n  const views = [];\n  let lastViewAsString = '';\n  for (let i = CONTAINER_HEADER_OFFSET; i < lContainer.length; i++) {\n    let childLView = lContainer[i];\n    let template;\n    let numRootNodes;\n    let serializedView;\n    if (isRootView(childLView)) {\n      // If this is a root view, get an LView for the underlying component,\n      // because it contains information about the view to serialize.\n      childLView = childLView[HEADER_OFFSET];\n      // If we have an LContainer at this position, this indicates that the\n      // host element was used as a ViewContainerRef anchor (e.g. a `ViewContainerRef`\n      // was injected within the component class). This case requires special handling.\n      if (isLContainer(childLView)) {\n        // Calculate the number of root nodes in all views in a given container\n        // and increment by one to account for an anchor node itself, i.e. in this\n        // scenario we'll have a layout that would look like this:\n        // `<app-root /><#VIEW1><#VIEW2>...<!--container-->`\n        // The `+1` is to capture the `<app-root />` element.\n        numRootNodes = calcNumRootNodesInLContainer(childLView) + 1;\n        annotateLContainerForHydration(childLView, context);\n        const componentLView = unwrapLView(childLView[HOST]);\n        serializedView = {\n          [TEMPLATE_ID]: componentLView[TVIEW].ssrId,\n          [NUM_ROOT_NODES]: numRootNodes\n        };\n      }\n    }\n    if (!serializedView) {\n      const childTView = childLView[TVIEW];\n      if (childTView.type === 1 /* TViewType.Component */) {\n        template = childTView.ssrId;\n        // This is a component view, thus it has only 1 root node: the component\n        // host node itself (other nodes would be inside that host node).\n        numRootNodes = 1;\n      } else {\n        template = getSsrId(childTView);\n        numRootNodes = calcNumRootNodes(childTView, childLView, childTView.firstChild);\n      }\n      serializedView = {\n        [TEMPLATE_ID]: template,\n        [NUM_ROOT_NODES]: numRootNodes\n      };\n      let isHydrateNeverBlock = false;\n      // If this is a defer block, serialize extra info.\n      if (isDeferBlock(lView[TVIEW], tNode)) {\n        const lDetails = getLDeferBlockDetails(lView, tNode);\n        const tDetails = getTDeferBlockDetails(lView[TVIEW], tNode);\n        if (context.isIncrementalHydrationEnabled && tDetails.hydrateTriggers !== null) {\n          const deferBlockId = `d${context.deferBlocks.size}`;\n          if (tDetails.hydrateTriggers.has(7 /* DeferBlockTrigger.Never */)) {\n            isHydrateNeverBlock = true;\n          }\n          let rootNodes = [];\n          collectNativeNodesInLContainer(lContainer, rootNodes);\n          // Add defer block into info context.deferBlocks\n          const deferBlockInfo = {\n            [NUM_ROOT_NODES]: rootNodes.length,\n            [DEFER_BLOCK_STATE]: lDetails[DEFER_BLOCK_STATE$1]\n          };\n          const serializedTriggers = serializeHydrateTriggers(tDetails.hydrateTriggers);\n          if (serializedTriggers.length > 0) {\n            deferBlockInfo[DEFER_HYDRATE_TRIGGERS] = serializedTriggers;\n          }\n          if (parentDeferBlockId !== null) {\n            // Serialize parent id only when it's present.\n            deferBlockInfo[DEFER_PARENT_BLOCK_ID] = parentDeferBlockId;\n          }\n          context.deferBlocks.set(deferBlockId, deferBlockInfo);\n          const node = unwrapRNode(lContainer);\n          if (node !== undefined) {\n            if (node.nodeType === Node.COMMENT_NODE) {\n              annotateDeferBlockAnchorForHydration(node, deferBlockId);\n            }\n          } else {\n            ngDevMode && validateNodeExists(node, childLView, tNode);\n            ngDevMode && validateMatchingNode(node, Node.COMMENT_NODE, null, childLView, tNode, true);\n            annotateDeferBlockAnchorForHydration(node, deferBlockId);\n          }\n          if (!isHydrateNeverBlock) {\n            // Add JSAction attributes for root nodes that use some hydration triggers\n            annotateDeferBlockRootNodesWithJsAction(tDetails, rootNodes, deferBlockId, context);\n          }\n          // Use current block id as parent for nested routes.\n          parentDeferBlockId = deferBlockId;\n          // Serialize extra info into the view object.\n          // TODO(incremental-hydration): this should be serialized and included at a different level\n          // (not at the view level).\n          serializedView[DEFER_BLOCK_ID] = deferBlockId;\n        }\n        // DEFER_BLOCK_STATE is used for reconciliation in hydration, both regular and incremental.\n        // We need to know which template is rendered when hydrating. So we serialize this state\n        // regardless of hydration type.\n        serializedView[DEFER_BLOCK_STATE] = lDetails[DEFER_BLOCK_STATE$1];\n      }\n      if (!isHydrateNeverBlock) {\n        Object.assign(serializedView, serializeLView(lContainer[i], parentDeferBlockId, context));\n      }\n    }\n    // Check if the previous view has the same shape (for example, it was\n    // produced by the *ngFor), in which case bump the counter on the previous\n    // view instead of including the same information again.\n    const currentViewAsString = JSON.stringify(serializedView);\n    if (views.length > 0 && currentViewAsString === lastViewAsString) {\n      const previousView = views[views.length - 1];\n      previousView[MULTIPLIER] ??= 1;\n      previousView[MULTIPLIER]++;\n    } else {\n      // Record this view as most recently added.\n      lastViewAsString = currentViewAsString;\n      views.push(serializedView);\n    }\n  }\n  return views;\n}\nfunction serializeHydrateTriggers(triggerMap) {\n  const serializableDeferBlockTrigger = new Set([0 /* DeferBlockTrigger.Idle */, 1 /* DeferBlockTrigger.Immediate */, 2 /* DeferBlockTrigger.Viewport */, 5 /* DeferBlockTrigger.Timer */]);\n  let triggers = [];\n  for (let [trigger, details] of triggerMap) {\n    if (serializableDeferBlockTrigger.has(trigger)) {\n      if (details === null) {\n        triggers.push(trigger);\n      } else {\n        triggers.push({\n          trigger,\n          delay: details.delay\n        });\n      }\n    }\n  }\n  return triggers;\n}\n/**\n * Helper function to produce a node path (which navigation steps runtime logic\n * needs to take to locate a node) and stores it in the `NODES` section of the\n * current serialized view.\n */\nfunction appendSerializedNodePath(ngh, tNode, lView, excludedParentNodes) {\n  const noOffsetIndex = tNode.index - HEADER_OFFSET;\n  ngh[NODES] ??= {};\n  // Ensure we don't calculate the path multiple times.\n  ngh[NODES][noOffsetIndex] ??= calcPathForNode(tNode, lView, excludedParentNodes);\n}\n/**\n * Helper function to append information about a disconnected node.\n * This info is needed at runtime to avoid DOM lookups for this element\n * and instead, the element would be created from scratch.\n */\nfunction appendDisconnectedNodeIndex(ngh, tNodeOrNoOffsetIndex) {\n  const noOffsetIndex = typeof tNodeOrNoOffsetIndex === 'number' ? tNodeOrNoOffsetIndex : tNodeOrNoOffsetIndex.index - HEADER_OFFSET;\n  ngh[DISCONNECTED_NODES] ??= [];\n  if (!ngh[DISCONNECTED_NODES].includes(noOffsetIndex)) {\n    ngh[DISCONNECTED_NODES].push(noOffsetIndex);\n  }\n}\n/**\n * Serializes the lView data into a SerializedView object that will later be added\n * to the TransferState storage and referenced using the `ngh` attribute on a host\n * element.\n *\n * @param lView the lView we are serializing\n * @param context the hydration context\n * @returns the `SerializedView` object containing the data to be added to the host node\n */\nfunction serializeLView(lView, parentDeferBlockId = null, context) {\n  const ngh = {};\n  const tView = lView[TVIEW];\n  const i18nChildren = getOrComputeI18nChildren(tView, context);\n  const nativeElementsToEventTypes = context.shouldReplayEvents ? collectDomEventsInfo(tView, lView, context.eventTypesToReplay) : null;\n  // Iterate over DOM element references in an LView.\n  for (let i = HEADER_OFFSET; i < tView.bindingStartIndex; i++) {\n    const tNode = tView.data[i];\n    const noOffsetIndex = i - HEADER_OFFSET;\n    // Attempt to serialize any i18n data for the given slot. We do this first, as i18n\n    // has its own process for serialization.\n    const i18nData = trySerializeI18nBlock(lView, i, context);\n    if (i18nData) {\n      ngh[I18N_DATA] ??= {};\n      ngh[I18N_DATA][noOffsetIndex] = i18nData.caseQueue;\n      for (const nodeNoOffsetIndex of i18nData.disconnectedNodes) {\n        appendDisconnectedNodeIndex(ngh, nodeNoOffsetIndex);\n      }\n      for (const nodeNoOffsetIndex of i18nData.disjointNodes) {\n        const tNode = tView.data[nodeNoOffsetIndex + HEADER_OFFSET];\n        ngDevMode && assertTNode(tNode);\n        appendSerializedNodePath(ngh, tNode, lView, i18nChildren);\n      }\n      continue;\n    }\n    // Skip processing of a given slot in the following cases:\n    // - Local refs (e.g. <div #localRef>) take up an extra slot in LViews\n    //   to store the same element. In this case, there is no information in\n    //   a corresponding slot in TNode data structure.\n    // - When a slot contains something other than a TNode. For example, there\n    //   might be some metadata information about a defer block or a control flow block.\n    if (!isTNodeShape(tNode)) {\n      continue;\n    }\n    // Skip any nodes that are in an i18n block but are considered detached (i.e. not\n    // present in the template). These nodes are disconnected from the DOM tree, and\n    // so we don't want to serialize any information about them.\n    if (isDetachedByI18n(tNode)) {\n      continue;\n    }\n    // Check if a native node that represents a given TNode is disconnected from the DOM tree.\n    // Such nodes must be excluded from the hydration (since the hydration won't be able to\n    // find them), so the TNode ids are collected and used at runtime to skip the hydration.\n    //\n    // This situation may happen during the content projection, when some nodes don't make it\n    // into one of the content projection slots (for example, when there is no default\n    // <ng-content /> slot in projector component's template).\n    if (isDisconnectedNode(tNode, lView) && isContentProjectedNode(tNode)) {\n      appendDisconnectedNodeIndex(ngh, tNode);\n      continue;\n    }\n    if (Array.isArray(tNode.projection)) {\n      for (const projectionHeadTNode of tNode.projection) {\n        // We may have `null`s in slots with no projected content.\n        if (!projectionHeadTNode) continue;\n        if (!Array.isArray(projectionHeadTNode)) {\n          // If we process re-projected content (i.e. `<ng-content>`\n          // appears at projection location), skip annotations for this content\n          // since all DOM nodes in this projection were handled while processing\n          // a parent lView, which contains those nodes.\n          if (!isProjectionTNode(projectionHeadTNode) && !isInSkipHydrationBlock(projectionHeadTNode)) {\n            if (isDisconnectedNode(projectionHeadTNode, lView)) {\n              // Check whether this node is connected, since we may have a TNode\n              // in the data structure as a projection segment head, but the\n              // content projection slot might be disabled (e.g.\n              // <ng-content *ngIf=\"false\" />).\n              appendDisconnectedNodeIndex(ngh, projectionHeadTNode);\n            } else {\n              appendSerializedNodePath(ngh, projectionHeadTNode, lView, i18nChildren);\n            }\n          }\n        } else {\n          // If a value is an array, it means that we are processing a projection\n          // where projectable nodes were passed in as DOM nodes (for example, when\n          // calling `ViewContainerRef.createComponent(CmpA, {projectableNodes: [...]})`).\n          //\n          // In this scenario, nodes can come from anywhere (either created manually,\n          // accessed via `document.querySelector`, etc) and may be in any state\n          // (attached or detached from the DOM tree). As a result, we can not reliably\n          // restore the state for such cases during hydration.\n          throw unsupportedProjectionOfDomNodes(unwrapRNode(lView[i]));\n        }\n      }\n    }\n    conditionallyAnnotateNodePath(ngh, tNode, lView, i18nChildren);\n    if (isLContainer(lView[i])) {\n      // Serialize information about a template.\n      const embeddedTView = tNode.tView;\n      if (embeddedTView !== null) {\n        ngh[TEMPLATES] ??= {};\n        ngh[TEMPLATES][noOffsetIndex] = getSsrId(embeddedTView);\n      }\n      // Serialize views within this LContainer.\n      const hostNode = lView[i][HOST]; // host node of this container\n      // LView[i][HOST] can be of 2 different types:\n      // - either a DOM node\n      // - or an array that represents an LView of a component\n      if (Array.isArray(hostNode)) {\n        // This is a component, serialize info about it.\n        const targetNode = unwrapRNode(hostNode);\n        if (!targetNode.hasAttribute(SKIP_HYDRATION_ATTR_NAME)) {\n          annotateHostElementForHydration(targetNode, hostNode, parentDeferBlockId, context);\n        }\n      }\n      ngh[CONTAINERS] ??= {};\n      ngh[CONTAINERS][noOffsetIndex] = serializeLContainer(lView[i], tNode, lView, parentDeferBlockId, context);\n    } else if (Array.isArray(lView[i]) && !isLetDeclaration(tNode)) {\n      // This is a component, annotate the host node with an `ngh` attribute.\n      // Note: Let declarations that return an array are also storing an array in the LView,\n      // we need to exclude them.\n      const targetNode = unwrapRNode(lView[i][HOST]);\n      if (!targetNode.hasAttribute(SKIP_HYDRATION_ATTR_NAME)) {\n        annotateHostElementForHydration(targetNode, lView[i], parentDeferBlockId, context);\n      }\n    } else {\n      // <ng-container> case\n      if (tNode.type & 8 /* TNodeType.ElementContainer */) {\n        // An <ng-container> is represented by the number of\n        // top-level nodes. This information is needed to skip over\n        // those nodes to reach a corresponding anchor node (comment node).\n        ngh[ELEMENT_CONTAINERS] ??= {};\n        ngh[ELEMENT_CONTAINERS][noOffsetIndex] = calcNumRootNodes(tView, lView, tNode.child);\n      } else if (tNode.type & (16 /* TNodeType.Projection */ | 128 /* TNodeType.LetDeclaration */)) {\n        // Current TNode represents an `<ng-content>` slot or `@let` declaration,\n        // thus it has no DOM elements associated with it, so the **next sibling**\n        // node would not be able to find an anchor. In this case, use full path instead.\n        let nextTNode = tNode.next;\n        // Skip over all `<ng-content>` slots and `@let` declarations in a row.\n        while (nextTNode !== null && nextTNode.type & (16 /* TNodeType.Projection */ | 128 /* TNodeType.LetDeclaration */)) {\n          nextTNode = nextTNode.next;\n        }\n        if (nextTNode && !isInSkipHydrationBlock(nextTNode)) {\n          // Handle a tNode after the `<ng-content>` slot.\n          appendSerializedNodePath(ngh, nextTNode, lView, i18nChildren);\n        }\n      } else if (tNode.type & 1 /* TNodeType.Text */) {\n        const rNode = unwrapRNode(lView[i]);\n        processTextNodeBeforeSerialization(context, rNode);\n      }\n    }\n    // Attach `jsaction` attribute to elements that have registered listeners,\n    // thus potentially having a need to do an event replay.\n    if (nativeElementsToEventTypes && tNode.type & 2 /* TNodeType.Element */) {\n      const nativeElement = unwrapRNode(lView[i]);\n      if (nativeElementsToEventTypes.has(nativeElement)) {\n        setJSActionAttributes(nativeElement, nativeElementsToEventTypes.get(nativeElement), parentDeferBlockId);\n      }\n    }\n  }\n  return ngh;\n}\n/**\n * Serializes node location in cases when it's needed, specifically:\n *\n *  1. If `tNode.projectionNext` is different from `tNode.next` - it means that\n *     the next `tNode` after projection is different from the one in the original\n *     template. Since hydration relies on `tNode.next`, this serialized info\n *     is required to help runtime code find the node at the correct location.\n *  2. In certain content projection-based use-cases, it's possible that only\n *     a content of a projected element is rendered. In this case, content nodes\n *     require an extra annotation, since runtime logic can't rely on parent-child\n *     connection to identify the location of a node.\n */\nfunction conditionallyAnnotateNodePath(ngh, tNode, lView, excludedParentNodes) {\n  if (isProjectionTNode(tNode)) {\n    // Do not annotate projection nodes (<ng-content />), since\n    // they don't have a corresponding DOM node representing them.\n    return;\n  }\n  // Handle case #1 described above.\n  if (tNode.projectionNext && tNode.projectionNext !== tNode.next && !isInSkipHydrationBlock(tNode.projectionNext)) {\n    appendSerializedNodePath(ngh, tNode.projectionNext, lView, excludedParentNodes);\n  }\n  // Handle case #2 described above.\n  // Note: we only do that for the first node (i.e. when `tNode.prev === null`),\n  // the rest of the nodes would rely on the current node location, so no extra\n  // annotation is needed.\n  if (tNode.prev === null && tNode.parent !== null && isDisconnectedNode(tNode.parent, lView) && !isDisconnectedNode(tNode, lView)) {\n    appendSerializedNodePath(ngh, tNode, lView, excludedParentNodes);\n  }\n}\n/**\n * Determines whether a component instance that is represented\n * by a given LView uses `ViewEncapsulation.ShadowDom`.\n */\nfunction componentUsesShadowDomEncapsulation(lView) {\n  const instance = lView[CONTEXT];\n  return instance?.constructor ? getComponentDef(instance.constructor)?.encapsulation === ViewEncapsulation$1.ShadowDom : false;\n}\n/**\n * Annotates component host element for hydration:\n * - by either adding the `ngh` attribute and collecting hydration-related info\n *   for the serialization and transferring to the client\n * - or by adding the `ngSkipHydration` attribute in case Angular detects that\n *   component contents is not compatible with hydration.\n *\n * @param element The Host element to be annotated\n * @param lView The associated LView\n * @param context The hydration context\n * @returns An index of serialized view from the transfer state object\n *          or `null` when a given component can not be serialized.\n */\nfunction annotateHostElementForHydration(element, lView, parentDeferBlockId, context) {\n  const renderer = lView[RENDERER];\n  if (hasI18n(lView) && !isI18nHydrationSupportEnabled() || componentUsesShadowDomEncapsulation(lView)) {\n    // Attach the skip hydration attribute if this component:\n    // - either has i18n blocks, since hydrating such blocks is not yet supported\n    // - or uses ShadowDom view encapsulation, since Domino doesn't support\n    //   shadow DOM, so we can not guarantee that client and server representations\n    //   would exactly match\n    renderer.setAttribute(element, SKIP_HYDRATION_ATTR_NAME, '');\n    return null;\n  } else {\n    const ngh = serializeLView(lView, parentDeferBlockId, context);\n    const index = context.serializedViewCollection.add(ngh);\n    renderer.setAttribute(element, NGH_ATTR_NAME, index.toString());\n    return index;\n  }\n}\n/**\n * Annotates defer block comment node for hydration:\n *\n * @param comment The Host element to be annotated\n * @param deferBlockId the id of the target defer block\n */\nfunction annotateDeferBlockAnchorForHydration(comment, deferBlockId) {\n  comment.textContent = `ngh=${deferBlockId}`;\n}\n/**\n * Physically inserts the comment nodes to ensure empty text nodes and adjacent\n * text node separators are preserved after server serialization of the DOM.\n * These get swapped back for empty text nodes or separators once hydration happens\n * on the client.\n *\n * @param corruptedTextNodes The Map of text nodes to be replaced with comments\n * @param doc The document\n */\nfunction insertCorruptedTextNodeMarkers(corruptedTextNodes, doc) {\n  for (const [textNode, marker] of corruptedTextNodes) {\n    textNode.after(doc.createComment(marker));\n  }\n}\n/**\n * Detects whether a given TNode represents a node that\n * is being content projected.\n */\nfunction isContentProjectedNode(tNode) {\n  let currentTNode = tNode;\n  while (currentTNode != null) {\n    // If we come across a component host node in parent nodes -\n    // this TNode is in the content projection section.\n    if (isComponentHost(currentTNode)) {\n      return true;\n    }\n    currentTNode = currentTNode.parent;\n  }\n  return false;\n}\n/**\n * Incremental hydration requires that any defer block root node\n * with interaction or hover triggers have all of their root nodes\n * trigger hydration with those events. So we need to make sure all\n * the root nodes of that block have the proper jsaction attribute\n * to ensure hydration is triggered, since the content is dehydrated\n */\nfunction annotateDeferBlockRootNodesWithJsAction(tDetails, rootNodes, parentDeferBlockId, context) {\n  const actionList = convertHydrateTriggersToJsAction(tDetails.hydrateTriggers);\n  for (let et of actionList) {\n    context.eventTypesToReplay.regular.add(et);\n  }\n  if (actionList.length > 0) {\n    const elementNodes = rootNodes.filter(rn => rn.nodeType === Node.ELEMENT_NODE);\n    for (let rNode of elementNodes) {\n      setJSActionAttributes(rNode, actionList, parentDeferBlockId);\n    }\n  }\n}\n\n/**\n * Indicates whether the hydration-related code was added,\n * prevents adding it multiple times.\n */\nlet isHydrationSupportEnabled = false;\n/**\n * Indicates whether the i18n-related code was added,\n * prevents adding it multiple times.\n *\n * Note: This merely controls whether the code is loaded,\n * while `setIsI18nHydrationSupportEnabled` determines\n * whether i18n blocks are serialized or hydrated.\n */\nlet isI18nHydrationRuntimeSupportEnabled = false;\n/**\n * Indicates whether the incremental hydration code was added,\n * prevents adding it multiple times.\n */\nlet isIncrementalHydrationRuntimeSupportEnabled = false;\n/**\n * Defines a period of time that Angular waits for the `ApplicationRef.isStable` to emit `true`.\n * If there was no event with the `true` value during this time, Angular reports a warning.\n */\nconst APPLICATION_IS_STABLE_TIMEOUT = 10_000;\n/**\n * Brings the necessary hydration code in tree-shakable manner.\n * The code is only present when the `provideClientHydration` is\n * invoked. Otherwise, this code is tree-shaken away during the\n * build optimization step.\n *\n * This technique allows us to swap implementations of methods so\n * tree shaking works appropriately when hydration is disabled or\n * enabled. It brings in the appropriate version of the method that\n * supports hydration only when enabled.\n */\nfunction enableHydrationRuntimeSupport() {\n  if (!isHydrationSupportEnabled) {\n    isHydrationSupportEnabled = true;\n    enableRetrieveHydrationInfoImpl();\n    enableLocateOrCreateElementNodeImpl();\n    enableLocateOrCreateTextNodeImpl();\n    enableLocateOrCreateElementContainerNodeImpl();\n    enableLocateOrCreateContainerAnchorImpl();\n    enableLocateOrCreateContainerRefImpl();\n    enableFindMatchingDehydratedViewImpl();\n    enableApplyRootElementTransformImpl();\n  }\n}\n/**\n * Brings the necessary i18n hydration code in tree-shakable manner.\n * Similar to `enableHydrationRuntimeSupport`, the code is only\n * present when `withI18nSupport` is invoked.\n */\nfunction enableI18nHydrationRuntimeSupport() {\n  if (!isI18nHydrationRuntimeSupportEnabled) {\n    isI18nHydrationRuntimeSupportEnabled = true;\n    enableLocateOrCreateI18nNodeImpl();\n    enablePrepareI18nBlockForHydrationImpl();\n    enableClaimDehydratedIcuCaseImpl();\n  }\n}\n/**\n * Brings the necessary incremental hydration code in tree-shakable manner.\n * Similar to `enableHydrationRuntimeSupport`, the code is only\n * present when `enableIncrementalHydrationRuntimeSupport` is invoked.\n */\nfunction enableIncrementalHydrationRuntimeSupport() {\n  if (!isIncrementalHydrationRuntimeSupportEnabled) {\n    isIncrementalHydrationRuntimeSupportEnabled = true;\n    enableRetrieveDeferBlockDataImpl();\n  }\n}\n/**\n * Outputs a message with hydration stats into a console.\n */\nfunction printHydrationStats(injector) {\n  const console = injector.get(Console);\n  const message = `Angular hydrated ${ngDevMode.hydratedComponents} component(s) ` + `and ${ngDevMode.hydratedNodes} node(s), ` + `${ngDevMode.componentsSkippedHydration} component(s) were skipped. ` + (isIncrementalHydrationEnabled(injector) ? `${ngDevMode.deferBlocksWithIncrementalHydration} defer block(s) were configured to use incremental hydration. ` : '') + `Learn more at https://angular.dev/guide/hydration.`;\n  // tslint:disable-next-line:no-console\n  console.log(message);\n}\n/**\n * Returns a Promise that is resolved when an application becomes stable.\n */\nfunction whenStableWithTimeout(appRef) {\n  const whenStablePromise = appRef.whenStable();\n  if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n    const timeoutTime = APPLICATION_IS_STABLE_TIMEOUT;\n    const console = appRef.injector.get(Console);\n    const ngZone = appRef.injector.get(NgZone);\n    // The following call should not and does not prevent the app to become stable\n    // We cannot use RxJS timer here because the app would remain unstable.\n    // This also avoids an extra change detection cycle.\n    const timeoutId = ngZone.runOutsideAngular(() => {\n      return setTimeout(() => logWarningOnStableTimedout(timeoutTime, console), timeoutTime);\n    });\n    whenStablePromise.finally(() => clearTimeout(timeoutId));\n  }\n  return whenStablePromise;\n}\n/**\n * Defines a name of an attribute that is added to the <body> tag\n * in the `index.html` file in case a given route was configured\n * with `RenderMode.Client`. 'cm' is an abbreviation for \"Client Mode\".\n */\nconst CLIENT_RENDER_MODE_FLAG = 'ngcm';\n/**\n * Checks whether the `RenderMode.Client` was defined for the current route.\n */\nfunction isClientRenderModeEnabled() {\n  const doc = getDocument();\n  return (typeof ngServerMode === 'undefined' || !ngServerMode) && doc.body.hasAttribute(CLIENT_RENDER_MODE_FLAG);\n}\n/**\n * Returns a set of providers required to setup hydration support\n * for an application that is server side rendered. This function is\n * included into the `provideClientHydration` public API function from\n * the `platform-browser` package.\n *\n * The function sets up an internal flag that would be recognized during\n * the server side rendering time as well, so there is no need to\n * configure or change anything in NgUniversal to enable the feature.\n */\nfunction withDomHydration() {\n  const providers = [{\n    provide: IS_HYDRATION_DOM_REUSE_ENABLED,\n    useFactory: () => {\n      let isEnabled = true;\n      if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n        // On the client, verify that the server response contains\n        // hydration annotations. Otherwise, keep hydration disabled.\n        const transferState = inject(TransferState, {\n          optional: true\n        });\n        isEnabled = !!transferState?.get(NGH_DATA_KEY, null);\n      }\n      if (isEnabled) {\n        performanceMarkFeature('NgHydration');\n      }\n      return isEnabled;\n    }\n  }, {\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      // i18n support is enabled by calling withI18nSupport(), but there's\n      // no way to turn it off (e.g. for tests), so we turn it off by default.\n      setIsI18nHydrationSupportEnabled(false);\n      if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n        // Since this function is used across both server and client,\n        // make sure that the runtime code is only added when invoked\n        // on the client (see the `enableHydrationRuntimeSupport` function\n        // call below).\n        return;\n      }\n      if (inject(IS_HYDRATION_DOM_REUSE_ENABLED)) {\n        verifySsrContentsIntegrity(getDocument());\n        enableHydrationRuntimeSupport();\n      } else if (typeof ngDevMode !== 'undefined' && ngDevMode && !isClientRenderModeEnabled()) {\n        const console = inject(Console);\n        const message = formatRuntimeError(-505 /* RuntimeErrorCode.MISSING_HYDRATION_ANNOTATIONS */, 'Angular hydration was requested on the client, but there was no ' + 'serialized information present in the server response, ' + 'thus hydration was not enabled. ' + 'Make sure the `provideClientHydration()` is included into the list ' + 'of providers in the server part of the application configuration.');\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n  if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n    providers.push({\n      provide: PRESERVE_HOST_CONTENT,\n      useFactory: () => {\n        // Preserve host element content only in a browser\n        // environment and when hydration is configured properly.\n        // On a server, an application is rendered from scratch,\n        // so the host content needs to be empty.\n        return inject(IS_HYDRATION_DOM_REUSE_ENABLED);\n      }\n    }, {\n      provide: APP_BOOTSTRAP_LISTENER,\n      useFactory: () => {\n        if (inject(IS_HYDRATION_DOM_REUSE_ENABLED)) {\n          const appRef = inject(ApplicationRef);\n          return () => {\n            // Wait until an app becomes stable and cleanup all views that\n            // were not claimed during the application bootstrap process.\n            // The timing is similar to when we start the serialization process\n            // on the server.\n            //\n            // Note: the cleanup task *MUST* be scheduled within the Angular zone in Zone apps\n            // to ensure that change detection is properly run afterward.\n            whenStableWithTimeout(appRef).then(() => {\n              // Note: we have to check whether the application is destroyed before\n              // performing other operations with the `injector`.\n              // The application may be destroyed **before** it becomes stable, so when\n              // the `whenStableWithTimeout` resolves, the injector might already be in\n              // a destroyed state. Thus, calling `injector.get` would throw an error\n              // indicating that the injector has already been destroyed.\n              if (appRef.destroyed) {\n                return;\n              }\n              cleanupDehydratedViews(appRef);\n              if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n                countBlocksSkippedByHydration(appRef.injector);\n                printHydrationStats(appRef.injector);\n              }\n            });\n          };\n        }\n        return () => {}; // noop\n      },\n      multi: true\n    });\n  }\n  return makeEnvironmentProviders(providers);\n}\n/**\n * Returns a set of providers required to setup support for i18n hydration.\n * Requires hydration to be enabled separately.\n */\nfunction withI18nSupport() {\n  return [{\n    provide: IS_I18N_HYDRATION_ENABLED,\n    useFactory: () => inject(IS_HYDRATION_DOM_REUSE_ENABLED)\n  }, {\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      if (inject(IS_HYDRATION_DOM_REUSE_ENABLED)) {\n        enableI18nHydrationRuntimeSupport();\n        setIsI18nHydrationSupportEnabled(true);\n        performanceMarkFeature('NgI18nHydration');\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Returns a set of providers required to setup support for incremental hydration.\n * Requires hydration to be enabled separately.\n * Enabling incremental hydration also enables event replay for the entire app.\n */\nfunction withIncrementalHydration() {\n  const providers = [withEventReplay(), {\n    provide: IS_INCREMENTAL_HYDRATION_ENABLED,\n    useValue: true\n  }, {\n    provide: DEHYDRATED_BLOCK_REGISTRY,\n    useClass: DehydratedBlockRegistry\n  }, {\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      enableIncrementalHydrationRuntimeSupport();\n      performanceMarkFeature('NgIncrementalHydration');\n    },\n    multi: true\n  }];\n  if (typeof ngServerMode === 'undefined' || !ngServerMode) {\n    providers.push({\n      provide: APP_BOOTSTRAP_LISTENER,\n      useFactory: () => {\n        const injector = inject(Injector);\n        const doc = getDocument();\n        return () => {\n          const deferBlockData = processBlockData(injector);\n          const commentsByBlockId = gatherDeferBlocksCommentNodes(doc, doc.body);\n          processAndInitTriggers(injector, deferBlockData, commentsByBlockId);\n          appendDeferBlocksToJSActionMap(doc, injector);\n        };\n      },\n      multi: true\n    });\n  }\n  return providers;\n}\n/**\n *\n * @param time The time in ms until the stable timedout warning message is logged\n */\nfunction logWarningOnStableTimedout(time, console) {\n  const message = `Angular hydration expected the ApplicationRef.isStable() to emit \\`true\\`, but it ` + `didn't happen within ${time}ms. Angular hydration logic depends on the application becoming stable ` + `as a signal to complete hydration process.`;\n  console.warn(formatRuntimeError(-506 /* RuntimeErrorCode.HYDRATION_STABLE_TIMEDOUT */, message));\n}\n\n/**\n * Transforms a value (typically a string) to a boolean.\n * Intended to be used as a transform function of an input.\n *\n *  @usageNotes\n *  ```ts\n *  @Input({ transform: booleanAttribute }) status!: boolean;\n *  ```\n * @param value Value to be transformed.\n *\n * @publicApi\n */\nfunction booleanAttribute(value) {\n  return typeof value === 'boolean' ? value : value != null && value !== 'false';\n}\n/**\n * Transforms a value (typically a string) to a number.\n * Intended to be used as a transform function of an input.\n * @param value Value to be transformed.\n * @param fallbackValue Value to use if the provided value can't be parsed as a number.\n *\n *  @usageNotes\n *  ```ts\n *  @Input({ transform: numberAttribute }) id!: number;\n *  ```\n *\n * @publicApi\n */\nfunction numberAttribute(value, fallbackValue = NaN) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  const isNumberValue = !isNaN(parseFloat(value)) && !isNaN(Number(value));\n  return isNumberValue ? Number(value) : fallbackValue;\n}\nconst PERFORMANCE_MARK_PREFIX = '🅰️';\nlet enablePerfLogging = false;\n/**\n * Function that will start measuring against the performance API\n * Should be used in pair with stopMeasuring\n */\nfunction startMeasuring(label) {\n  if (!enablePerfLogging) {\n    return;\n  }\n  const {\n    startLabel\n  } = labels(label);\n  /* tslint:disable:ban */\n  performance.mark(startLabel);\n  /* tslint:enable:ban */\n}\n/**\n * Function that will stop measuring against the performance API\n * Should be used in pair with stopMeasuring\n */\nfunction stopMeasuring(label) {\n  if (!enablePerfLogging) {\n    return;\n  }\n  const {\n    startLabel,\n    labelName,\n    endLabel\n  } = labels(label);\n  /* tslint:disable:ban */\n  performance.mark(endLabel);\n  performance.measure(labelName, startLabel, endLabel);\n  performance.clearMarks(startLabel);\n  performance.clearMarks(endLabel);\n  /* tslint:enable:ban */\n}\nfunction labels(label) {\n  const labelName = `${PERFORMANCE_MARK_PREFIX}:${label}`;\n  return {\n    labelName,\n    startLabel: `start:${labelName}`,\n    endLabel: `end:${labelName}`\n  };\n}\nlet warningLogged = false;\n/**\n * This enables an internal performance profiler\n *\n * It should not be imported in application code\n */\nfunction enableProfiling() {\n  if (!warningLogged && (typeof performance === 'undefined' || !performance.mark || !performance.measure)) {\n    warningLogged = true;\n    console.warn('Performance API is not supported on this platform');\n    return;\n  }\n  enablePerfLogging = true;\n}\nfunction disableProfiling() {\n  enablePerfLogging = false;\n}\n\n/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n/**\n * Gets the class name of the closest component to a node.\n * Warning! this function will return minified names if the name of the component is minified. The\n * consumer of the function is responsible for resolving the minified name to its original name.\n * @param node Node from which to start the search.\n */\nfunction getClosestComponentName(node) {\n  let currentNode = node;\n  while (currentNode) {\n    const lView = readPatchedLView(currentNode);\n    if (lView !== null) {\n      for (let i = HEADER_OFFSET; i < lView.length; i++) {\n        const current = lView[i];\n        if (!isLView(current) && !isLContainer(current) || current[HOST] !== currentNode) {\n          continue;\n        }\n        const tView = lView[TVIEW];\n        const tNode = getTNode(tView, i);\n        if (isComponentHost(tNode)) {\n          const def = tView.data[tNode.directiveStart + tNode.componentOffset];\n          const name = def.debugInfo?.className || def.type.name;\n          // Note: the name may be an empty string if the class name is\n          // dropped due to minification. In such cases keep going up the tree.\n          if (name) {\n            return name;\n          } else {\n            break;\n          }\n        }\n      }\n    }\n    currentNode = currentNode.parentNode;\n  }\n  return null;\n}\n\n/**\n * Compiles a partial directive declaration object into a full directive definition object.\n *\n * @codeGenApi\n */\nfunction ɵɵngDeclareDirective(decl) {\n  const compiler = getCompilerFacade({\n    usage: 1 /* JitCompilerUsage.PartialDeclaration */,\n    kind: 'directive',\n    type: decl.type\n  });\n  return compiler.compileDirectiveDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵfac.js`, decl);\n}\n/**\n * Evaluates the class metadata declaration.\n *\n * @codeGenApi\n */\nfunction ɵɵngDeclareClassMetadata(decl) {\n  setClassMetadata(decl.type, decl.decorators, decl.ctorParameters ?? null, decl.propDecorators ?? null);\n}\n/**\n * Evaluates the class metadata of a component that contains deferred blocks.\n *\n * @codeGenApi\n */\nfunction ɵɵngDeclareClassMetadataAsync(decl) {\n  setClassMetadataAsync(decl.type, decl.resolveDeferredDeps, (...types) => {\n    const meta = decl.resolveMetadata(...types);\n    setClassMetadata(decl.type, meta.decorators, meta.ctorParameters, meta.propDecorators);\n  });\n}\n/**\n * Compiles a partial component declaration object into a full component definition object.\n *\n * @codeGenApi\n */\nfunction ɵɵngDeclareComponent(decl) {\n  const compiler = getCompilerFacade({\n    usage: 1 /* JitCompilerUsage.PartialDeclaration */,\n    kind: 'component',\n    type: decl.type\n  });\n  return compiler.compileComponentDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵcmp.js`, decl);\n}\n/**\n * Compiles a partial pipe declaration object into a full pipe definition object.\n *\n * @codeGenApi\n */\nfunction ɵɵngDeclareFactory(decl) {\n  const compiler = getCompilerFacade({\n    usage: 1 /* JitCompilerUsage.PartialDeclaration */,\n    kind: getFactoryKind(decl.target),\n    type: decl.type\n  });\n  return compiler.compileFactoryDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵfac.js`, decl);\n}\nfunction getFactoryKind(target) {\n  switch (target) {\n    case FactoryTarget.Directive:\n      return 'directive';\n    case FactoryTarget.Component:\n      return 'component';\n    case FactoryTarget.Injectable:\n      return 'injectable';\n    case FactoryTarget.Pipe:\n      return 'pipe';\n    case FactoryTarget.NgModule:\n      return 'NgModule';\n  }\n}\n/**\n * Compiles a partial injectable declaration object into a full injectable definition object.\n *\n * @codeGenApi\n */\nfunction ɵɵngDeclareInjectable(decl) {\n  const compiler = getCompilerFacade({\n    usage: 1 /* JitCompilerUsage.PartialDeclaration */,\n    kind: 'injectable',\n    type: decl.type\n  });\n  return compiler.compileInjectableDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵprov.js`, decl);\n}\n/**\n * Compiles a partial injector declaration object into a full injector definition object.\n *\n * @codeGenApi\n */\nfunction ɵɵngDeclareInjector(decl) {\n  const compiler = getCompilerFacade({\n    usage: 1 /* JitCompilerUsage.PartialDeclaration */,\n    kind: 'NgModule',\n    type: decl.type\n  });\n  return compiler.compileInjectorDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵinj.js`, decl);\n}\n/**\n * Compiles a partial NgModule declaration object into a full NgModule definition object.\n *\n * @codeGenApi\n */\nfunction ɵɵngDeclareNgModule(decl) {\n  const compiler = getCompilerFacade({\n    usage: 1 /* JitCompilerUsage.PartialDeclaration */,\n    kind: 'NgModule',\n    type: decl.type\n  });\n  return compiler.compileNgModuleDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵmod.js`, decl);\n}\n/**\n * Compiles a partial pipe declaration object into a full pipe definition object.\n *\n * @codeGenApi\n */\nfunction ɵɵngDeclarePipe(decl) {\n  const compiler = getCompilerFacade({\n    usage: 1 /* JitCompilerUsage.PartialDeclaration */,\n    kind: 'pipe',\n    type: decl.type\n  });\n  return compiler.compilePipeDeclaration(angularCoreEnv, `ng:///${decl.type.name}/ɵpipe.js`, decl);\n}\nconst NOT_SET = /* @__PURE__ */Symbol('NOT_SET');\nconst EMPTY_CLEANUP_SET = /* @__PURE__ */new Set();\nconst AFTER_RENDER_PHASE_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...SIGNAL_NODE,\n  consumerIsAlwaysLive: true,\n  consumerAllowSignalWrites: true,\n  value: NOT_SET,\n  cleanup: null,\n  /** Called when the effect becomes dirty */\n  consumerMarkedDirty() {\n    if (this.sequence.impl.executing) {\n      // If hooks are in the middle of executing, then it matters whether this node has yet been\n      // executed within its sequence. If not, then we don't want to notify the scheduler since\n      // this node will be reached naturally.\n      if (this.sequence.lastPhase === null || this.sequence.lastPhase < this.phase) {\n        return;\n      }\n      // If during the execution of a later phase an earlier phase became dirty, then we should not\n      // run any further phases until the earlier one reruns.\n      this.sequence.erroredOrDestroyed = true;\n    }\n    // Either hooks are not running, or we're marking a node dirty that has already run within its\n    // sequence.\n    this.sequence.scheduler.notify(7 /* NotificationSource.RenderHook */);\n  },\n  phaseFn(previousValue) {\n    this.sequence.lastPhase = this.phase;\n    if (!this.dirty) {\n      return this.signal;\n    }\n    this.dirty = false;\n    if (this.value !== NOT_SET && !consumerPollProducersForChange(this)) {\n      // None of our producers report a change since the last time they were read, so no\n      // recomputation of our value is necessary.\n      return this.signal;\n    }\n    // Run any needed cleanup functions.\n    try {\n      for (const cleanupFn of this.cleanup ?? EMPTY_CLEANUP_SET) {\n        cleanupFn();\n      }\n    } finally {\n      // Even if a cleanup function errors, ensure it's cleared.\n      this.cleanup?.clear();\n    }\n    // Prepare to call the user's effect callback. If there was a previous phase, then it gave us\n    // its value as a `Signal`, otherwise `previousValue` will be `undefined`.\n    const args = [];\n    if (previousValue !== undefined) {\n      args.push(previousValue);\n    }\n    args.push(this.registerCleanupFn);\n    // Call the user's callback in our reactive context.\n    const prevConsumer = consumerBeforeComputation(this);\n    let newValue;\n    try {\n      newValue = this.userFn.apply(null, args);\n    } finally {\n      consumerAfterComputation(this, prevConsumer);\n    }\n    if (this.value === NOT_SET || !this.equal(this.value, newValue)) {\n      this.value = newValue;\n      this.version++;\n    }\n    return this.signal;\n  }\n}))();\n/**\n * An `AfterRenderSequence` that manages an `afterRenderEffect`'s phase effects.\n */\nclass AfterRenderEffectSequence extends AfterRenderSequence {\n  scheduler;\n  /**\n   * While this sequence is executing, this tracks the last phase which was called by the\n   * `afterRender` machinery.\n   *\n   * When a phase effect is marked dirty, this is used to determine whether it's already run or not.\n   */\n  lastPhase = null;\n  /**\n   * The reactive nodes for each phase, if a phase effect is defined for that phase.\n   *\n   * These are initialized to `undefined` but set in the constructor.\n   */\n  nodes = [undefined, undefined, undefined, undefined];\n  constructor(impl, effectHooks, view, scheduler, destroyRef, snapshot = null) {\n    // Note that we also initialize the underlying `AfterRenderSequence` hooks to `undefined` and\n    // populate them as we create reactive nodes below.\n    super(impl, [undefined, undefined, undefined, undefined], view, false, destroyRef, snapshot);\n    this.scheduler = scheduler;\n    // Setup a reactive node for each phase.\n    for (const phase of AFTER_RENDER_PHASES) {\n      const effectHook = effectHooks[phase];\n      if (effectHook === undefined) {\n        continue;\n      }\n      const node = Object.create(AFTER_RENDER_PHASE_EFFECT_NODE);\n      node.sequence = this;\n      node.phase = phase;\n      node.userFn = effectHook;\n      node.dirty = true;\n      node.signal = () => {\n        producerAccessed(node);\n        return node.value;\n      };\n      node.signal[SIGNAL] = node;\n      node.registerCleanupFn = fn => (node.cleanup ??= new Set()).add(fn);\n      this.nodes[phase] = node;\n      // Install the upstream hook which runs the `phaseFn` for this phase.\n      this.hooks[phase] = value => node.phaseFn(value);\n    }\n  }\n  afterRun() {\n    super.afterRun();\n    // We're done running this sequence, so reset `lastPhase`.\n    this.lastPhase = null;\n  }\n  destroy() {\n    super.destroy();\n    // Run the cleanup functions for each node.\n    for (const node of this.nodes) {\n      for (const fn of node?.cleanup ?? EMPTY_CLEANUP_SET) {\n        fn();\n      }\n    }\n  }\n}\n/**\n * @publicApi\n */\nfunction afterRenderEffect(callbackOrSpec, options) {\n  ngDevMode && assertNotInReactiveContext(afterRenderEffect, 'Call `afterRenderEffect` outside of a reactive context. For example, create the render ' + 'effect inside the component constructor`.');\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(afterRenderEffect);\n  }\n  if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n    return NOOP_AFTER_RENDER_REF;\n  }\n  const injector = options?.injector ?? inject(Injector);\n  const scheduler = injector.get(ChangeDetectionScheduler);\n  const manager = injector.get(AfterRenderManager);\n  const tracing = injector.get(TracingService, null, {\n    optional: true\n  });\n  manager.impl ??= injector.get(AfterRenderImpl);\n  let spec = callbackOrSpec;\n  if (typeof spec === 'function') {\n    spec = {\n      mixedReadWrite: callbackOrSpec\n    };\n  }\n  const viewContext = injector.get(ViewContext, null, {\n    optional: true\n  });\n  const sequence = new AfterRenderEffectSequence(manager.impl, [spec.earlyRead, spec.write, spec.mixedReadWrite, spec.read], viewContext?.view, scheduler, injector.get(DestroyRef), tracing?.snapshot(null));\n  manager.impl.register(sequence);\n  return sequence;\n}\n\n/**\n * Creates a `ComponentRef` instance based on provided component type and a set of options.\n *\n * @usageNotes\n *\n * The example below demonstrates how the `createComponent` function can be used\n * to create an instance of a ComponentRef dynamically and attach it to an ApplicationRef,\n * so that it gets included into change detection cycles.\n *\n * Note: the example uses standalone components, but the function can also be used for\n * non-standalone components (declared in an NgModule) as well.\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   template: `Hello {{ name }}!`\n * })\n * class HelloComponent {\n *   name = 'Angular';\n * }\n *\n * @Component({\n *   standalone: true,\n *   template: `<div id=\"hello-component-host\"></div>`\n * })\n * class RootComponent {}\n *\n * // Bootstrap an application.\n * const applicationRef = await bootstrapApplication(RootComponent);\n *\n * // Locate a DOM node that would be used as a host.\n * const hostElement = document.getElementById('hello-component-host');\n *\n * // Get an `EnvironmentInjector` instance from the `ApplicationRef`.\n * const environmentInjector = applicationRef.injector;\n *\n * // We can now create a `ComponentRef` instance.\n * const componentRef = createComponent(HelloComponent, {hostElement, environmentInjector});\n *\n * // Last step is to register the newly created ref using the `ApplicationRef` instance\n * // to include the component view into change detection cycles.\n * applicationRef.attachView(componentRef.hostView);\n * componentRef.changeDetectorRef.detectChanges();\n * ```\n *\n * @param component Component class reference.\n * @param options Set of options to use:\n *  * `environmentInjector`: An `EnvironmentInjector` instance to be used for the component.\n *  * `hostElement` (optional): A DOM node that should act as a host node for the component. If not\n * provided, Angular creates one based on the tag name used in the component selector (and falls\n * back to using `div` if selector doesn't have tag name info).\n *  * `elementInjector` (optional): An `ElementInjector` instance, see additional info about it\n * [here](guide/di/hierarchical-dependency-injection#elementinjector).\n *  * `projectableNodes` (optional): A list of DOM nodes that should be projected through\n * [`<ng-content>`](api/core/ng-content) of the new component instance, e.g.,\n * `[[element1, element2]]`: projects `element1` and `element2` into the same `<ng-content>`.\n * `[[element1, element2], [element3]]`: projects `element1` and `element2` into one `<ng-content>`,\n * and `element3` into a separate `<ng-content>`.\n *  * `directives` (optional): Directives that should be applied to the component.\n *  * `binding` (optional): Bindings to apply to the root component.\n * @returns ComponentRef instance that represents a given Component.\n *\n * @publicApi\n */\nfunction createComponent(component, options) {\n  ngDevMode && assertComponentDef(component);\n  const componentDef = getComponentDef(component);\n  const elementInjector = options.elementInjector || getNullInjector();\n  const factory = new ComponentFactory(componentDef);\n  return factory.create(elementInjector, options.projectableNodes, options.hostElement, options.environmentInjector, options.directives, options.bindings);\n}\n/**\n * Creates an object that allows to retrieve component metadata.\n *\n * @usageNotes\n *\n * The example below demonstrates how to use the function and how the fields\n * of the returned object map to the component metadata.\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   selector: 'foo-component',\n *   template: `\n *     <ng-content></ng-content>\n *     <ng-content select=\"content-selector-a\"></ng-content>\n *   `,\n * })\n * class FooComponent {\n *   @Input('inputName') inputPropName: string;\n *   @Output('outputName') outputPropName = new EventEmitter<void>();\n * }\n *\n * const mirror = reflectComponentType(FooComponent);\n * expect(mirror.type).toBe(FooComponent);\n * expect(mirror.selector).toBe('foo-component');\n * expect(mirror.isStandalone).toBe(true);\n * expect(mirror.inputs).toEqual([{propName: 'inputName', templateName: 'inputPropName'}]);\n * expect(mirror.outputs).toEqual([{propName: 'outputName', templateName: 'outputPropName'}]);\n * expect(mirror.ngContentSelectors).toEqual([\n *   '*',                 // first `<ng-content>` in a template, the selector defaults to `*`\n *   'content-selector-a' // second `<ng-content>` in a template\n * ]);\n * ```\n *\n * @param component Component class reference.\n * @returns An object that allows to retrieve component metadata.\n *\n * @publicApi\n */\nfunction reflectComponentType(component) {\n  const componentDef = getComponentDef(component);\n  if (!componentDef) return null;\n  const factory = new ComponentFactory(componentDef);\n  return {\n    get selector() {\n      return factory.selector;\n    },\n    get type() {\n      return factory.componentType;\n    },\n    get inputs() {\n      return factory.inputs;\n    },\n    get outputs() {\n      return factory.outputs;\n    },\n    get ngContentSelectors() {\n      return factory.ngContentSelectors;\n    },\n    get isStandalone() {\n      return componentDef.standalone;\n    },\n    get isSignal() {\n      return componentDef.signals;\n    }\n  };\n}\n\n/**\n * Merge multiple application configurations from left to right.\n *\n * @param configs Two or more configurations to be merged.\n * @returns A merged [ApplicationConfig](api/core/ApplicationConfig).\n *\n * @publicApi\n */\nfunction mergeApplicationConfig(...configs) {\n  return configs.reduce((prev, curr) => {\n    return Object.assign(prev, curr, {\n      providers: [...prev.providers, ...curr.providers]\n    });\n  }, {\n    providers: []\n  });\n}\n\n/**\n * Injection token representing the current HTTP request object.\n *\n * Use this token to access the current request when handling server-side\n * rendering (SSR).\n *\n * @remarks\n * This token may be `null` in the following scenarios:\n *\n * * During the build processes.\n * * When the application is rendered in the browser (client-side rendering).\n * * When performing static site generation (SSG).\n * * During route extraction in development (at the time of the request).\n *\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Request `Request` on MDN}\n *\n * @publicApi\n */\nconst REQUEST = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'REQUEST' : '', {\n  providedIn: 'platform',\n  factory: () => null\n});\n/**\n * Injection token for response initialization options.\n *\n * Use this token to provide response options for configuring or initializing\n * HTTP responses in server-side rendering or API endpoints.\n *\n * @remarks\n * This token may be `null` in the following scenarios:\n *\n * * During the build processes.\n * * When the application is rendered in the browser (client-side rendering).\n * * When performing static site generation (SSG).\n * * During route extraction in development (at the time of the request).\n *\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Response/Response `ResponseInit` on MDN}\n *\n * @publicApi\n */\nconst RESPONSE_INIT = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'RESPONSE_INIT' : '', {\n  providedIn: 'platform',\n  factory: () => null\n});\n/**\n * Injection token for additional request context.\n *\n * Use this token to pass custom metadata or context related to the current request in server-side rendering.\n *\n * @remarks\n * This token is only available during server-side rendering and will be `null` in other contexts.\n *\n * @publicApi\n */\nconst REQUEST_CONTEXT = /*#__PURE__*/new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'REQUEST_CONTEXT' : '', {\n  providedIn: 'platform',\n  factory: () => null\n});\nexport { APP_BOOTSTRAP_LISTENER, APP_ID, ApplicationInitStatus, ApplicationModule, ApplicationRef, COMPILER_OPTIONS, ChangeDetectorRef, ContentChild, ContentChildren, DefaultIterableDiffer, DestroyRef, ENVIRONMENT_INITIALIZER, EmbeddedViewRef, ErrorHandler, HOST_TAG_NAME, HostAttributeToken, Injectable, InjectionToken, Injector, IterableDiffers, KeyValueDiffers, LOCALE_ID, NgModule, NgZone, Optional, OutputEmitterRef, PLATFORM_INITIALIZER, PlatformRef, Query, REQUEST, REQUEST_CONTEXT, RESPONSE_INIT, SkipSelf, TransferState, VERSION, Version, ViewChild, ViewChildren, ViewEncapsulation$1 as ViewEncapsulation, ViewRef, afterRenderEffect, assertInInjectionContext, assertNotInReactiveContext, assertPlatform, booleanAttribute, contentChild, contentChildren, createComponent, createPlatform, createPlatformFactory, destroyPlatform, enableProdMode, getModuleFactory, getNgModuleById, getPlatform, inject, input, isDevMode, makeEnvironmentProviders, mergeApplicationConfig, model, numberAttribute, output, platformCore, provideCheckNoChangesConfig, provideEnvironmentInitializer, providePlatformInitializer, reflectComponentType, runInInjectionContext, viewChild, viewChildren, ALLOW_MULTIPLE_PLATFORMS as ɵALLOW_MULTIPLE_PLATFORMS, AfterRenderManager as ɵAfterRenderManager, CLIENT_RENDER_MODE_FLAG as ɵCLIENT_RENDER_MODE_FLAG, CONTAINER_HEADER_OFFSET as ɵCONTAINER_HEADER_OFFSET, ChangeDetectionScheduler as ɵChangeDetectionScheduler, ChangeDetectionSchedulerImpl as ɵChangeDetectionSchedulerImpl, Console as ɵConsole, DEFAULT_LOCALE_ID as ɵDEFAULT_LOCALE_ID, DEHYDRATED_BLOCK_REGISTRY as ɵDEHYDRATED_BLOCK_REGISTRY, ENABLE_ROOT_COMPONENT_BOOTSTRAP as ɵENABLE_ROOT_COMPONENT_BOOTSTRAP, IMAGE_CONFIG as ɵIMAGE_CONFIG, INJECTOR_SCOPE as ɵINJECTOR_SCOPE, ɵINPUT_SIGNAL_BRAND_WRITE_TYPE, INTERNAL_APPLICATION_ERROR_HANDLER as ɵINTERNAL_APPLICATION_ERROR_HANDLER, IS_HYDRATION_DOM_REUSE_ENABLED as ɵIS_HYDRATION_DOM_REUSE_ENABLED, IS_INCREMENTAL_HYDRATION_ENABLED as ɵIS_INCREMENTAL_HYDRATION_ENABLED, JSACTION_BLOCK_ELEMENT_MAP as ɵJSACTION_BLOCK_ELEMENT_MAP, JSACTION_EVENT_CONTRACT as ɵJSACTION_EVENT_CONTRACT, NgModuleFactory as ɵNgModuleFactory, PERFORMANCE_MARK_PREFIX as ɵPERFORMANCE_MARK_PREFIX, PROVIDED_NG_ZONE as ɵPROVIDED_NG_ZONE, ComponentFactory as ɵRender3ComponentFactory, RuntimeError as ɵRuntimeError, SIGNAL as ɵSIGNAL, TracingService as ɵTracingService, ViewRef$1 as ɵViewRef, annotateForHydration as ɵannotateForHydration, compileNgModuleFactory as ɵcompileNgModuleFactory, createOrReusePlatformInjector as ɵcreateOrReusePlatformInjector, defaultIterableDiffers as ɵdefaultIterableDiffers, defaultKeyValueDiffers as ɵdefaultKeyValueDiffers, disableProfiling as ɵdisableProfiling, enableProfiling as ɵenableProfiling, formatRuntimeError as ɵformatRuntimeError, getClosestComponentName as ɵgetClosestComponentName, getComponentDef as ɵgetComponentDef, getDocument as ɵgetDocument, _global as ɵglobal, injectChangeDetectorRef as ɵinjectChangeDetectorRef, internalCreateApplication as ɵinternalCreateApplication, internalProvideZoneChangeDetection as ɵinternalProvideZoneChangeDetection, isPromise as ɵisPromise, performanceMarkFeature as ɵperformanceMarkFeature, resolveComponentResources as ɵresolveComponentResources, setClassMetadata as ɵsetClassMetadata, setClassMetadataAsync as ɵsetClassMetadataAsync, setLocaleId as ɵsetLocaleId, startMeasuring as ɵstartMeasuring, stopMeasuring as ɵstopMeasuring, stringify as ɵstringify, withDomHydration as ɵwithDomHydration, withEventReplay as ɵwithEventReplay, withI18nSupport as ɵwithI18nSupport, withIncrementalHydration as ɵwithIncrementalHydration, FactoryTarget as ɵɵFactoryTarget, __defineInjectable as ɵɵdefineInjectable, __defineInjector as ɵɵdefineInjector, __defineNgModule as ɵɵdefineNgModule, __inject as ɵɵinject, __injectAttribute as ɵɵinjectAttribute, ɵɵngDeclareClassMetadata, ɵɵngDeclareClassMetadataAsync, ɵɵngDeclareComponent, ɵɵngDeclareDirective, ɵɵngDeclareFactory, ɵɵngDeclareInjectable, ɵɵngDeclareInjector, ɵɵngDeclareNgModule, ɵɵngDeclarePipe };\n//# sourceMappingURL=core.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}