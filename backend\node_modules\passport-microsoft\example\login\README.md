# Example Express Application using passport-microsoft

This is a simple example application that uses passport-microsoft and passport.js to allow you to log in and see profile information.

It is inteded to be used as a reference for getting started with passport-microsoft.

## Running the App

Run the app by installing dependencies:

    npm install

And then executing the start command:

    npm start

Navigate to http://localhost:3000 to see the web user interface.

## Credits

  - [<PERSON>](https://www.seafish.io) - [passport-microsoft on Github](https://github.com/seanfisher/passport-microsoft)

  Copyright (c) 2022 <PERSON> <[seafish.io](https://www.seafish.io)>

## License

  - [The MIT License](http://opensource.org/licenses/MIT)
