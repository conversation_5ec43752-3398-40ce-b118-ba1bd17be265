{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\n// Components\nimport { ProfileComponent } from './profile.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProfileComponent\n}];\nexport let ProfileModule = /*#__PURE__*/(() => {\n  class ProfileModule {\n    static #_ = this.ɵfac = function ProfileModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProfileModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(routes), MatCardModule, MatButtonModule, MatIconModule]\n    });\n  }\n  return ProfileModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}