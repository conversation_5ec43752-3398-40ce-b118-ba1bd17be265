{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const ObjectUnsubscribedError = createErrorClass(_super => function ObjectUnsubscribedErrorImpl() {\n  _super(this);\n  this.name = 'ObjectUnsubscribedError';\n  this.message = 'object unsubscribed';\n});\n//# sourceMappingURL=ObjectUnsubscribedError.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}