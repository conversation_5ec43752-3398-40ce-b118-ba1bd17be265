"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OtpController = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let OtpController = class OtpController {
    constructor(userRepository, securityService, emailService, smsService) {
        this.userRepository = userRepository;
        this.securityService = securityService;
        this.emailService = emailService;
        this.smsService = smsService;
    }
    async sendOTP(request) {
        if (!request.email && !request.phone) {
            throw new rest_1.HttpErrors.BadRequest('Either email or phone is required');
        }
        if (request.email) {
            // Check if user exists for login type
            if (request.type === 'login') {
                const user = await this.userRepository.findOne({
                    where: { email: request.email },
                });
                if (!user) {
                    throw new rest_1.HttpErrors.NotFound('User not found');
                }
                if (!user.emailVerified) {
                    // In development, allow OTP for unverified emails with warning
                    if (process.env.NODE_ENV === 'development') {
                        console.log('⚠️  Warning: Sending OTP to unverified email in development mode');
                    }
                    else {
                        throw new rest_1.HttpErrors.BadRequest('Email not verified. Please verify your email first.');
                    }
                }
            }
            const otp = await this.securityService.generateOTP(request.email, request.type);
            await this.emailService.sendOTPEmail(request.email, otp, request.type);
            return { message: 'OTP sent to email successfully' };
        }
        if (request.phone) {
            // Check if user exists for login type
            if (request.type === 'login') {
                const user = await this.userRepository.findOne({
                    where: { phone: request.phone },
                });
                if (!user) {
                    throw new rest_1.HttpErrors.NotFound('User not found');
                }
                // For development: Send OTP to email instead of phone
                if (process.env.NODE_ENV === 'development') {
                    console.log('📱 Development mode: Sending OTP to email instead of phone');
                    const otp = await this.securityService.generateOTP(user.email, request.type);
                    await this.emailService.sendOTPEmail(user.email, otp, request.type);
                    return { message: 'OTP sent to email (associated with phone) successfully' };
                }
                if (!user.phoneVerified) {
                    throw new rest_1.HttpErrors.BadRequest('Phone not verified');
                }
            }
            const otp = await this.securityService.generateOTP(request.phone, request.type);
            await this.smsService.sendOTP(request.phone, otp, request.type);
            return { message: 'OTP sent to phone successfully' };
        }
        throw new rest_1.HttpErrors.BadRequest('Invalid request');
    }
    async sendEmailOTP(request) {
        // Check if user exists for login type
        if (request.type === 'login') {
            const user = await this.userRepository.findOne({
                where: { email: request.email },
            });
            if (!user) {
                throw new rest_1.HttpErrors.NotFound('User not found');
            }
            if (!user.emailVerified) {
                // In development, allow OTP for unverified emails with warning
                if (process.env.NODE_ENV === 'development') {
                    console.log('⚠️  Warning: Sending OTP to unverified email in development mode');
                }
                else {
                    throw new rest_1.HttpErrors.BadRequest('Email not verified. Please verify your email first.');
                }
            }
        }
        const otp = await this.securityService.generateOTP(request.email, request.type);
        await this.emailService.sendOTPEmail(request.email, otp, request.type);
        return { message: 'OTP sent successfully' };
    }
    async sendSMSOTP(request) {
        // Check if user exists for login type
        if (request.type === 'login') {
            const user = await this.userRepository.findOne({
                where: { phone: request.phone },
            });
            if (!user) {
                throw new rest_1.HttpErrors.NotFound('User not found');
            }
            if (!user.phoneVerified) {
                throw new rest_1.HttpErrors.BadRequest('Phone not verified');
            }
        }
        const otp = await this.securityService.generateOTP(request.phone, request.type);
        await this.smsService.sendOTP(request.phone, otp, request.type);
        return { message: 'OTP sent successfully' };
    }
    async verifyOTP(request) {
        const isValid = await this.securityService.verifyOTP(request.identifier, request.code, request.type);
        if (isValid && request.type === 'verification') {
            // Mark email or phone as verified
            const isEmail = request.identifier.includes('@');
            if (isEmail) {
                const user = await this.userRepository.findOne({
                    where: { email: request.identifier },
                });
                if (user) {
                    await this.userRepository.updateById(user.id, {
                        emailVerified: true,
                        updatedAt: new Date(),
                    });
                }
            }
            else {
                const user = await this.userRepository.findOne({
                    where: { phone: request.identifier },
                });
                if (user) {
                    await this.userRepository.updateById(user.id, {
                        phoneVerified: true,
                        updatedAt: new Date(),
                    });
                }
            }
        }
        return {
            valid: isValid,
            message: isValid ? 'OTP verified successfully' : 'Invalid or expired OTP',
        };
    }
    async loginWithOTP(request) {
        // Verify OTP
        const isValid = await this.securityService.verifyOTP(request.identifier, request.code, 'login');
        if (!isValid) {
            throw new rest_1.HttpErrors.Unauthorized('Invalid or expired OTP');
        }
        // Find user
        const isEmail = request.identifier.includes('@');
        const whereClause = isEmail
            ? { email: request.identifier }
            : { phone: request.identifier };
        const user = await this.userRepository.findOne({
            where: whereClause,
        });
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        if (!user.isActive) {
            throw new rest_1.HttpErrors.Unauthorized('Account is deactivated');
        }
        // Update last login
        await this.userRepository.updateById(user.id, {
            lastLoginAt: new Date(),
            updatedAt: new Date(),
        });
        // Generate proper JWT token
        const userProfile = {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            roles: user.roles,
        };
        // Use the same JWT service as regular login
        const jwt = require('jsonwebtoken');
        const token = jwt.sign({
            sub: user.id,
            email: user.email,
            roles: user.roles,
        }, process.env.JWT_SECRET, {
            expiresIn: process.env.JWT_EXPIRES_IN || '24h',
            algorithm: 'HS256',
        });
        return {
            token,
            user: userProfile,
        };
    }
};
exports.OtpController = OtpController;
tslib_1.__decorate([
    (0, rest_1.post)('/otp/send'),
    (0, rest_1.response)(200, {
        description: 'Send OTP via email or SMS',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['type'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        phone: { type: 'string' },
                        type: { type: 'string', enum: ['login', 'verification'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "sendOTP", null);
tslib_1.__decorate([
    (0, rest_1.post)('/otp/send-email'),
    (0, rest_1.response)(200, {
        description: 'Send OTP via email',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email', 'type'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        type: { type: 'string', enum: ['login', 'verification'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "sendEmailOTP", null);
tslib_1.__decorate([
    (0, rest_1.post)('/otp/send-sms'),
    (0, rest_1.response)(200, {
        description: 'Send OTP via SMS',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['phone', 'type'],
                    properties: {
                        phone: { type: 'string' },
                        type: { type: 'string', enum: ['login', 'verification'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "sendSMSOTP", null);
tslib_1.__decorate([
    (0, rest_1.post)('/otp/verify'),
    (0, rest_1.response)(200, {
        description: 'Verify OTP',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        valid: { type: 'boolean' },
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['identifier', 'code', 'type'],
                    properties: {
                        identifier: { type: 'string' }, // email or phone
                        code: { type: 'string' },
                        type: { type: 'string', enum: ['login', 'verification'] },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "verifyOTP", null);
tslib_1.__decorate([
    (0, rest_1.post)('/otp/login'),
    (0, rest_1.response)(200, {
        description: 'Login with OTP',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        user: { type: 'object' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['identifier', 'code'],
                    properties: {
                        identifier: { type: 'string' }, // email or phone
                        code: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OtpController.prototype, "loginWithOTP", null);
exports.OtpController = OtpController = tslib_1.__decorate([
    tslib_1.__param(0, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(1, (0, core_1.inject)('services.SecurityService')),
    tslib_1.__param(2, (0, core_1.inject)('services.EmailService')),
    tslib_1.__param(3, (0, core_1.inject)('services.SmsService')),
    tslib_1.__metadata("design:paramtypes", [repositories_1.UserRepository,
        services_1.SecurityService,
        services_1.EmailService,
        services_1.SmsService])
], OtpController);
//# sourceMappingURL=otp.controller.js.map