<div class="auth-container">
  <div class="auth-card fade-in">
    <!-- Header -->
    <div class="auth-header">
      <h1>Welcome Back</h1>
      <p>Sign in to your secure account</p>
      <div class="security-badge">
        <mat-icon>security</mat-icon>
        Secure Login
      </div>
    </div>

    <div class="auth-content">
      <!-- Regular Login Form -->
      <form *ngIf="!showOTPLogin && !showTwoFactor" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <mat-form-field class="form-field" appearance="outline">
          <mat-label>Email Address</mat-label>
          <input matInput type="email" formControlName="email" autocomplete="email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error>{{ getFieldError(loginForm, 'email') }}</mat-error>
        </mat-form-field>

        <mat-form-field class="form-field" appearance="outline">
          <mat-label>Password</mat-label>
          <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" autocomplete="current-password">
          <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error>{{ getFieldError(loginForm, 'password') }}</mat-error>
        </mat-form-field>

        <button mat-raised-button color="primary" type="submit" class="submit-button" [disabled]="loading">
          <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
          <span *ngIf="!loading">Sign In</span>
        </button>

        <div class="divider">
          <span>or</span>
        </div>

        <button mat-stroked-button type="button" class="w-100" (click)="toggleOTPLogin()">
          <mat-icon>sms</mat-icon>
          Login with OTP
        </button>

        <!-- OAuth Login Buttons -->
        <div class="oauth-section mt-3">
          <div class="divider">
            <span>or continue with</span>
          </div>

          <div class="oauth-buttons">
            <button *ngFor="let provider of oauthProviders"
                    mat-stroked-button
                    type="button"
                    class="oauth-button"
                    [style.border-color]="provider.color"
                    (click)="loginWithOAuth(provider.name)"
                    [disabled]="loading">
              <i [class]="provider.icon" [style.color]="provider.color"></i>
              <span>{{ provider.displayName }}</span>
            </button>
          </div>
        </div>

        <div class="text-center mt-3">
          <a routerLink="/auth/forgot-password" class="text-primary">Forgot Password?</a>
        </div>

        <div class="text-center mt-2">
          <span>Don't have an account? </span>
          <a routerLink="/auth/register" class="text-primary">Sign Up</a>
        </div>
      </form>

      <!-- Two-Factor Authentication Form -->
      <form *ngIf="showTwoFactor" [formGroup]="twoFactorForm" (ngSubmit)="onTwoFactorSubmit()">
        <div class="text-center mb-3">
          <mat-icon color="primary" style="font-size: 48px; width: 48px; height: 48px;">security</mat-icon>
          <h3>Two-Factor Authentication</h3>
          <p>Enter the 6-digit code from your authenticator app</p>
        </div>

        <mat-form-field class="form-field" appearance="outline">
          <mat-label>Authentication Code</mat-label>
          <input matInput formControlName="twoFactorToken" placeholder="000000" maxlength="6" autocomplete="one-time-code">
          <mat-icon matSuffix>verified_user</mat-icon>
          <mat-error>{{ getFieldError(twoFactorForm, 'twoFactorToken') }}</mat-error>
        </mat-form-field>

        <button mat-raised-button color="primary" type="submit" class="submit-button" [disabled]="loading">
          <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
          <span *ngIf="!loading">Verify & Sign In</span>
        </button>

        <button mat-button type="button" class="w-100 mt-2" (click)="backToLogin()">
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </form>

      <!-- OTP Login Form -->
      <form *ngIf="showOTPLogin" [formGroup]="otpForm">
        <div class="text-center mb-3">
          <mat-icon color="primary" style="font-size: 48px; width: 48px; height: 48px;">sms</mat-icon>
          <h3>Login with OTP</h3>
          <p>Enter your email or phone number to receive a one-time password</p>
        </div>

        <mat-form-field class="form-field" appearance="outline">
          <mat-label>Email or Phone Number</mat-label>
          <input matInput formControlName="identifier" placeholder="<EMAIL> or +1234567890">
          <mat-icon matSuffix>contact_mail</mat-icon>
          <mat-error>{{ getFieldError(otpForm, 'identifier') }}</mat-error>
        </mat-form-field>

        <button *ngIf="!otpSent" mat-raised-button color="accent" type="button" class="submit-button" 
                (click)="sendOTP()" [disabled]="loading">
          <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
          <span *ngIf="!loading">Send OTP</span>
        </button>

        <div *ngIf="otpSent">
          <mat-form-field class="form-field" appearance="outline">
            <mat-label>Enter OTP</mat-label>
            <input matInput formControlName="code" placeholder="000000" maxlength="6" autocomplete="one-time-code">
            <mat-icon matSuffix>lock</mat-icon>
            <mat-error>{{ getFieldError(otpForm, 'code') }}</mat-error>
          </mat-form-field>

          <button mat-raised-button color="primary" type="button" class="submit-button" 
                  (click)="loginWithOTP()" [disabled]="loading">
            <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
            <span *ngIf="!loading">Verify & Sign In</span>
          </button>

          <button mat-button type="button" class="w-100 mt-2" (click)="sendOTP()">
            <mat-icon>refresh</mat-icon>
            Resend OTP
          </button>
        </div>

        <button mat-button type="button" class="w-100 mt-2" (click)="toggleOTPLogin()">
          <mat-icon>arrow_back</mat-icon>
          Back to Login
        </button>
      </form>
    </div>
  </div>
</div>
