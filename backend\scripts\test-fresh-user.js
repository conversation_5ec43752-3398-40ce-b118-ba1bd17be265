const axios = require('axios');
const speakeasy = require('speakeasy');

const BASE_URL = 'http://localhost:3002';
let authToken = '';
let twoFactorSecret = '';

// Fresh test user data with timestamp
const timestamp = Date.now();
const testUser = {
  email: `fresh-test-${timestamp}@example.com`,
  password: 'FreshTestPassword123!',
  firstName: 'Fresh',
  lastName: 'User',
  phone: `+123456${timestamp.toString().slice(-4)}`
};

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
}

async function testCompleteFlow() {
  console.log('🚀 Testing Complete Authentication Flow with Fresh User');
  console.log('=' .repeat(60));
  console.log('👤 Test User:', testUser.email);
  console.log('=' .repeat(60));

  // 1. User Signup
  console.log('\n1️⃣  User Signup');
  const signupResult = await apiCall('POST', '/auth/signup', testUser);
  if (signupResult.success) {
    console.log('✅ User registered successfully');
    console.log('📧 Verification email sent (check console logs)');
  } else {
    console.log('❌ Signup failed:', signupResult.error);
    return;
  }

  // 2. User Login
  console.log('\n2️⃣  User Login');
  const loginResult = await apiCall('POST', '/auth/login', {
    email: testUser.email,
    password: testUser.password
  });
  
  if (loginResult.success) {
    authToken = loginResult.data.token;
    console.log('✅ Login successful');
    console.log('🔑 JWT token received');
  } else {
    console.log('❌ Login failed:', loginResult.error);
    return;
  }

  // 3. 2FA Setup
  console.log('\n3️⃣  2FA Setup');
  const setup2FAResult = await apiCall('POST', '/2fa/setup', null, authToken);
  if (setup2FAResult.success) {
    twoFactorSecret = setup2FAResult.data.secret;
    console.log('✅ 2FA setup successful');
    console.log('🔐 Secret:', twoFactorSecret);
    console.log('📱 QR Code generated for authenticator apps');
  } else {
    console.log('❌ 2FA setup failed:', setup2FAResult.error);
  }

  // 4. Enable 2FA
  if (twoFactorSecret) {
    console.log('\n4️⃣  Enable 2FA');
    const token = speakeasy.totp({
      secret: twoFactorSecret,
      encoding: 'base32'
    });
    
    console.log('🔢 Generated TOTP token:', token);
    
    const verify2FAResult = await apiCall('POST', '/2fa/verify', { token }, authToken);
    if (verify2FAResult.success) {
      console.log('✅ 2FA enabled successfully');
    } else {
      console.log('❌ 2FA verification failed:', verify2FAResult.error);
    }
  }

  // 5. 2FA Email OTP
  console.log('\n5️⃣  2FA Email OTP');
  const emailOTPResult = await apiCall('POST', '/2fa/send-email', null, authToken);
  if (emailOTPResult.success) {
    console.log('✅ 2FA email OTP sent');
    console.log('📧 Check console logs for email content');
  } else {
    console.log('❌ Email OTP failed:', emailOTPResult.error);
  }

  // 6. Change Password
  console.log('\n6️⃣  Change Password');
  const newPassword = 'NewFreshPassword456!';
  let changePasswordData = {
    currentPassword: testUser.password,
    newPassword: newPassword
  };

  // Add 2FA token if enabled
  if (twoFactorSecret) {
    const token = speakeasy.totp({
      secret: twoFactorSecret,
      encoding: 'base32'
    });
    changePasswordData.twoFactorToken = token;
    console.log('🔢 Using 2FA token for password change:', token);
  }

  const changePasswordResult = await apiCall('POST', '/auth/change-password', changePasswordData, authToken);
  if (changePasswordResult.success) {
    console.log('✅ Password changed successfully');
    testUser.password = newPassword; // Update for future use
  } else {
    console.log('❌ Password change failed:', changePasswordResult.error);
  }

  // 7. OAuth URLs
  console.log('\n7️⃣  OAuth URLs');
  const providers = ['google', 'github', 'microsoft'];
  for (const provider of providers) {
    const oauthResult = await apiCall('GET', `/auth/oauth/${provider}/url`);
    if (oauthResult.success) {
      console.log(`✅ ${provider.toUpperCase()} OAuth URL generated`);
      console.log(`   ${oauthResult.data.url.substring(0, 80)}...`);
    } else {
      console.log(`❌ ${provider} OAuth failed:`, oauthResult.error);
    }
  }

  // 8. Test New OTP Endpoint
  console.log('\n8️⃣  Unified OTP Endpoint');
  const otpResult = await apiCall('POST', '/otp/send', {
    email: testUser.email,
    type: 'login'
  });
  if (otpResult.success) {
    console.log('✅ Unified OTP endpoint working');
    console.log('📧 OTP sent via email');
  } else {
    console.log('❌ Unified OTP failed:', otpResult.error);
  }

  console.log('\n' + '=' .repeat(60));
  console.log('🎉 Complete Authentication Flow Test Completed!');
  console.log('=' .repeat(60));
  console.log('✨ Key Features Tested:');
  console.log('   ✅ User Registration & Login');
  console.log('   ✅ 2FA Setup & Verification (TOTP)');
  console.log('   ✅ Email OTP for 2FA');
  console.log('   ✅ Password Change with 2FA');
  console.log('   ✅ OAuth URL Generation');
  console.log('   ✅ Unified OTP Endpoint');
  console.log('');
  console.log('🔧 Ready for Frontend Integration:');
  console.log('   • Backend: http://localhost:3002');
  console.log('   • Frontend: http://localhost:4200');
  console.log('   • API Explorer: http://localhost:3002/explorer');
  console.log('');
  console.log('📱 2FA Compatible with:');
  console.log('   • Google Authenticator');
  console.log('   • Microsoft Authenticator');
  console.log('   • Authy (by Twilio)');
  console.log('   • Any TOTP-compatible app');
}

// Run the test
if (require.main === module) {
  testCompleteFlow().catch(console.error);
}

module.exports = { testCompleteFlow };
