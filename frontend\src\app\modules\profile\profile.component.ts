import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../services/auth.service';
import { TwoFactorService } from '../../services/two-factor.service';
import { OAuthService } from '../../services/oauth.service';
import { User, ChangePasswordRequest } from '../../models/user.model';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
  standalone: false
})
export class ProfileComponent implements OnInit {
  currentUser: User | null = null;
  changePasswordForm: FormGroup;
  twoFactorStatus = { enabled: false };

  loading = false;
  hideCurrentPassword = true;
  hideNewPassword = true;
  hideConfirmPassword = true;
  showChangePassword = false;

  constructor(
    private authService: AuthService,
    private twoFactorService: TwoFactorService,
    private oauthService: OAuthService,
    private formBuilder: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.changePasswordForm = this.formBuilder.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]],
      twoFactorToken: ['']
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    this.currentUser = this.authService.currentUserValue;
    this.load2FAStatus();
  }

  load2FAStatus(): void {
    this.twoFactorService.get2FAStatus().subscribe({
      next: (status) => {
        this.twoFactorStatus = status;
      },
      error: (error) => {
        console.error('Failed to load 2FA status:', error);
      }
    });
  }

  toggleChangePassword(): void {
    this.showChangePassword = !this.showChangePassword;
    if (!this.showChangePassword) {
      this.changePasswordForm.reset();
    }
  }

  onChangePassword(): void {
    if (this.changePasswordForm.invalid) {
      this.markFormGroupTouched(this.changePasswordForm);
      return;
    }

    this.loading = true;
    const formValue = this.changePasswordForm.value;

    this.authService.changePassword(
      formValue.currentPassword,
      formValue.newPassword,
      formValue.twoFactorToken || undefined
    ).subscribe({
      next: (response) => {
        this.snackBar.open('Password changed successfully!', 'Close', { duration: 3000 });
        this.changePasswordForm.reset();
        this.showChangePassword = false;
        this.loading = false;
      },
      error: (error) => {
        this.snackBar.open(error.message || 'Failed to change password', 'Close', { duration: 5000 });
        this.loading = false;
      }
    });
  }

  isOAuthUser(): boolean {
    return this.oauthService.isOAuthUser(this.currentUser);
  }

  getOAuthProviderName(): string {
    return this.oauthService.getOAuthProviderName(this.currentUser);
  }

  getOAuthProviderIcon(): string {
    return this.oauthService.getOAuthProviderIcon(this.currentUser);
  }

  getOAuthProviderColor(): string {
    return this.oauthService.getOAuthProviderColor(this.currentUser);
  }

  getFieldError(fieldName: string): string {
    const field = this.changePasswordForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      if (field.errors['passwordMismatch']) return 'Passwords do not match';
    }
    return '';
  }

  private passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword');
    const confirmPassword = form.get('confirmPassword');

    if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    } else {
      confirmPassword?.setErrors(null);
    }

    return null;
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }
}
