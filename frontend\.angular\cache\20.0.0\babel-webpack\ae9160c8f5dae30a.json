{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function debounce(durationSelector) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue = null;\n    let durationSubscriber = null;\n    const emit = () => {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      durationSubscriber = null;\n      if (hasValue) {\n        hasValue = false;\n        const value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      durationSubscriber === null || durationSubscriber === void 0 ? void 0 : durationSubscriber.unsubscribe();\n      hasValue = true;\n      lastValue = value;\n      durationSubscriber = createOperatorSubscriber(subscriber, emit, noop);\n      innerFrom(durationSelector(value)).subscribe(durationSubscriber);\n    }, () => {\n      emit();\n      subscriber.complete();\n    }, undefined, () => {\n      lastValue = durationSubscriber = null;\n    }));\n  });\n}\n//# sourceMappingURL=debounce.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}