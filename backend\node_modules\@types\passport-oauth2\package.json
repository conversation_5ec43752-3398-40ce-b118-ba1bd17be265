{"name": "@types/passport-oauth2", "version": "1.4.17", "description": "TypeScript definitions for passport-oauth2", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/passport-oauth2", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "pasi<PERSON><PERSON>", "url": "https://github.com/pasieronen"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "githubUsername": "EduardoAC", "url": "https://github.com/EduardoAC"}, {"name": "<PERSON>", "githubUsername": "ivan94", "url": "https://github.com/ivan94"}, {"name": "<PERSON>", "githubUsername": "da<PERSON><PERSON><PERSON>", "url": "https://github.com/daphnesmit"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/passport-oauth2"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/oauth": "*", "@types/passport": "*"}, "typesPublisherContentHash": "94b283b17bf39ac046713ffc9abb86534d3f10dc97b4ba3a8a489a9f554534e4", "typeScriptVersion": "4.7"}