{"ast": null, "code": "const MAC_ENTER = 3;\nconst BACKSPACE = 8;\nconst TAB = 9;\nconst NUM_CENTER = 12;\nconst ENTER = 13;\nconst SHIFT = 16;\nconst CONTROL = 17;\nconst ALT = 18;\nconst PAUSE = 19;\nconst CAPS_LOCK = 20;\nconst ESCAPE = 27;\nconst SPACE = 32;\nconst PAGE_UP = 33;\nconst PAGE_DOWN = 34;\nconst END = 35;\nconst HOME = 36;\nconst LEFT_ARROW = 37;\nconst UP_ARROW = 38;\nconst RIGHT_ARROW = 39;\nconst DOWN_ARROW = 40;\nconst PLUS_SIGN = 43;\nconst PRINT_SCREEN = 44;\nconst INSERT = 45;\nconst DELETE = 46;\nconst ZERO = 48;\nconst ONE = 49;\nconst TWO = 50;\nconst THREE = 51;\nconst FOUR = 52;\nconst FIVE = 53;\nconst SIX = 54;\nconst SEVEN = 55;\nconst EIGHT = 56;\nconst NINE = 57;\nconst FF_SEMICOLON = 59; // Firefox (Gecko) fires this for semicolon instead of 186\nconst FF_EQUALS = 61; // Firefox (Gecko) fires this for equals instead of 187\nconst QUESTION_MARK = 63;\nconst AT_SIGN = 64;\nconst A = 65;\nconst B = 66;\nconst C = 67;\nconst D = 68;\nconst E = 69;\nconst F = 70;\nconst G = 71;\nconst H = 72;\nconst I = 73;\nconst J = 74;\nconst K = 75;\nconst L = 76;\nconst M = 77;\nconst N = 78;\nconst O = 79;\nconst P = 80;\nconst Q = 81;\nconst R = 82;\nconst S = 83;\nconst T = 84;\nconst U = 85;\nconst V = 86;\nconst W = 87;\nconst X = 88;\nconst Y = 89;\nconst Z = 90;\nconst META = 91; // WIN_KEY_LEFT\nconst MAC_WK_CMD_LEFT = 91;\nconst MAC_WK_CMD_RIGHT = 93;\nconst CONTEXT_MENU = 93;\nconst NUMPAD_ZERO = 96;\nconst NUMPAD_ONE = 97;\nconst NUMPAD_TWO = 98;\nconst NUMPAD_THREE = 99;\nconst NUMPAD_FOUR = 100;\nconst NUMPAD_FIVE = 101;\nconst NUMPAD_SIX = 102;\nconst NUMPAD_SEVEN = 103;\nconst NUMPAD_EIGHT = 104;\nconst NUMPAD_NINE = 105;\nconst NUMPAD_MULTIPLY = 106;\nconst NUMPAD_PLUS = 107;\nconst NUMPAD_MINUS = 109;\nconst NUMPAD_PERIOD = 110;\nconst NUMPAD_DIVIDE = 111;\nconst F1 = 112;\nconst F2 = 113;\nconst F3 = 114;\nconst F4 = 115;\nconst F5 = 116;\nconst F6 = 117;\nconst F7 = 118;\nconst F8 = 119;\nconst F9 = 120;\nconst F10 = 121;\nconst F11 = 122;\nconst F12 = 123;\nconst NUM_LOCK = 144;\nconst SCROLL_LOCK = 145;\nconst FIRST_MEDIA = 166;\nconst FF_MINUS = 173;\nconst MUTE = 173; // Firefox (Gecko) fires 181 for MUTE\nconst VOLUME_DOWN = 174; // Firefox (Gecko) fires 182 for VOLUME_DOWN\nconst VOLUME_UP = 175; // Firefox (Gecko) fires 183 for VOLUME_UP\nconst FF_MUTE = 181;\nconst FF_VOLUME_DOWN = 182;\nconst LAST_MEDIA = 183;\nconst FF_VOLUME_UP = 183;\nconst SEMICOLON = 186; // Firefox (Gecko) fires 59 for SEMICOLON\nconst EQUALS = 187; // Firefox (Gecko) fires 61 for EQUALS\nconst COMMA = 188;\nconst DASH = 189; // Firefox (Gecko) fires 173 for DASH/MINUS\nconst PERIOD = 190;\nconst SLASH = 191;\nconst APOSTROPHE = 192;\nconst TILDE = 192;\nconst OPEN_SQUARE_BRACKET = 219;\nconst BACKSLASH = 220;\nconst CLOSE_SQUARE_BRACKET = 221;\nconst SINGLE_QUOTE = 222;\nconst MAC_META = 224;\nexport { FF_EQUALS as $, A, BACKSPACE as B, CONTROL as C, DOWN_ARROW as D, END as E, F1 as F, PRINT_SCREEN as G, HOME as H, INSERT as I, TWO as J, THREE as K, LEFT_ARROW as L, MAC_META as M, NINE as N, ONE as O, PAGE_DOWN as P, FOUR as Q, RIGHT_ARROW as R, SPACE as S, TAB as T, UP_ARROW as U, FIVE as V, SIX as W, SEVEN as X, EIGHT as Y, Z, FF_SEMICOLON as _, PAGE_UP as a, CLOSE_SQUARE_BRACKET as a$, QUESTION_MARK as a0, AT_SIGN as a1, B as a2, C as a3, D as a4, E as a5, F as a6, G as a7, H as a8, I as a9, NUMPAD_SEVEN as aA, NUMPAD_EIGHT as aB, NUMPAD_NINE as aC, NUMPAD_MULTIPLY as aD, NUMPAD_PLUS as aE, NUMPAD_MINUS as aF, NUMPAD_PERIOD as aG, NUMPAD_DIVIDE as aH, NUM_LOCK as aI, SCROLL_LOCK as aJ, FIRST_MEDIA as aK, FF_MINUS as aL, MUTE as aM, VOLUME_DOWN as aN, VOLUME_UP as aO, FF_MUTE as aP, FF_VOLUME_DOWN as aQ, LAST_MEDIA as aR, FF_VOLUME_UP as aS, SEMICOLON as aT, EQUALS as aU, DASH as aV, SLASH as aW, APOSTROPHE as aX, TILDE as aY, OPEN_SQUARE_BRACKET as aZ, BACKSLASH as a_, J as aa, K as ab, L as ac, M as ad, N as ae, O as af, P as ag, Q as ah, R as ai, S as aj, T as ak, U as al, V as am, W as an, X as ao, Y as ap, MAC_WK_CMD_LEFT as aq, MAC_WK_CMD_RIGHT as ar, CONTEXT_MENU as as, NUMPAD_ZERO as at, NUMPAD_ONE as au, NUMPAD_TWO as av, NUMPAD_THREE as aw, NUMPAD_FOUR as ax, NUMPAD_FIVE as ay, NUMPAD_SIX as az, ZERO as b, SINGLE_QUOTE as b0, ENTER as c, ALT as d, META as e, SHIFT as f, ESCAPE as g, PERIOD as h, DELETE as i, F2 as j, F3 as k, F4 as l, F5 as m, F6 as n, F7 as o, F8 as p, F9 as q, F10 as r, F11 as s, F12 as t, COMMA as u, MAC_ENTER as v, NUM_CENTER as w, PAUSE as x, CAPS_LOCK as y, PLUS_SIGN as z };\n//# sourceMappingURL=keycodes-CpHkExLC.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}