import { TokenService } from '@loopback/authentication';
import { UserProfile } from '@loopback/security';
import { User as UserModel } from '../models';
import { UserRepository as UserRepo } from '../repositories';
import { SecurityService, EmailService, SmsService, OAuthService, JwtService } from '../services';
export declare class AuthController {
    jwtService: TokenService;
    user: UserProfile;
    protected userRepository: UserRepo;
    securityService?: SecurityService | undefined;
    emailService?: EmailService | undefined;
    smsService?: SmsService | undefined;
    oauthService?: OAuthService | undefined;
    customJwtService?: JwtService | undefined;
    constructor(jwtService: TokenService, user: UserProfile, userRepository: UserRepo, securityService?: SecurityService | undefined, emailService?: EmailService | undefined, smsService?: SmsService | undefined, oauthService?: OAuthService | undefined, customJwtService?: JwtService | undefined);
    signUp(newUserRequest: Omit<UserModel, 'id'> & {
        password: string;
    }): Promise<{
        message: string;
        userId: string;
    }>;
    login(credentials: {
        email: string;
        password: string;
        twoFactorToken?: string;
    }): Promise<{
        token: string;
        user: UserModel;
        requiresTwoFactor?: boolean;
    }>;
    verifyEmail(request: {
        token: string;
    }): Promise<{
        message: string;
    }>;
    forgotPassword(request: {
        email: string;
    }): Promise<{
        message: string;
    }>;
    resetPassword(request: {
        token: string;
        password: string;
    }): Promise<{
        message: string;
    }>;
    changePassword(request: {
        currentPassword: string;
        newPassword: string;
        twoFactorToken?: string;
    }): Promise<{
        message: string;
    }>;
    getOAuthUrl(provider: string): Promise<{
        url: string;
    }>;
    oauthCallback(provider: string, request: {
        code: string;
        state?: string;
    }): Promise<{
        token: string;
        user: UserModel;
    }>;
    private verifyCredentials;
    private convertToUserProfile;
    private handleFailedLogin;
    private handleSuccessfulLogin;
}
