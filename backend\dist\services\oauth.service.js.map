{"version": 3, "file": "oauth.service.js", "sourceRoot": "", "sources": ["../../src/services/oauth.service.ts"], "names": [], "mappings": ";;;;AAAA,yCAAwD;AACxD,qDAAgD;AAChD,yCAA0C;AAC1C,6DAAiD;AACjD,0DAA0B;AAC1B,kDAA+C;AAIxC,IAAM,YAAY,GAAlB,MAAM,YAAY;IAGvB,YACwC,cAA8B;QAA9B,mBAAc,GAAd,cAAc,CAAgB;QAEpE,IAAI,CAAC,YAAY,GAAG,IAAI,kCAAY,CAClC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAC5B,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAChC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAC/B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACnC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;gBACnD,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB;aACvC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YACpC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;YAC1D,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,OAAO,CAAC,GAAG;gBACf,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,SAAS,EAAE,OAAO,CAAC,UAAU;gBAC7B,QAAQ,EAAE,OAAO,CAAC,WAAW;gBAC7B,SAAS,EAAE,OAAO,CAAC,OAAO;gBAC1B,aAAa,EAAE,OAAO,CAAC,cAAc;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,WAAmB;QACzC,IAAI,CAAC;YACH,4BAA4B;YAC5B,MAAM,YAAY,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,6BAA6B,EAAE;gBAClE,OAAO,EAAE;oBACP,eAAe,EAAE,SAAS,WAAW,EAAE;oBACvC,YAAY,EAAE,WAAW;iBAC1B;aACF,CAAC,CAAC;YAEH,oCAAoC;YACpC,MAAM,aAAa,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,oCAAoC,EAAE;gBAC1E,OAAO,EAAE;oBACP,eAAe,EAAE,SAAS,WAAW,EAAE;oBACvC,YAAY,EAAE,WAAW;iBAC1B;aACF,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAE5E,OAAO;gBACL,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACnC,KAAK,EAAE,YAAY,EAAE,KAAK,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK;gBACrD,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK;gBAC3E,QAAQ,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;gBACrE,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,UAAU;gBACvC,aAAa,EAAE,YAAY,EAAE,QAAQ,IAAI,KAAK;aAC/C,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,WAAmB;QAC5C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,qCAAqC,EAAE;gBACtE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,WAAW,EAAE;iBACzC;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACpB,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,iBAAiB;gBAC5D,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,SAAS;gBAClC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAC/B,SAAS,EAAE,IAAI,EAAE,mDAAmD;gBACpE,aAAa,EAAE,IAAI,CAAC,4CAA4C;aACjE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,yBAAyB,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,QAAgB,EAAE,SAAc;QAC1D,IAAI,IAAI,GAAgB,IAAI,CAAC;QAE7B,sCAAsC;QACtC,MAAM,YAAY,GAAG,GAAG,QAAQ,IAAI,CAAC;QACrC,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACvC,KAAK,EAAE,EAAC,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC,EAAE,EAAC;SACtC,CAAC,CAAC;QAEH,IAAI,IAAI,EAAE,CAAC;YACT,4BAA4B;YAC5B,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE;gBAC5C,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,SAAS,EAAE,SAAS,CAAC,SAAS;gBAC9B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAED,iDAAiD;QACjD,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBACvC,KAAK,EAAE,EAAC,KAAK,EAAE,SAAS,CAAC,KAAK,EAAC;aAChC,CAAC,CAAC;YAEH,IAAI,IAAI,EAAE,CAAC;gBACT,sCAAsC;gBACtC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE;oBAC5C,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC,EAAE;oBAC5B,aAAa,EAAE,QAAQ;oBACvB,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS;oBAChD,aAAa,EAAE,SAAS,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;oBAC5D,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YAC/C,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC,EAAE;YAC5B,aAAa,EAAE,QAAQ;YACvB,SAAS,EAAE,SAAS,CAAC,SAAS;YAC9B,aAAa,EAAE,SAAS,CAAC,aAAa,IAAI,IAAI;YAC9C,KAAK,EAAE,CAAC,MAAM,CAAC;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,gBAAgB,CAAC,QAAgB,EAAE,KAAc;QAC/C,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,2CAA2C,CAAC;QAE9F,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ;gBACX,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;oBAClD,WAAW,EAAE,SAAS;oBACtB,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;oBAC3B,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ;iBAC5C,CAAC,CAAC;gBACH,OAAO,SAAS,CAAC;YAEnB,KAAK,QAAQ;gBACX,MAAM,SAAS,GAAG,2CAA2C;oBAC3D,aAAa,OAAO,CAAC,GAAG,CAAC,gBAAgB,GAAG;oBAC5C,gBAAgB,kBAAkB,CAAC,OAAO,CAAC,GAAG;oBAC9C,mBAAmB;oBACnB,SAAS,KAAK,CAAC,CAAC,CAAC,UAAU,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAClD,OAAO,SAAS,CAAC;YAEnB,KAAK,WAAW;gBACd,MAAM,YAAY,GAAG,iEAAiE;oBACpF,aAAa,OAAO,CAAC,GAAG,CAAC,mBAAmB,GAAG;oBAC/C,qBAAqB;oBACrB,gBAAgB,kBAAkB,CAAC,OAAO,CAAC,GAAG;oBAC9C,kBAAkB;oBAClB,SAAS,KAAK,CAAC,CAAC,CAAC,aAAa,KAAK,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;gBACxD,OAAO,YAAY,CAAC;YAEtB;gBACE,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,QAAgB,EAAE,IAAY;QACvD,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ;gBACX,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACxD,OAAO,MAAM,CAAC,YAAa,CAAC;YAE9B,KAAK,QAAQ;gBACX,MAAM,cAAc,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,6CAA6C,EAAE;oBACrF,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB;oBACvC,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB;oBAC/C,IAAI;iBACL,EAAE;oBACD,OAAO,EAAE;wBACP,QAAQ,EAAE,kBAAkB;qBAC7B;iBACF,CAAC,CAAC;gBACH,OAAO,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC;YAE1C,KAAK,WAAW;gBACd,MAAM,iBAAiB,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,4DAA4D,EAAE;oBACvG,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB;oBAC1C,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB;oBAClD,IAAI;oBACJ,UAAU,EAAE,oBAAoB;oBAChC,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB;iBAC7C,EAAE;oBACD,OAAO,EAAE;wBACP,cAAc,EAAE,mCAAmC;qBACpD;iBACF,CAAC,CAAC;gBACH,OAAO,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC;YAE7C;gBACE,MAAM,IAAI,iBAAU,CAAC,UAAU,CAAC,4BAA4B,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;CACF,CAAA;AA7NY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,iBAAU,EAAC,EAAC,KAAK,EAAE,mBAAY,CAAC,SAAS,EAAC,CAAC;IAKvC,mBAAA,IAAA,uBAAU,EAAC,6BAAc,CAAC,CAAA;6CAA2B,6BAAc;GAJ3D,YAAY,CA6NxB"}