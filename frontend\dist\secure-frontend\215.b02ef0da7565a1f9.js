"use strict";(self.webpackChunksecure_frontend=self.webpackChunksecure_frontend||[]).push([[215],{215:(ke,F,s)=>{s.r(F),s.d(F,{AuthModule:()=>ue});var u=s(6396),a=s(9417),p=s(1736),I=s(5596),O=s(340),g=s(6863),f=s(8834),_=s(1074),R=s(8511),e=s(7241),r=s(8564),l=s(7705),M=s(1560);const L=["mat-internal-form-field",""],S=["*"];let G=(()=>{class c{labelPosition;static \u0275fac=function(n){return new(n||c)};static \u0275cmp=e.VBU({type:c,selectors:[["div","mat-internal-form-field",""]],hostAttrs:[1,"mdc-form-field","mat-internal-form-field"],hostVars:2,hostBindings:function(n,o){2&n&&e.AVh("mdc-form-field--align-end","before"===o.labelPosition)},inputs:{labelPosition:"labelPosition"},attrs:L,ngContentSelectors:S,decls:1,vars:0,template:function(n,o){1&n&&(e.NAR(),e.SdG(0))},styles:[".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}\n"],encapsulation:2,changeDetection:0})}return c})();var $=s(6609),B=s(7218),A=s(2655),C=s(1715);const z=["input"],D=["label"],N=["*"],V=new r.nKC("mat-checkbox-default-options",{providedIn:"root",factory:w});function w(){return{color:"accent",clickAction:"check-indeterminate",disabledInteractive:!1}}var m=function(c){return c[c.Init=0]="Init",c[c.Checked=1]="Checked",c[c.Unchecked=2]="Unchecked",c[c.Indeterminate=3]="Indeterminate",c}(m||{});class X{source;checked}const P=w();let E=(()=>{class c{_elementRef=(0,r.WQX)(e.aKT);_changeDetectorRef=(0,r.WQX)(l.gRc);_ngZone=(0,r.WQX)(e.SKi);_animationsDisabled=(0,$._)();_options=(0,r.WQX)(V,{optional:!0});focus(){this._inputElement.nativeElement.focus()}_createChangeEvent(t){const n=new X;return n.source=this,n.checked=t,n}_getAnimationTargetElement(){return this._inputElement?.nativeElement}_animationClasses={uncheckedToChecked:"mdc-checkbox--anim-unchecked-checked",uncheckedToIndeterminate:"mdc-checkbox--anim-unchecked-indeterminate",checkedToUnchecked:"mdc-checkbox--anim-checked-unchecked",checkedToIndeterminate:"mdc-checkbox--anim-checked-indeterminate",indeterminateToChecked:"mdc-checkbox--anim-indeterminate-checked",indeterminateToUnchecked:"mdc-checkbox--anim-indeterminate-unchecked"};ariaLabel="";ariaLabelledby=null;ariaDescribedby;ariaExpanded;ariaControls;ariaOwns;_uniqueId;id;get inputId(){return`${this.id||this._uniqueId}-input`}required;labelPosition="after";name=null;change=new e.bkB;indeterminateChange=new e.bkB;value;disableRipple;_inputElement;_labelElement;tabIndex;color;disabledInteractive;_onTouched=()=>{};_currentAnimationClass="";_currentCheckState=m.Init;_controlValueAccessorChangeFn=()=>{};_validatorChangeFn=()=>{};constructor(){(0,r.WQX)(M._).load(B._);const t=(0,r.WQX)(new l.ES_("tabindex"),{optional:!0});this._options=this._options||P,this.color=this._options.color||P.color,this.tabIndex=null==t?0:parseInt(t)||0,this.id=this._uniqueId=(0,r.WQX)(R._).getId("mat-mdc-checkbox-"),this.disabledInteractive=this._options?.disabledInteractive??!1}ngOnChanges(t){t.required&&this._validatorChangeFn()}ngAfterViewInit(){this._syncIndeterminate(this.indeterminate)}get checked(){return this._checked}set checked(t){t!=this.checked&&(this._checked=t,this._changeDetectorRef.markForCheck())}_checked=!1;get disabled(){return this._disabled}set disabled(t){t!==this.disabled&&(this._disabled=t,this._changeDetectorRef.markForCheck())}_disabled=!1;get indeterminate(){return this._indeterminate()}set indeterminate(t){const n=t!=this._indeterminate();this._indeterminate.set(t),n&&(this._transitionCheckState(t?m.Indeterminate:this.checked?m.Checked:m.Unchecked),this.indeterminateChange.emit(t)),this._syncIndeterminate(t)}_indeterminate=(0,r.vPA)(!1);_isRippleDisabled(){return this.disableRipple||this.disabled}_onLabelTextChange(){this._changeDetectorRef.detectChanges()}writeValue(t){this.checked=!!t}registerOnChange(t){this._controlValueAccessorChangeFn=t}registerOnTouched(t){this._onTouched=t}setDisabledState(t){this.disabled=t}validate(t){return this.required&&!0!==t.value?{required:!0}:null}registerOnValidatorChange(t){this._validatorChangeFn=t}_transitionCheckState(t){let n=this._currentCheckState,o=this._getAnimationTargetElement();if(n!==t&&o&&(this._currentAnimationClass&&o.classList.remove(this._currentAnimationClass),this._currentAnimationClass=this._getAnimationClassForCheckStateTransition(n,t),this._currentCheckState=t,this._currentAnimationClass.length>0)){o.classList.add(this._currentAnimationClass);const i=this._currentAnimationClass;this._ngZone.runOutsideAngular(()=>{setTimeout(()=>{o.classList.remove(i)},1e3)})}}_emitChangeEvent(){this._controlValueAccessorChangeFn(this.checked),this.change.emit(this._createChangeEvent(this.checked)),this._inputElement&&(this._inputElement.nativeElement.checked=this.checked)}toggle(){this.checked=!this.checked,this._controlValueAccessorChangeFn(this.checked)}_handleInputClick(){const t=this._options?.clickAction;this.disabled||"noop"===t?(this.disabled&&this.disabledInteractive||!this.disabled&&"noop"===t)&&(this._inputElement.nativeElement.checked=this.checked,this._inputElement.nativeElement.indeterminate=this.indeterminate):(this.indeterminate&&"check"!==t&&Promise.resolve().then(()=>{this._indeterminate.set(!1),this.indeterminateChange.emit(!1)}),this._checked=!this._checked,this._transitionCheckState(this._checked?m.Checked:m.Unchecked),this._emitChangeEvent())}_onInteractionEvent(t){t.stopPropagation()}_onBlur(){Promise.resolve().then(()=>{this._onTouched(),this._changeDetectorRef.markForCheck()})}_getAnimationClassForCheckStateTransition(t,n){if(this._animationsDisabled)return"";switch(t){case m.Init:if(n===m.Checked)return this._animationClasses.uncheckedToChecked;if(n==m.Indeterminate)return this._checked?this._animationClasses.checkedToIndeterminate:this._animationClasses.uncheckedToIndeterminate;break;case m.Unchecked:return n===m.Checked?this._animationClasses.uncheckedToChecked:this._animationClasses.uncheckedToIndeterminate;case m.Checked:return n===m.Unchecked?this._animationClasses.checkedToUnchecked:this._animationClasses.checkedToIndeterminate;case m.Indeterminate:return n===m.Checked?this._animationClasses.indeterminateToChecked:this._animationClasses.indeterminateToUnchecked}return""}_syncIndeterminate(t){const n=this._inputElement;n&&(n.nativeElement.indeterminate=t)}_onInputClick(){this._handleInputClick()}_onTouchTargetClick(){this._handleInputClick(),this.disabled||this._inputElement.nativeElement.focus()}_preventBubblingFromLabel(t){t.target&&this._labelElement.nativeElement.contains(t.target)&&t.stopPropagation()}static \u0275fac=function(n){return new(n||c)};static \u0275cmp=e.VBU({type:c,selectors:[["mat-checkbox"]],viewQuery:function(n,o){if(1&n&&(e.GBs(z,5),e.GBs(D,5)),2&n){let i;e.mGM(i=e.lsd())&&(o._inputElement=i.first),e.mGM(i=e.lsd())&&(o._labelElement=i.first)}},hostAttrs:[1,"mat-mdc-checkbox"],hostVars:16,hostBindings:function(n,o){2&n&&(e.Avn("id",o.id),e.BMQ("tabindex",null)("aria-label",null)("aria-labelledby",null),e.HbH(o.color?"mat-"+o.color:"mat-accent"),e.AVh("_mat-animation-noopable",o._animationsDisabled)("mdc-checkbox--disabled",o.disabled)("mat-mdc-checkbox-disabled",o.disabled)("mat-mdc-checkbox-checked",o.checked)("mat-mdc-checkbox-disabled-interactive",o.disabledInteractive))},inputs:{ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],ariaExpanded:[2,"aria-expanded","ariaExpanded",l.L39],ariaControls:[0,"aria-controls","ariaControls"],ariaOwns:[0,"aria-owns","ariaOwns"],id:"id",required:[2,"required","required",l.L39],labelPosition:"labelPosition",name:"name",value:"value",disableRipple:[2,"disableRipple","disableRipple",l.L39],tabIndex:[2,"tabIndex","tabIndex",t=>null==t?void 0:(0,l.Udg)(t)],color:"color",disabledInteractive:[2,"disabledInteractive","disabledInteractive",l.L39],checked:[2,"checked","checked",l.L39],disabled:[2,"disabled","disabled",l.L39],indeterminate:[2,"indeterminate","indeterminate",l.L39]},outputs:{change:"change",indeterminateChange:"indeterminateChange"},exportAs:["matCheckbox"],features:[e.Jv_([{provide:a.kq,useExisting:(0,r.Rfq)(()=>c),multi:!0},{provide:a.cz,useExisting:c,multi:!0}]),e.OA$],ngContentSelectors:N,decls:15,vars:23,consts:[["checkbox",""],["input",""],["label",""],["mat-internal-form-field","",3,"click","labelPosition"],[1,"mdc-checkbox"],[1,"mat-mdc-checkbox-touch-target",3,"click"],["type","checkbox",1,"mdc-checkbox__native-control",3,"blur","click","change","checked","indeterminate","disabled","id","required","tabIndex"],[1,"mdc-checkbox__ripple"],[1,"mdc-checkbox__background"],["focusable","false","viewBox","0 0 24 24","aria-hidden","true",1,"mdc-checkbox__checkmark"],["fill","none","d","M1.73,12.91 8.1,19.28 22.79,4.59",1,"mdc-checkbox__checkmark-path"],[1,"mdc-checkbox__mixedmark"],["mat-ripple","",1,"mat-mdc-checkbox-ripple","mat-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mdc-label",3,"for"]],template:function(n,o){if(1&n){const i=e.RV6();e.NAR(),e.j41(0,"div",3),e.bIt("click",function(k){return r.eBV(i),r.Njj(o._preventBubblingFromLabel(k))}),e.j41(1,"div",4,0)(3,"div",5),e.bIt("click",function(){return r.eBV(i),r.Njj(o._onTouchTargetClick())}),e.k0s(),e.j41(4,"input",6,1),e.bIt("blur",function(){return r.eBV(i),r.Njj(o._onBlur())})("click",function(){return r.eBV(i),r.Njj(o._onInputClick())})("change",function(k){return r.eBV(i),r.Njj(o._onInteractionEvent(k))}),e.k0s(),e.nrm(6,"div",7),e.j41(7,"div",8),r.qSk(),e.j41(8,"svg",9),e.nrm(9,"path",10),e.k0s(),r.joV(),e.nrm(10,"div",11),e.k0s(),e.nrm(11,"div",12),e.k0s(),e.j41(12,"label",13,2),e.SdG(14),e.k0s()()}if(2&n){const i=e.sdS(2);e.Y8G("labelPosition",o.labelPosition),e.R7$(4),e.AVh("mdc-checkbox--selected",o.checked),e.Y8G("checked",o.checked)("indeterminate",o.indeterminate)("disabled",o.disabled&&!o.disabledInteractive)("id",o.inputId)("required",o.required)("tabIndex",o.disabled&&!o.disabledInteractive?-1:o.tabIndex),e.BMQ("aria-label",o.ariaLabel||null)("aria-labelledby",o.ariaLabelledby)("aria-describedby",o.ariaDescribedby)("aria-checked",o.indeterminate?"mixed":null)("aria-controls",o.ariaControls)("aria-disabled",!(!o.disabled||!o.disabledInteractive)||null)("aria-expanded",o.ariaExpanded)("aria-owns",o.ariaOwns)("name",o.name)("value",o.value),e.R7$(7),e.Y8G("matRippleTrigger",i)("matRippleDisabled",o.disableRipple||o.disabled)("matRippleCentered",!0),e.R7$(),e.Y8G("for",o.inputId)}},dependencies:[A.M,G],styles:['.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom;padding:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);margin:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox:hover>.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:hover>.mat-mdc-checkbox-ripple>.mat-ripple-element{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-focus-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-unselected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-unselected-pressed-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity));background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-hover-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity));background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-focus-state-layer-color, var(--mat-sys-primary))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked+.mdc-checkbox__ripple{opacity:var(--mat-checkbox-selected-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity));background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox:active>.mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mat-checkbox-selected-pressed-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control+.mdc-checkbox__ripple{background-color:var(--mat-checkbox-unselected-hover-state-layer-color, var(--mat-sys-on-surface))}.mdc-checkbox .mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit;z-index:1;width:var(--mat-checkbox-state-layer-size, 40px);height:var(--mat-checkbox-state-layer-size, 40px);top:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);right:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - var(--mat-checkbox-state-layer-size, 40px))/2)}.mdc-checkbox--disabled{cursor:default;pointer-events:none}@media(forced-colors: active){.mdc-checkbox--disabled{opacity:.5}}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1);-webkit-print-color-adjust:exact;color-adjust:exact;border-color:var(--mat-checkbox-unselected-icon-color, var(--mat-sys-on-surface-variant));top:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2);left:calc((var(--mat-checkbox-state-layer-size, 40px) - 18px)/2)}.mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox__native-control:disabled:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:disabled:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:checked)~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-hover-icon-color, var(--mat-sys-on-surface));background-color:rgba(0,0,0,0)}.mdc-checkbox:hover>.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox:hover>.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-hover-icon-color, var(--mat-sys-primary))}.mdc-checkbox__native-control:focus:focus:not(:checked)~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mat-checkbox-unselected-focus-icon-color, var(--mat-sys-on-surface))}.mdc-checkbox__native-control:focus:focus:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:focus:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary));background-color:var(--mat-checkbox-selected-focus-icon-color, var(--mat-sys-primary))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox:hover>.mdc-checkbox__native-control~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__background{border-color:var(--mat-checkbox-disabled-unselected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{background-color:var(--mat-checkbox-disabled-selected-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent));border-color:rgba(0,0,0,0)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms cubic-bezier(0.4, 0, 0.6, 1);color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}@media(forced-colors: active){.mdc-checkbox--disabled .mdc-checkbox__checkmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__checkmark{color:CanvasText}}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);border-color:var(--mat-checkbox-selected-checkmark-color, var(--mat-sys-on-primary))}@media(forced-colors: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled .mdc-checkbox__mixedmark,.mdc-checkbox--disabled.mat-mdc-checkbox-disabled-interactive .mdc-checkbox__mixedmark{border-color:var(--mat-checkbox-disabled-selected-checkmark-color, var(--mat-sys-surface))}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__checkmark{transition:opacity 180ms cubic-bezier(0, 0, 0.2, 1),transform 180ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background>.mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-touch-target,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__native-control,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__ripple,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mat-mdc-checkbox-ripple::before,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__checkmark>.mdc-checkbox__checkmark-path,.mat-mdc-checkbox._mat-animation-noopable>.mat-internal-form-field>.mdc-checkbox>.mdc-checkbox__background>.mdc-checkbox__mixedmark{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox .mat-internal-form-field{color:var(--mat-checkbox-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-checkbox-label-text-font, var(--mat-sys-body-medium-font));line-height:var(--mat-checkbox-label-text-line-height, var(--mat-sys-body-medium-line-height));font-size:var(--mat-checkbox-label-text-size, var(--mat-sys-body-medium-size));letter-spacing:var(--mat-checkbox-label-text-tracking, var(--mat-sys-body-medium-tracking));font-weight:var(--mat-checkbox-label-text-weight, var(--mat-sys-body-medium-weight))}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive{pointer-events:auto}.mat-mdc-checkbox.mat-mdc-checkbox-disabled.mat-mdc-checkbox-disabled-interactive input{cursor:default}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-checkbox label:empty{display:none}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox .mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox .mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display, block)}.mat-mdc-checkbox .mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-focus-indicator::before{content:""}\n'],encapsulation:2,changeDetection:0})}return c})(),Y=(()=>{class c{static \u0275fac=function(n){return new(n||c)};static \u0275mod=e.$C({type:c});static \u0275inj=r.G2t({imports:[E,C.M,C.M]})}return c})();var x=s(9183),v=s(8822),U=s(1997),T=s(4796),q=s(7097),y=s(7715),h=s(3573),j=s(4179);function J(c,d){1&c&&e.nrm(0,"mat-spinner",25)}function W(c,d){1&c&&(e.j41(0,"span"),e.EFF(1,"Sign In"),e.k0s())}function H(c,d){if(1&c){const t=e.RV6();e.j41(0,"button",26),e.bIt("click",function(){const o=r.eBV(t).$implicit,i=e.XpG(2);return r.Njj(i.loginWithOAuth(o.name))}),e.nrm(1,"i"),e.j41(2,"span"),e.EFF(3),e.k0s()()}if(2&c){const t=d.$implicit,n=e.XpG(2);e.xc7("border-color",t.color),e.Y8G("disabled",n.loading),e.R7$(),e.HbH(t.icon),e.xc7("color",t.color),e.R7$(2),e.JRh(t.displayName)}}function Q(c,d){if(1&c){const t=e.RV6();e.j41(0,"form",7),e.bIt("ngSubmit",function(){r.eBV(t);const o=e.XpG();return r.Njj(o.onSubmit())}),e.j41(1,"mat-form-field",8)(2,"mat-label"),e.EFF(3,"Email Address"),e.k0s(),e.nrm(4,"input",9),e.j41(5,"mat-icon",10),e.EFF(6,"email"),e.k0s(),e.j41(7,"mat-error"),e.EFF(8),e.k0s()(),e.j41(9,"mat-form-field",8)(10,"mat-label"),e.EFF(11,"Password"),e.k0s(),e.nrm(12,"input",11),e.j41(13,"button",12),e.bIt("click",function(){r.eBV(t);const o=e.XpG();return r.Njj(o.hidePassword=!o.hidePassword)}),e.j41(14,"mat-icon"),e.EFF(15),e.k0s()(),e.j41(16,"mat-error"),e.EFF(17),e.k0s()(),e.j41(18,"button",13),e.DNE(19,J,1,0,"mat-spinner",14)(20,W,2,0,"span",15),e.k0s(),e.j41(21,"div",16)(22,"span"),e.EFF(23,"or"),e.k0s()(),e.j41(24,"button",17),e.bIt("click",function(){r.eBV(t);const o=e.XpG();return r.Njj(o.toggleOTPLogin())}),e.j41(25,"mat-icon"),e.EFF(26,"sms"),e.k0s(),e.EFF(27," Login with OTP "),e.k0s(),e.j41(28,"div",18)(29,"div",16)(30,"span"),e.EFF(31,"or continue with"),e.k0s()(),e.j41(32,"div",19),e.DNE(33,H,4,8,"button",20),e.k0s()(),e.j41(34,"div",21)(35,"a",22),e.EFF(36,"Forgot Password?"),e.k0s()(),e.j41(37,"div",23)(38,"span"),e.EFF(39,"Don't have an account? "),e.k0s(),e.j41(40,"a",24),e.EFF(41,"Sign Up"),e.k0s()()()}if(2&c){const t=e.XpG();e.Y8G("formGroup",t.loginForm),e.R7$(8),e.JRh(t.getFieldError(t.loginForm,"email")),e.R7$(4),e.Y8G("type",t.hidePassword?"password":"text"),e.R7$(3),e.JRh(t.hidePassword?"visibility_off":"visibility"),e.R7$(2),e.JRh(t.getFieldError(t.loginForm,"password")),e.R7$(),e.Y8G("disabled",t.loading),e.R7$(),e.Y8G("ngIf",t.loading),e.R7$(),e.Y8G("ngIf",!t.loading),e.R7$(13),e.Y8G("ngForOf",t.oauthProviders)}}function Z(c,d){1&c&&e.nrm(0,"mat-spinner",25)}function K(c,d){1&c&&(e.j41(0,"span"),e.EFF(1,"Verify & Sign In"),e.k0s())}function ee(c,d){if(1&c){const t=e.RV6();e.j41(0,"form",7),e.bIt("ngSubmit",function(){r.eBV(t);const o=e.XpG();return r.Njj(o.onTwoFactorSubmit())}),e.j41(1,"div",27)(2,"mat-icon",28),e.EFF(3,"security"),e.k0s(),e.j41(4,"h3"),e.EFF(5,"Two-Factor Authentication"),e.k0s(),e.j41(6,"p"),e.EFF(7,"Enter the 6-digit code from your authenticator app"),e.k0s()(),e.j41(8,"mat-form-field",8)(9,"mat-label"),e.EFF(10,"Authentication Code"),e.k0s(),e.nrm(11,"input",29),e.j41(12,"mat-icon",10),e.EFF(13,"verified_user"),e.k0s(),e.j41(14,"mat-error"),e.EFF(15),e.k0s()(),e.j41(16,"button",13),e.DNE(17,Z,1,0,"mat-spinner",14)(18,K,2,0,"span",15),e.k0s(),e.j41(19,"button",30),e.bIt("click",function(){r.eBV(t);const o=e.XpG();return r.Njj(o.backToLogin())}),e.j41(20,"mat-icon"),e.EFF(21,"arrow_back"),e.k0s(),e.EFF(22," Back to Login "),e.k0s()()}if(2&c){const t=e.XpG();e.Y8G("formGroup",t.twoFactorForm),e.R7$(15),e.JRh(t.getFieldError(t.twoFactorForm,"twoFactorToken")),e.R7$(),e.Y8G("disabled",t.loading),e.R7$(),e.Y8G("ngIf",t.loading),e.R7$(),e.Y8G("ngIf",!t.loading)}}function te(c,d){1&c&&e.nrm(0,"mat-spinner",25)}function oe(c,d){1&c&&(e.j41(0,"span"),e.EFF(1,"Send OTP"),e.k0s())}function ne(c,d){if(1&c){const t=e.RV6();e.j41(0,"button",34),e.bIt("click",function(){r.eBV(t);const o=e.XpG(2);return r.Njj(o.sendOTP())}),e.DNE(1,te,1,0,"mat-spinner",14)(2,oe,2,0,"span",15),e.k0s()}if(2&c){const t=e.XpG(2);e.Y8G("disabled",t.loading),e.R7$(),e.Y8G("ngIf",t.loading),e.R7$(),e.Y8G("ngIf",!t.loading)}}function ce(c,d){1&c&&e.nrm(0,"mat-spinner",25)}function re(c,d){1&c&&(e.j41(0,"span"),e.EFF(1,"Verify & Sign In"),e.k0s())}function ae(c,d){if(1&c){const t=e.RV6();e.j41(0,"div")(1,"mat-form-field",8)(2,"mat-label"),e.EFF(3,"Enter OTP"),e.k0s(),e.nrm(4,"input",35),e.j41(5,"mat-icon",10),e.EFF(6,"lock"),e.k0s(),e.j41(7,"mat-error"),e.EFF(8),e.k0s()(),e.j41(9,"button",36),e.bIt("click",function(){r.eBV(t);const o=e.XpG(2);return r.Njj(o.loginWithOTP())}),e.DNE(10,ce,1,0,"mat-spinner",14)(11,re,2,0,"span",15),e.k0s(),e.j41(12,"button",30),e.bIt("click",function(){r.eBV(t);const o=e.XpG(2);return r.Njj(o.sendOTP())}),e.j41(13,"mat-icon"),e.EFF(14,"refresh"),e.k0s(),e.EFF(15," Resend OTP "),e.k0s()()}if(2&c){const t=e.XpG(2);e.R7$(8),e.JRh(t.getFieldError(t.otpForm,"code")),e.R7$(),e.Y8G("disabled",t.loading),e.R7$(),e.Y8G("ngIf",t.loading),e.R7$(),e.Y8G("ngIf",!t.loading)}}function ie(c,d){if(1&c){const t=e.RV6();e.j41(0,"form",31)(1,"div",27)(2,"mat-icon",28),e.EFF(3,"sms"),e.k0s(),e.j41(4,"h3"),e.EFF(5,"Login with OTP"),e.k0s(),e.j41(6,"p"),e.EFF(7,"Enter your email or phone number to receive a one-time password"),e.k0s()(),e.j41(8,"mat-form-field",8)(9,"mat-label"),e.EFF(10,"Email or Phone Number"),e.k0s(),e.nrm(11,"input",32),e.j41(12,"mat-icon",10),e.EFF(13,"contact_mail"),e.k0s(),e.j41(14,"mat-error"),e.EFF(15),e.k0s()(),e.DNE(16,ne,3,3,"button",33)(17,ae,16,4,"div",15),e.j41(18,"button",30),e.bIt("click",function(){r.eBV(t);const o=e.XpG();return r.Njj(o.toggleOTPLogin())}),e.j41(19,"mat-icon"),e.EFF(20,"arrow_back"),e.k0s(),e.EFF(21," Back to Login "),e.k0s()()}if(2&c){const t=e.XpG();e.Y8G("formGroup",t.otpForm),e.R7$(15),e.JRh(t.getFieldError(t.otpForm,"identifier")),e.R7$(),e.Y8G("ngIf",!t.otpSent),e.R7$(),e.Y8G("ngIf",t.otpSent)}}function de(c,d){if(1&c&&(e.j41(0,"div",28)(1,"div",29),e.nrm(2,"div",30),e.k0s(),e.j41(3,"span",31),e.EFF(4),e.k0s()()),2&c){const t=e.XpG();e.R7$(2),e.xc7("width",t.passwordStrength,"%"),e.Y8G("ngClass",t.getPasswordStrengthColor()),e.R7$(),e.Y8G("ngClass",t.getPasswordStrengthColor()),e.R7$(),e.JRh(t.passwordStrengthText)}}function me(c,d){if(1&c&&(e.j41(0,"div",32),e.EFF(1),e.k0s()),2&c){const t=e.XpG();e.R7$(),e.SpI(" ",t.getFieldError("acceptTerms")," ")}}function le(c,d){1&c&&e.nrm(0,"mat-spinner",33)}function he(c,d){1&c&&(e.j41(0,"span"),e.EFF(1,"Create Account"),e.k0s())}const be=[{path:"",redirectTo:"login",pathMatch:"full"},{path:"login",component:(()=>{class c{constructor(t,n,o,i,b,k){this.formBuilder=t,this.authService=n,this.oauthService=o,this.router=i,this.route=b,this.snackBar=k,this.loading=!1,this.hidePassword=!0,this.showOTPLogin=!1,this.showTwoFactor=!1,this.otpSent=!1,this.returnUrl="",this.oauthProviders=[],this.loginForm=this.formBuilder.group({email:["",[a.k0.required,a.k0.email]],password:["",[a.k0.required,a.k0.minLength(8)]]}),this.otpForm=this.formBuilder.group({identifier:["",[a.k0.required]],code:["",[a.k0.required,a.k0.pattern(/^\d{6}$/)]]}),this.twoFactorForm=this.formBuilder.group({twoFactorToken:["",[a.k0.required,a.k0.pattern(/^\d{6}$/)]]})}ngOnInit(){this.returnUrl=this.route.snapshot.queryParams.returnUrl||"/dashboard",this.oauthProviders=this.oauthService.getAvailableProviders(),this.authService.isAuthenticated&&this.router.navigate([this.returnUrl]);const t=this.route.snapshot.queryParams.message;t&&this.snackBar.open(t,"Close",{duration:5e3});const n=this.route.snapshot.queryParams.code;n&&this.handleOAuthCallback(n,this.route.snapshot.queryParams.state)}onSubmit(){this.loginForm.invalid?this.markFormGroupTouched(this.loginForm):(this.loading=!0,this.authService.login(this.loginForm.value).subscribe({next:n=>{n.requiresTwoFactor?(this.showTwoFactor=!0,this.snackBar.open("Please enter your two-factor authentication code","Close",{duration:5e3})):(this.snackBar.open("Login successful!","Close",{duration:3e3}),this.router.navigate([this.returnUrl])),this.loading=!1},error:n=>{this.snackBar.open(n.message||"Login failed","Close",{duration:5e3}),this.loading=!1}}))}onTwoFactorSubmit(){if(this.twoFactorForm.invalid)return void this.markFormGroupTouched(this.twoFactorForm);this.loading=!0;const t={...this.loginForm.value,twoFactorToken:this.twoFactorForm.value.twoFactorToken};this.authService.login(t).subscribe({next:n=>{this.snackBar.open("Login successful!","Close",{duration:3e3}),this.router.navigate([this.returnUrl]),this.loading=!1},error:n=>{this.snackBar.open(n.message||"Two-factor authentication failed","Close",{duration:5e3}),this.loading=!1}})}sendOTP(){const t=this.otpForm.get("identifier")?.value;t?(this.loading=!0,this.authService.sendOTP({identifier:t,type:"login"}).subscribe({next:()=>{this.otpSent=!0,this.snackBar.open("OTP sent successfully!","Close",{duration:3e3}),this.loading=!1},error:o=>{this.snackBar.open(o.message||"Failed to send OTP","Close",{duration:5e3}),this.loading=!1}})):this.snackBar.open("Please enter email or phone number","Close",{duration:3e3})}loginWithOTP(){if(this.otpForm.invalid)return void this.markFormGroupTouched(this.otpForm);this.loading=!0;const{identifier:t,code:n}=this.otpForm.value;this.authService.loginWithOTP(t,n).subscribe({next:()=>{this.snackBar.open("Login successful!","Close",{duration:3e3}),this.router.navigate([this.returnUrl]),this.loading=!1},error:o=>{this.snackBar.open(o.message||"OTP login failed","Close",{duration:5e3}),this.loading=!1}})}toggleOTPLogin(){this.showOTPLogin=!this.showOTPLogin,this.showTwoFactor=!1,this.otpSent=!1,this.otpForm.reset()}backToLogin(){this.showTwoFactor=!1,this.showOTPLogin=!1,this.otpSent=!1}loginWithOAuth(t){this.loading=!0,this.oauthService.initiateOAuthLogin(t)}handleOAuthCallback(t,n){this.loading=!0,this.oauthService.handleOAuthCallback(t,n).subscribe({next:o=>{this.snackBar.open("OAuth login successful!","Close",{duration:3e3}),this.router.navigate([this.returnUrl]),this.loading=!1},error:o=>{this.snackBar.open(o.message||"OAuth login failed","Close",{duration:5e3}),this.loading=!1,this.router.navigate([],{relativeTo:this.route,queryParams:{},replaceUrl:!0})}})}getFieldError(t,n){const o=t.get(n);if(o?.errors&&o.touched){if(o.errors.required)return`${n} is required`;if(o.errors.email)return"Please enter a valid email";if(o.errors.minlength)return`${n} must be at least ${o.errors.minlength.requiredLength} characters`;if(o.errors.pattern)return"Please enter a valid format"}return""}markFormGroupTouched(t){Object.keys(t.controls).forEach(n=>{t.get(n)?.markAsTouched()})}static#e=this.\u0275fac=function(n){return new(n||c)(e.rXU(a.ok),e.rXU(T.u),e.rXU(q.T),e.rXU(y.Ix),e.rXU(y.nX),e.rXU(v.UG))};static#t=this.\u0275cmp=e.VBU({type:c,selectors:[["app-login"]],standalone:!1,decls:15,vars:3,consts:[[1,"auth-container"],[1,"auth-card","fade-in"],[1,"auth-header"],[1,"security-badge"],[1,"auth-content"],[3,"formGroup","ngSubmit",4,"ngIf"],[3,"formGroup",4,"ngIf"],[3,"ngSubmit","formGroup"],["appearance","outline",1,"form-field"],["matInput","","type","email","formControlName","email","autocomplete","email"],["matSuffix",""],["matInput","","formControlName","password","autocomplete","current-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],["mat-raised-button","","color","primary","type","submit",1,"submit-button",3,"disabled"],["diameter","20",4,"ngIf"],[4,"ngIf"],[1,"divider"],["mat-stroked-button","","type","button",1,"w-100",3,"click"],[1,"oauth-section","mt-3"],[1,"oauth-buttons"],["mat-stroked-button","","type","button","class","oauth-button",3,"border-color","disabled","click",4,"ngFor","ngForOf"],[1,"text-center","mt-3"],["routerLink","/auth/forgot-password",1,"text-primary"],[1,"text-center","mt-2"],["routerLink","/auth/register",1,"text-primary"],["diameter","20"],["mat-stroked-button","","type","button",1,"oauth-button",3,"click","disabled"],[1,"text-center","mb-3"],["color","primary",2,"font-size","48px","width","48px","height","48px"],["matInput","","formControlName","twoFactorToken","placeholder","000000","maxlength","6","autocomplete","one-time-code"],["mat-button","","type","button",1,"w-100","mt-2",3,"click"],[3,"formGroup"],["matInput","","formControlName","identifier","placeholder","<EMAIL> or +1234567890"],["mat-raised-button","","color","accent","type","button","class","submit-button",3,"disabled","click",4,"ngIf"],["mat-raised-button","","color","accent","type","button",1,"submit-button",3,"click","disabled"],["matInput","","formControlName","code","placeholder","000000","maxlength","6","autocomplete","one-time-code"],["mat-raised-button","","color","primary","type","button",1,"submit-button",3,"click","disabled"]],template:function(n,o){1&n&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h1"),e.EFF(4,"Welcome Back"),e.k0s(),e.j41(5,"p"),e.EFF(6,"Sign in to your secure account"),e.k0s(),e.j41(7,"div",3)(8,"mat-icon"),e.EFF(9,"security"),e.k0s(),e.EFF(10," Secure Login "),e.k0s()(),e.j41(11,"div",4),e.DNE(12,Q,42,9,"form",5)(13,ee,23,5,"form",5)(14,ie,22,4,"form",6),e.k0s()()()),2&n&&(e.R7$(12),e.Y8G("ngIf",!o.showOTPLogin&&!o.showTwoFactor),e.R7$(),e.Y8G("ngIf",o.showTwoFactor),e.R7$(),e.Y8G("ngIf",o.showOTPLogin))},dependencies:[u.Sq,u.bT,a.qT,a.me,a.BC,a.cb,a.tU,a.j4,a.JD,p.Wk,h.j,h.M,h.b,h.g,g.fg,f.$z,j.M,_.An,x.LG],styles:['.w-100[_ngcontent-%COMP%]{width:100%}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:1rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:1rem}.text-center[_ngcontent-%COMP%]{text-align:center}.text-primary[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.text-primary[_ngcontent-%COMP%]:hover{text-decoration:underline}.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{position:relative;text-align:center;margin:1.5rem 0}.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]:before{content:"";position:absolute;top:50%;left:0;right:0;height:1px;background:#e0e0e0}.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#fff;padding:0 1rem;color:#666;font-size:.875rem}.oauth-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.75rem;padding:.75rem 1rem;width:100%;border-radius:8px;transition:all .2s ease}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none;box-shadow:none}']})}return c})()},{path:"register",component:(()=>{class c{constructor(t,n,o,i){this.formBuilder=t,this.authService=n,this.router=o,this.snackBar=i,this.loading=!1,this.hidePassword=!0,this.hideConfirmPassword=!0,this.passwordStrength=0,this.passwordStrengthText="",this.registerForm=this.formBuilder.group({firstName:["",[a.k0.required,a.k0.minLength(2),a.k0.maxLength(50)]],lastName:["",[a.k0.required,a.k0.minLength(2),a.k0.maxLength(50)]],email:["",[a.k0.required,a.k0.email]],phone:["",[a.k0.pattern(/^\+?[1-9]\d{1,14}$/)]],password:["",[a.k0.required,a.k0.minLength(8),this.passwordValidator]],confirmPassword:["",[a.k0.required]],acceptTerms:[!1,[a.k0.requiredTrue]]},{validators:this.passwordMatchValidator})}ngOnInit(){this.authService.isAuthenticated&&this.router.navigate(["/dashboard"]),this.registerForm.get("password")?.valueChanges.subscribe(t=>{this.updatePasswordStrength(t)})}onSubmit(){this.registerForm.invalid?this.markFormGroupTouched(this.registerForm):(this.loading=!0,this.authService.register(this.registerForm.value).subscribe({next:n=>{this.snackBar.open("Registration successful! Please check your email for verification.","Close",{duration:8e3}),this.router.navigate(["/auth/login"],{queryParams:{message:"Please verify your email before logging in."}}),this.loading=!1},error:n=>{this.snackBar.open(n.message||"Registration failed","Close",{duration:5e3}),this.loading=!1}}))}getFieldError(t){const n=this.registerForm.get(t);if(n?.errors&&n.touched){if(n.errors.required)return`${this.getFieldLabel(t)} is required`;if(n.errors.email)return"Please enter a valid email address";if(n.errors.minlength)return`${this.getFieldLabel(t)} must be at least ${n.errors.minlength.requiredLength} characters`;if(n.errors.maxlength)return`${this.getFieldLabel(t)} must not exceed ${n.errors.maxlength.requiredLength} characters`;if(n.errors.pattern)return"phone"===t?"Please enter a valid phone number":"Please enter a valid format";if(n.errors.passwordStrength)return n.errors.passwordStrength;if(n.errors.passwordMismatch)return"Passwords do not match";if(n.errors.requiredTrue)return"You must accept the terms and conditions"}return""}getFieldLabel(t){return{firstName:"First name",lastName:"Last name",email:"Email",phone:"Phone number",password:"Password",confirmPassword:"Confirm password"}[t]||t}passwordValidator(t){const n=t.value;if(!n)return null;const o=[];return n.length<8&&o.push("at least 8 characters"),/[A-Z]/.test(n)||o.push("one uppercase letter"),/[a-z]/.test(n)||o.push("one lowercase letter"),/\d/.test(n)||o.push("one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(n)||o.push("one special character"),o.length>0?{passwordStrength:`Password must contain ${o.join(", ")}`}:null}passwordMatchValidator(t){const n=t.get("password"),o=t.get("confirmPassword");return n&&o?n.value!==o.value?(o.setErrors({passwordMismatch:!0}),{passwordMismatch:!0}):(o.errors?.passwordMismatch&&(delete o.errors.passwordMismatch,0===Object.keys(o.errors).length&&o.setErrors(null)),null):null}updatePasswordStrength(t){if(!t)return this.passwordStrength=0,void(this.passwordStrengthText="");let n=0;n=[t.length>=8,/[A-Z]/.test(t),/[a-z]/.test(t),/\d/.test(t),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(t)].filter(b=>b).length,this.passwordStrength=n/5*100,this.passwordStrengthText=["Very Weak","Weak","Fair","Good","Strong"][n-1]||"Very Weak"}getPasswordStrengthColor(){return this.passwordStrength<20?"warn":this.passwordStrength<40?"accent":"primary"}markFormGroupTouched(t){Object.keys(t.controls).forEach(n=>{t.get(n)?.markAsTouched()})}static#e=this.\u0275fac=function(n){return new(n||c)(e.rXU(a.ok),e.rXU(T.u),e.rXU(y.Ix),e.rXU(v.UG))};static#t=this.\u0275cmp=e.VBU({type:c,selectors:[["app-register"]],standalone:!1,decls:84,vars:16,consts:[[1,"auth-container"],[1,"auth-card","fade-in"],[1,"auth-header"],[1,"security-badge"],[1,"auth-content"],[3,"ngSubmit","formGroup"],[1,"name-row"],["appearance","outline",1,"form-field","half-width"],["matInput","","formControlName","firstName","autocomplete","given-name"],["matSuffix",""],["matInput","","formControlName","lastName","autocomplete","family-name"],["appearance","outline",1,"form-field"],["matInput","","type","email","formControlName","email","autocomplete","email"],["matInput","","type","tel","formControlName","phone","placeholder","+1234567890","autocomplete","tel"],["matInput","","formControlName","password","autocomplete","new-password",3,"type"],["mat-icon-button","","matSuffix","","type","button",3,"click"],["class","password-strength",4,"ngIf"],["matInput","","formControlName","confirmPassword","autocomplete","new-password",3,"type"],[1,"checkbox-field"],["formControlName","acceptTerms"],["href","/terms","target","_blank"],["href","/privacy","target","_blank"],["class","error-message",4,"ngIf"],["mat-raised-button","","color","primary","type","submit",1,"submit-button",3,"disabled"],["diameter","20",4,"ngIf"],[4,"ngIf"],[1,"text-center","mt-3"],["routerLink","/auth/login",1,"text-primary"],[1,"password-strength"],[1,"strength-bar"],[1,"strength-fill",3,"ngClass"],[1,"strength-text",3,"ngClass"],[1,"error-message"],["diameter","20"]],template:function(n,o){if(1&n&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h1"),e.EFF(4,"Create Account"),e.k0s(),e.j41(5,"p"),e.EFF(6,"Join our secure platform today"),e.k0s(),e.j41(7,"div",3)(8,"mat-icon"),e.EFF(9,"verified_user"),e.k0s(),e.EFF(10," Secure Registration "),e.k0s()(),e.j41(11,"div",4)(12,"form",5),e.bIt("ngSubmit",function(){return o.onSubmit()}),e.j41(13,"div",6)(14,"mat-form-field",7)(15,"mat-label"),e.EFF(16,"First Name"),e.k0s(),e.nrm(17,"input",8),e.j41(18,"mat-icon",9),e.EFF(19,"person"),e.k0s(),e.j41(20,"mat-error"),e.EFF(21),e.k0s()(),e.j41(22,"mat-form-field",7)(23,"mat-label"),e.EFF(24,"Last Name"),e.k0s(),e.nrm(25,"input",10),e.j41(26,"mat-icon",9),e.EFF(27,"person"),e.k0s(),e.j41(28,"mat-error"),e.EFF(29),e.k0s()()(),e.j41(30,"mat-form-field",11)(31,"mat-label"),e.EFF(32,"Email Address"),e.k0s(),e.nrm(33,"input",12),e.j41(34,"mat-icon",9),e.EFF(35,"email"),e.k0s(),e.j41(36,"mat-error"),e.EFF(37),e.k0s()(),e.j41(38,"mat-form-field",11)(39,"mat-label"),e.EFF(40,"Phone Number (Optional)"),e.k0s(),e.nrm(41,"input",13),e.j41(42,"mat-icon",9),e.EFF(43,"phone"),e.k0s(),e.j41(44,"mat-error"),e.EFF(45),e.k0s(),e.j41(46,"mat-hint"),e.EFF(47,"For SMS verification and 2FA"),e.k0s()(),e.j41(48,"mat-form-field",11)(49,"mat-label"),e.EFF(50,"Password"),e.k0s(),e.nrm(51,"input",14),e.j41(52,"button",15),e.bIt("click",function(){return o.hidePassword=!o.hidePassword}),e.j41(53,"mat-icon"),e.EFF(54),e.k0s()(),e.j41(55,"mat-error"),e.EFF(56),e.k0s()(),e.DNE(57,de,5,5,"div",16),e.j41(58,"mat-form-field",11)(59,"mat-label"),e.EFF(60,"Confirm Password"),e.k0s(),e.nrm(61,"input",17),e.j41(62,"button",15),e.bIt("click",function(){return o.hideConfirmPassword=!o.hideConfirmPassword}),e.j41(63,"mat-icon"),e.EFF(64),e.k0s()(),e.j41(65,"mat-error"),e.EFF(66),e.k0s()(),e.j41(67,"div",18)(68,"mat-checkbox",19),e.EFF(69," I agree to the "),e.j41(70,"a",20),e.EFF(71,"Terms of Service"),e.k0s(),e.EFF(72," and "),e.j41(73,"a",21),e.EFF(74,"Privacy Policy"),e.k0s()(),e.DNE(75,me,2,1,"div",22),e.k0s(),e.j41(76,"button",23),e.DNE(77,le,1,0,"mat-spinner",24)(78,he,2,0,"span",25),e.k0s(),e.j41(79,"div",26)(80,"span"),e.EFF(81,"Already have an account? "),e.k0s(),e.j41(82,"a",27),e.EFF(83,"Sign In"),e.k0s()()()()()()),2&n){let i;e.R7$(12),e.Y8G("formGroup",o.registerForm),e.R7$(9),e.JRh(o.getFieldError("firstName")),e.R7$(8),e.JRh(o.getFieldError("lastName")),e.R7$(8),e.JRh(o.getFieldError("email")),e.R7$(8),e.JRh(o.getFieldError("phone")),e.R7$(6),e.Y8G("type",o.hidePassword?"password":"text"),e.R7$(3),e.JRh(o.hidePassword?"visibility_off":"visibility"),e.R7$(2),e.JRh(o.getFieldError("password")),e.R7$(),e.Y8G("ngIf",null==(i=o.registerForm.get("password"))?null:i.value),e.R7$(4),e.Y8G("type",o.hideConfirmPassword?"password":"text"),e.R7$(3),e.JRh(o.hideConfirmPassword?"visibility_off":"visibility"),e.R7$(2),e.JRh(o.getFieldError("confirmPassword")),e.R7$(9),e.Y8G("ngIf",o.getFieldError("acceptTerms")),e.R7$(),e.Y8G("disabled",o.loading),e.R7$(),e.Y8G("ngIf",o.loading),e.R7$(),e.Y8G("ngIf",!o.loading)}},dependencies:[u.YU,u.bT,a.qT,a.me,a.BC,a.cb,a.j4,a.JD,p.Wk,h.j,h.M,h.c,h.b,h.g,g.fg,f.$z,j.M,_.An,E,x.LG],styles:[".name-row[_ngcontent-%COMP%]{display:flex;gap:1rem}.name-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%]{flex:1}.password-strength[_ngcontent-%COMP%]{margin-bottom:1rem}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]{width:100%;height:4px;background:#e0e0e0;border-radius:2px;overflow:hidden;margin-bottom:.5rem}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill[_ngcontent-%COMP%]{height:100%;transition:width .3s ease,background-color .3s ease}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.warn[_ngcontent-%COMP%]{background:#f44336}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.accent[_ngcontent-%COMP%]{background:#ff9800}.password-strength[_ngcontent-%COMP%]   .strength-bar[_ngcontent-%COMP%]   .strength-fill.primary[_ngcontent-%COMP%]{background:#4caf50}.password-strength[_ngcontent-%COMP%]   .strength-text[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500}.password-strength[_ngcontent-%COMP%]   .strength-text.warn[_ngcontent-%COMP%]{color:#f44336}.password-strength[_ngcontent-%COMP%]   .strength-text.accent[_ngcontent-%COMP%]{color:#ff9800}.password-strength[_ngcontent-%COMP%]   .strength-text.primary[_ngcontent-%COMP%]{color:#4caf50}.checkbox-field[_ngcontent-%COMP%]{margin:1.5rem 0}.checkbox-field[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{font-size:.875rem}.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.checkbox-field[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@media (max-width: 768px){.name-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}}"]})}return c})()}];let ue=(()=>{class c{static#e=this.\u0275fac=function(n){return new(n||c)};static#t=this.\u0275mod=e.$C({type:c});static#o=this.\u0275inj=r.G2t({imports:[u.MD,a.X1,p.iI.forChild(be),I.Hu,O.M,g.fS,f.Hl,_.m_,Y,x.D6,v._T,U.w]})}return c})()}}]);