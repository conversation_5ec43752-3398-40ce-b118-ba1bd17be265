{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.tokenKey = environment.security.jwtTokenKey;\n    this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n    this.currentUser = this.currentUserSubject.asObservable();\n  }\n  get currentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  get isAuthenticated() {\n    return !!this.getToken() && !!this.currentUserValue;\n  }\n  get isEmailVerified() {\n    return this.currentUserValue?.emailVerified || false;\n  }\n  get isTwoFactorEnabled() {\n    return this.currentUserValue?.twoFactorEnabled || false;\n  }\n  register(userData) {\n    return this.http.post(`${environment.apiUrl}/auth/signup`, userData).pipe(catchError(this.handleError));\n  }\n  login(credentials) {\n    return this.http.post(`${environment.apiUrl}/auth/login`, credentials).pipe(tap(response => {\n      if (response.token && !response.requiresTwoFactor) {\n        this.setToken(response.token);\n        this.currentUserSubject.next(response.user);\n      }\n    }), catchError(this.handleError));\n  }\n  logout() {\n    this.removeToken();\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/auth/login']);\n  }\n  verifyEmail(token) {\n    return this.http.post(`${environment.apiUrl}/auth/verify-email`, {\n      token\n    }).pipe(catchError(this.handleError));\n  }\n  forgotPassword(email) {\n    return this.http.post(`${environment.apiUrl}/auth/forgot-password`, {\n      email\n    }).pipe(catchError(this.handleError));\n  }\n  resetPassword(resetData) {\n    return this.http.post(`${environment.apiUrl}/auth/reset-password`, resetData).pipe(catchError(this.handleError));\n  }\n  sendOTP(request) {\n    const endpoint = request.identifier.includes('@') ? 'send-email' : 'send-sms';\n    return this.http.post(`${environment.apiUrl}/otp/${endpoint}`, request).pipe(catchError(this.handleError));\n  }\n  verifyOTP(verification) {\n    return this.http.post(`${environment.apiUrl}/otp/verify`, verification).pipe(catchError(this.handleError));\n  }\n  loginWithOTP(identifier, code) {\n    return this.http.post(`${environment.apiUrl}/otp/login`, {\n      identifier,\n      code\n    }).pipe(tap(response => {\n      if (response.token) {\n        this.setToken(response.token);\n        this.currentUserSubject.next(response.user);\n      }\n    }), catchError(this.handleError));\n  }\n  // New API: Change Password\n  changePassword(currentPassword, newPassword, twoFactorToken) {\n    const headers = this.getAuthHeaders();\n    const body = {\n      currentPassword,\n      newPassword\n    };\n    if (twoFactorToken) {\n      body.twoFactorToken = twoFactorToken;\n    }\n    return this.http.post(`${environment.apiUrl}/auth/change-password`, body, {\n      headers\n    }).pipe(catchError(this.handleError));\n  }\n  // New API: OAuth URLs\n  getOAuthUrl(provider) {\n    return this.http.get(`${environment.apiUrl}/auth/oauth/${provider}/url`).pipe(catchError(this.handleError));\n  }\n  // New API: OAuth Callback\n  oauthCallback(provider, code, state) {\n    const body = {\n      code\n    };\n    if (state) {\n      body.state = state;\n    }\n    return this.http.post(`${environment.apiUrl}/auth/oauth/${provider}/callback`, body).pipe(tap(response => {\n      if (response.token) {\n        this.setToken(response.token);\n        this.currentUserSubject.next(response.user);\n      }\n    }), catchError(this.handleError));\n  }\n  // New API: Send OTP (unified endpoint)\n  sendOTPUnified(email, phone, type = 'verification') {\n    const body = {\n      type\n    };\n    if (email) body.email = email;\n    if (phone) body.phone = phone;\n    return this.http.post(`${environment.apiUrl}/otp/send`, body).pipe(catchError(this.handleError));\n  }\n  refreshUserData() {\n    return this.http.get(`${environment.apiUrl}/auth/me`).pipe(tap(user => {\n      this.currentUserSubject.next(user);\n    }), catchError(this.handleError));\n  }\n  getToken() {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(this.tokenKey);\n    }\n    return null;\n  }\n  setToken(token) {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.tokenKey, token);\n    }\n  }\n  removeToken() {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(this.tokenKey);\n    }\n  }\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n  getUserFromStorage() {\n    if (typeof window !== 'undefined') {\n      const token = this.getToken();\n      if (token) {\n        try {\n          // Decode JWT token to get user info (basic implementation)\n          const payload = JSON.parse(atob(token.split('.')[1]));\n          return payload.user || null;\n        } catch (error) {\n          console.error('Error parsing token:', error);\n          this.removeToken();\n        }\n      }\n    }\n    return null;\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = error.error.message;\n    } else {\n      // Server-side error\n      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n    }\n    console.error('Auth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  // Security utilities\n  isTokenExpired() {\n    const token = this.getToken();\n    if (!token) return true;\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const expiry = payload.exp * 1000; // Convert to milliseconds\n      return Date.now() > expiry;\n    } catch (error) {\n      return true;\n    }\n  }\n  autoLogout() {\n    const token = this.getToken();\n    if (token && this.isTokenExpired()) {\n      this.logout();\n    }\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "catchError", "tap", "environment", "AuthService", "constructor", "http", "router", "<PERSON><PERSON><PERSON>", "security", "jwtTokenKey", "currentUserSubject", "getUserFromStorage", "currentUser", "asObservable", "currentUserValue", "value", "isAuthenticated", "getToken", "isEmailVerified", "emailVerified", "isTwoFactorEnabled", "twoFactorEnabled", "register", "userData", "post", "apiUrl", "pipe", "handleError", "login", "credentials", "response", "token", "requiresTwoFactor", "setToken", "next", "user", "logout", "removeToken", "navigate", "verifyEmail", "forgotPassword", "email", "resetPassword", "resetData", "sendOTP", "request", "endpoint", "identifier", "includes", "verifyOTP", "verification", "loginWithOTP", "code", "changePassword", "currentPassword", "newPassword", "twoFactorToken", "headers", "getAuthHeaders", "body", "getOAuthUrl", "provider", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "sendOTPUnified", "phone", "type", "refreshUserData", "window", "localStorage", "getItem", "setItem", "removeItem", "payload", "JSON", "parse", "atob", "split", "error", "console", "errorMessage", "ErrorEvent", "message", "status", "Error", "isTokenExpired", "expiry", "exp", "Date", "now", "autoLogout", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { environment } from '../../environments/environment';\nimport {\n  User,\n  UserRegistration,\n  UserLogin,\n  LoginResponse,\n  PasswordReset,\n  ApiResponse,\n  OTPRequest,\n  OTPVerification\n} from '../models/index';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private currentUserSubject: BehaviorSubject<User | null>;\n  public currentUser: Observable<User | null>;\n  private tokenKey = environment.security.jwtTokenKey;\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {\n    this.currentUserSubject = new BehaviorSubject<User | null>(this.getUserFromStorage());\n    this.currentUser = this.currentUserSubject.asObservable();\n  }\n\n  public get currentUserValue(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  public get isAuthenticated(): boolean {\n    return !!this.getToken() && !!this.currentUserValue;\n  }\n\n  public get isEmailVerified(): boolean {\n    return this.currentUserValue?.emailVerified || false;\n  }\n\n  public get isTwoFactorEnabled(): boolean {\n    return this.currentUserValue?.twoFactorEnabled || false;\n  }\n\n  register(userData: UserRegistration): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/signup`, userData)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  login(credentials: UserLogin): Observable<LoginResponse> {\n    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/login`, credentials)\n      .pipe(\n        tap(response => {\n          if (response.token && !response.requiresTwoFactor) {\n            this.setToken(response.token);\n            this.currentUserSubject.next(response.user);\n          }\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  logout(): void {\n    this.removeToken();\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/auth/login']);\n  }\n\n  verifyEmail(token: string): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/verify-email`, { token })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  forgotPassword(email: string): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/forgot-password`, { email })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  resetPassword(resetData: PasswordReset): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/reset-password`, resetData)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  sendOTP(request: OTPRequest): Observable<ApiResponse> {\n    const endpoint = request.identifier.includes('@') ? 'send-email' : 'send-sms';\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/otp/${endpoint}`, request)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  verifyOTP(verification: OTPVerification): Observable<ApiResponse> {\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/otp/verify`, verification)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  loginWithOTP(identifier: string, code: string): Observable<LoginResponse> {\n    return this.http.post<LoginResponse>(`${environment.apiUrl}/otp/login`, { identifier, code })\n      .pipe(\n        tap(response => {\n          if (response.token) {\n            this.setToken(response.token);\n            this.currentUserSubject.next(response.user);\n          }\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  // New API: Change Password\n  changePassword(currentPassword: string, newPassword: string, twoFactorToken?: string): Observable<ApiResponse> {\n    const headers = this.getAuthHeaders();\n    const body: any = { currentPassword, newPassword };\n    if (twoFactorToken) {\n      body.twoFactorToken = twoFactorToken;\n    }\n\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/change-password`, body, { headers })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  // New API: OAuth URLs\n  getOAuthUrl(provider: 'google' | 'github' | 'microsoft'): Observable<{url: string}> {\n    return this.http.get<{url: string}>(`${environment.apiUrl}/auth/oauth/${provider}/url`)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  // New API: OAuth Callback\n  oauthCallback(provider: 'google' | 'github' | 'microsoft', code: string, state?: string): Observable<LoginResponse> {\n    const body: any = { code };\n    if (state) {\n      body.state = state;\n    }\n\n    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/oauth/${provider}/callback`, body)\n      .pipe(\n        tap(response => {\n          if (response.token) {\n            this.setToken(response.token);\n            this.currentUserSubject.next(response.user);\n          }\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  // New API: Send OTP (unified endpoint)\n  sendOTPUnified(email?: string, phone?: string, type: 'login' | 'verification' = 'verification'): Observable<ApiResponse> {\n    const body: any = { type };\n    if (email) body.email = email;\n    if (phone) body.phone = phone;\n\n    return this.http.post<ApiResponse>(`${environment.apiUrl}/otp/send`, body)\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  refreshUserData(): Observable<User> {\n    return this.http.get<User>(`${environment.apiUrl}/auth/me`)\n      .pipe(\n        tap(user => {\n          this.currentUserSubject.next(user);\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  getToken(): string | null {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem(this.tokenKey);\n    }\n    return null;\n  }\n\n  setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.tokenKey, token);\n    }\n  }\n\n  removeToken(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(this.tokenKey);\n    }\n  }\n\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': token ? `Bearer ${token}` : ''\n    });\n  }\n\n  private getUserFromStorage(): User | null {\n    if (typeof window !== 'undefined') {\n      const token = this.getToken();\n      if (token) {\n        try {\n          // Decode JWT token to get user info (basic implementation)\n          const payload = JSON.parse(atob(token.split('.')[1]));\n          return payload.user || null;\n        } catch (error) {\n          console.error('Error parsing token:', error);\n          this.removeToken();\n        }\n      }\n    }\n    return null;\n  }\n\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'An error occurred';\n    \n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = error.error.message;\n    } else {\n      // Server-side error\n      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n    }\n    \n    console.error('Auth Service Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n\n  // Security utilities\n  isTokenExpired(): boolean {\n    const token = this.getToken();\n    if (!token) return true;\n\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const expiry = payload.exp * 1000; // Convert to milliseconds\n      return Date.now() > expiry;\n    } catch (error) {\n      return true;\n    }\n  }\n\n  autoLogout(): void {\n    const token = this.getToken();\n    if (token && this.isTokenExpired()) {\n      this.logout();\n    }\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAAcC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAErD,SAASC,WAAW,QAAQ,gCAAgC;;;;AAe5D,OAAM,MAAOC,WAAW;EAKtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IAJR,KAAAC,QAAQ,GAAGL,WAAW,CAACM,QAAQ,CAACC,WAAW;IAMjD,IAAI,CAACC,kBAAkB,GAAG,IAAIZ,eAAe,CAAc,IAAI,CAACa,kBAAkB,EAAE,CAAC;IACrF,IAAI,CAACC,WAAW,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;EAC3D;EAEA,IAAWC,gBAAgBA,CAAA;IACzB,OAAO,IAAI,CAACJ,kBAAkB,CAACK,KAAK;EACtC;EAEA,IAAWC,eAAeA,CAAA;IACxB,OAAO,CAAC,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAC,CAAC,IAAI,CAACH,gBAAgB;EACrD;EAEA,IAAWI,eAAeA,CAAA;IACxB,OAAO,IAAI,CAACJ,gBAAgB,EAAEK,aAAa,IAAI,KAAK;EACtD;EAEA,IAAWC,kBAAkBA,CAAA;IAC3B,OAAO,IAAI,CAACN,gBAAgB,EAAEO,gBAAgB,IAAI,KAAK;EACzD;EAEAC,QAAQA,CAACC,QAA0B;IACjC,OAAO,IAAI,CAAClB,IAAI,CAACmB,IAAI,CAAc,GAAGtB,WAAW,CAACuB,MAAM,cAAc,EAAEF,QAAQ,CAAC,CAC9EG,IAAI,CACH1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAC,KAAKA,CAACC,WAAsB;IAC1B,OAAO,IAAI,CAACxB,IAAI,CAACmB,IAAI,CAAgB,GAAGtB,WAAW,CAACuB,MAAM,aAAa,EAAEI,WAAW,CAAC,CAClFH,IAAI,CACHzB,GAAG,CAAC6B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,IAAI,CAACD,QAAQ,CAACE,iBAAiB,EAAE;QACjD,IAAI,CAACC,QAAQ,CAACH,QAAQ,CAACC,KAAK,CAAC;QAC7B,IAAI,CAACrB,kBAAkB,CAACwB,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC;MAC7C;IACF,CAAC,CAAC,EACFnC,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAS,MAAMA,CAAA;IACJ,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAAC3B,kBAAkB,CAACwB,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAAC5B,MAAM,CAACgC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAC,WAAWA,CAACR,KAAa;IACvB,OAAO,IAAI,CAAC1B,IAAI,CAACmB,IAAI,CAAc,GAAGtB,WAAW,CAACuB,MAAM,oBAAoB,EAAE;MAAEM;IAAK,CAAE,CAAC,CACrFL,IAAI,CACH1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAa,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAI,CAACpC,IAAI,CAACmB,IAAI,CAAc,GAAGtB,WAAW,CAACuB,MAAM,uBAAuB,EAAE;MAAEgB;IAAK,CAAE,CAAC,CACxFf,IAAI,CACH1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAe,aAAaA,CAACC,SAAwB;IACpC,OAAO,IAAI,CAACtC,IAAI,CAACmB,IAAI,CAAc,GAAGtB,WAAW,CAACuB,MAAM,sBAAsB,EAAEkB,SAAS,CAAC,CACvFjB,IAAI,CACH1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAiB,OAAOA,CAACC,OAAmB;IACzB,MAAMC,QAAQ,GAAGD,OAAO,CAACE,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC,GAAG,YAAY,GAAG,UAAU;IAC7E,OAAO,IAAI,CAAC3C,IAAI,CAACmB,IAAI,CAAc,GAAGtB,WAAW,CAACuB,MAAM,QAAQqB,QAAQ,EAAE,EAAED,OAAO,CAAC,CACjFnB,IAAI,CACH1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAsB,SAASA,CAACC,YAA6B;IACrC,OAAO,IAAI,CAAC7C,IAAI,CAACmB,IAAI,CAAc,GAAGtB,WAAW,CAACuB,MAAM,aAAa,EAAEyB,YAAY,CAAC,CACjFxB,IAAI,CACH1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAwB,YAAYA,CAACJ,UAAkB,EAAEK,IAAY;IAC3C,OAAO,IAAI,CAAC/C,IAAI,CAACmB,IAAI,CAAgB,GAAGtB,WAAW,CAACuB,MAAM,YAAY,EAAE;MAAEsB,UAAU;MAAEK;IAAI,CAAE,CAAC,CAC1F1B,IAAI,CACHzB,GAAG,CAAC6B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,IAAI,CAACE,QAAQ,CAACH,QAAQ,CAACC,KAAK,CAAC;QAC7B,IAAI,CAACrB,kBAAkB,CAACwB,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC;MAC7C;IACF,CAAC,CAAC,EACFnC,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEA;EACA0B,cAAcA,CAACC,eAAuB,EAAEC,WAAmB,EAAEC,cAAuB;IAClF,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,EAAE;IACrC,MAAMC,IAAI,GAAQ;MAAEL,eAAe;MAAEC;IAAW,CAAE;IAClD,IAAIC,cAAc,EAAE;MAClBG,IAAI,CAACH,cAAc,GAAGA,cAAc;IACtC;IAEA,OAAO,IAAI,CAACnD,IAAI,CAACmB,IAAI,CAAc,GAAGtB,WAAW,CAACuB,MAAM,uBAAuB,EAAEkC,IAAI,EAAE;MAAEF;IAAO,CAAE,CAAC,CAChG/B,IAAI,CACH1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEA;EACAiC,WAAWA,CAACC,QAA2C;IACrD,OAAO,IAAI,CAACxD,IAAI,CAACyD,GAAG,CAAgB,GAAG5D,WAAW,CAACuB,MAAM,eAAeoC,QAAQ,MAAM,CAAC,CACpFnC,IAAI,CACH1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEA;EACAoC,aAAaA,CAACF,QAA2C,EAAET,IAAY,EAAEY,KAAc;IACrF,MAAML,IAAI,GAAQ;MAAEP;IAAI,CAAE;IAC1B,IAAIY,KAAK,EAAE;MACTL,IAAI,CAACK,KAAK,GAAGA,KAAK;IACpB;IAEA,OAAO,IAAI,CAAC3D,IAAI,CAACmB,IAAI,CAAgB,GAAGtB,WAAW,CAACuB,MAAM,eAAeoC,QAAQ,WAAW,EAAEF,IAAI,CAAC,CAChGjC,IAAI,CACHzB,GAAG,CAAC6B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClB,IAAI,CAACE,QAAQ,CAACH,QAAQ,CAACC,KAAK,CAAC;QAC7B,IAAI,CAACrB,kBAAkB,CAACwB,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC;MAC7C;IACF,CAAC,CAAC,EACFnC,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEA;EACAsC,cAAcA,CAACxB,KAAc,EAAEyB,KAAc,EAAEC,IAAA,GAAiC,cAAc;IAC5F,MAAMR,IAAI,GAAQ;MAAEQ;IAAI,CAAE;IAC1B,IAAI1B,KAAK,EAAEkB,IAAI,CAAClB,KAAK,GAAGA,KAAK;IAC7B,IAAIyB,KAAK,EAAEP,IAAI,CAACO,KAAK,GAAGA,KAAK;IAE7B,OAAO,IAAI,CAAC7D,IAAI,CAACmB,IAAI,CAAc,GAAGtB,WAAW,CAACuB,MAAM,WAAW,EAAEkC,IAAI,CAAC,CACvEjC,IAAI,CACH1B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAyC,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC/D,IAAI,CAACyD,GAAG,CAAO,GAAG5D,WAAW,CAACuB,MAAM,UAAU,CAAC,CACxDC,IAAI,CACHzB,GAAG,CAACkC,IAAI,IAAG;MACT,IAAI,CAACzB,kBAAkB,CAACwB,IAAI,CAACC,IAAI,CAAC;IACpC,CAAC,CAAC,EACFnC,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACL;EAEAV,QAAQA,CAAA;IACN,IAAI,OAAOoD,MAAM,KAAK,WAAW,EAAE;MACjC,OAAOC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAChE,QAAQ,CAAC;IAC5C;IACA,OAAO,IAAI;EACb;EAEA0B,QAAQA,CAACF,KAAa;IACpB,IAAI,OAAOsC,MAAM,KAAK,WAAW,EAAE;MACjCC,YAAY,CAACE,OAAO,CAAC,IAAI,CAACjE,QAAQ,EAAEwB,KAAK,CAAC;IAC5C;EACF;EAEAM,WAAWA,CAAA;IACT,IAAI,OAAOgC,MAAM,KAAK,WAAW,EAAE;MACjCC,YAAY,CAACG,UAAU,CAAC,IAAI,CAAClE,QAAQ,CAAC;IACxC;EACF;EAEAmD,cAAcA,CAAA;IACZ,MAAM3B,KAAK,GAAG,IAAI,CAACd,QAAQ,EAAE;IAC7B,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAEkC,KAAK,GAAG,UAAUA,KAAK,EAAE,GAAG;KAC9C,CAAC;EACJ;EAEQpB,kBAAkBA,CAAA;IACxB,IAAI,OAAO0D,MAAM,KAAK,WAAW,EAAE;MACjC,MAAMtC,KAAK,GAAG,IAAI,CAACd,QAAQ,EAAE;MAC7B,IAAIc,KAAK,EAAE;QACT,IAAI;UACF;UACA,MAAM2C,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC9C,KAAK,CAAC+C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,OAAOJ,OAAO,CAACvC,IAAI,IAAI,IAAI;QAC7B,CAAC,CAAC,OAAO4C,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAAC1C,WAAW,EAAE;QACpB;MACF;IACF;IACA,OAAO,IAAI;EACb;EAEQV,WAAWA,CAACoD,KAAU;IAC5B,IAAIE,YAAY,GAAG,mBAAmB;IAEtC,IAAIF,KAAK,CAACA,KAAK,YAAYG,UAAU,EAAE;MACrC;MACAD,YAAY,GAAGF,KAAK,CAACA,KAAK,CAACI,OAAO;IACpC,CAAC,MAAM;MACL;MACAF,YAAY,GAAGF,KAAK,CAACA,KAAK,EAAEI,OAAO,IAAIJ,KAAK,CAACI,OAAO,IAAI,eAAeJ,KAAK,CAACK,MAAM,EAAE;IACvF;IAEAJ,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,OAAOhF,UAAU,CAAC,MAAM,IAAIsF,KAAK,CAACJ,YAAY,CAAC,CAAC;EAClD;EAEA;EACAK,cAAcA,CAAA;IACZ,MAAMvD,KAAK,GAAG,IAAI,CAACd,QAAQ,EAAE;IAC7B,IAAI,CAACc,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAI;MACF,MAAM2C,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC9C,KAAK,CAAC+C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMS,MAAM,GAAGb,OAAO,CAACc,GAAG,GAAG,IAAI,CAAC,CAAC;MACnC,OAAOC,IAAI,CAACC,GAAG,EAAE,GAAGH,MAAM;IAC5B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACd,OAAO,IAAI;IACb;EACF;EAEAY,UAAUA,CAAA;IACR,MAAM5D,KAAK,GAAG,IAAI,CAACd,QAAQ,EAAE;IAC7B,IAAIc,KAAK,IAAI,IAAI,CAACuD,cAAc,EAAE,EAAE;MAClC,IAAI,CAAClD,MAAM,EAAE;IACf;EACF;EAAC,QAAAwD,CAAA,G;qCArPUzF,WAAW,EAAA0F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXhG,WAAW;IAAAiG,OAAA,EAAXjG,WAAW,CAAAkG,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}