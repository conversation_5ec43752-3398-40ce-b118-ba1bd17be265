import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import {inject} from '@loopback/core';
import {authenticate, TokenService} from '@loopback/authentication';
import {
  TokenServiceBindings,
} from '@loopback/authentication-jwt';
import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {compare, hash} from 'bcryptjs';
import {User as UserModel} from '../models';
import {UserRepository as UserRepo} from '../repositories';
import {SecurityService, EmailService, SmsService, OAuthService} from '../services';

export class AuthController {
  constructor(
    @inject(TokenServiceBindings.TOKEN_SERVICE)
    public jwtService: TokenService,
    @inject(SecurityBindings.USER, {optional: true})
    public user: UserProfile,
    @repository(UserRepo) protected userRepository: UserRepo,
    @inject('services.SecurityService', {optional: true}) public securityService?: SecurityService,
    @inject('services.EmailService', {optional: true}) public emailService?: EmailService,
    @inject('services.SmsService', {optional: true}) public smsService?: SmsService,
    @inject('services.OAuthService', {optional: true}) public oauthService?: OAuthService,
  ) {}

  @post('/auth/signup')
  @response(200, {
    description: 'User registration',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
            userId: {type: 'string'},
          },
        },
      },
    },
  })
  async signUp(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['email', 'firstName', 'lastName', 'password'],
            properties: {
              email: {type: 'string', format: 'email'},
              firstName: {type: 'string', minLength: 2, maxLength: 50},
              lastName: {type: 'string', minLength: 2, maxLength: 50},
              phone: {type: 'string'},
              password: {type: 'string', minLength: 8},
            },
          },
        },
      },
    })
    newUserRequest: Omit<UserModel, 'id'> & {password: string},
  ): Promise<{message: string; userId: string}> {
    console.log('🔍 Signup started for email:', newUserRequest.email);
    console.log('🔍 Request data:', {
      email: newUserRequest.email,
      firstName: newUserRequest.firstName,
      lastName: newUserRequest.lastName,
      phone: newUserRequest.phone,
      hasPassword: !!newUserRequest.password
    });

    // Check if email is disposable (optional - warn but allow)
    try {
      if (this.securityService) {
        const isDisposable = await this.securityService.isDisposableEmail(newUserRequest.email);
        if (isDisposable) {
          console.log(`⚠️ Disposable email detected: ${newUserRequest.email}`);
          // Allow registration but log the warning
        }
      }
    } catch (error) {
      console.log('⚠️ Email validation service unavailable, skipping check');
    }

    console.log('🔍 Step 1: Email validation completed');

    // Validate password strength
    console.log('🔍 Step 2: Starting password validation');
    if (this.securityService) {
      const passwordValidation = await this.securityService.validatePasswordStrength(newUserRequest.password);
      if (!passwordValidation.isValid) {
        throw new HttpErrors.BadRequest(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }
      console.log('🔍 Step 2: Password validation passed');
    } else {
      // Basic password validation if service is not available
      if (newUserRequest.password.length < 8) {
        throw new HttpErrors.BadRequest('Password must be at least 8 characters long');
      }
      console.log('🔍 Step 2: Basic password validation passed');
    }

    // Check if user already exists
    console.log('🔍 Step 3: Checking if user exists');
    const foundUser = await this.userRepository.findOne({
      where: {email: newUserRequest.email},
    });

    if (foundUser) {
      console.log('❌ Step 3: User already exists');
      throw new HttpErrors.Conflict('Email already exists');
    }
    console.log('🔍 Step 3: User does not exist, proceeding');

    // Hash password
    let password: string;
    if (this.securityService) {
      password = await this.securityService.hashPassword(newUserRequest.password);
    } else {
      // Fallback to bcrypt directly
      const bcrypt = require('bcryptjs');
      password = await bcrypt.hash(newUserRequest.password, 12);
    }

    // Generate email verification token (optional)
    let emailVerificationToken;
    let emailVerificationExpires;
    try {
      if (this.securityService) {
        emailVerificationToken = await this.securityService.generateEmailVerificationToken();
        emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
      }
    } catch (error) {
      console.log('⚠️ Email verification token generation failed, proceeding without it');
    }

    // Create user
    const savedUser = await this.userRepository.create({
      email: newUserRequest.email,
      firstName: newUserRequest.firstName,
      lastName: newUserRequest.lastName,
      phone: newUserRequest.phone,
      password: password,
      emailVerificationToken,
      emailVerificationExpires,
      emailVerified: true, // Set to true by default to allow immediate login
      roles: ['user'],
    });

    // Send verification email (optional)
    try {
      if (emailVerificationToken && this.emailService) {
        await this.emailService.sendVerificationEmail(savedUser.email, emailVerificationToken);
        console.log(`📧 Verification email sent to ${savedUser.email}`);
      }
    } catch (error) {
      console.error('Failed to send verification email:', error);
      // Don't fail registration if email sending fails
    }

    return {
      message: 'User registered successfully. You can now log in.',
      userId: savedUser.id,
    };
  }

  @post('/auth/login')
  @response(200, {
    description: 'Token',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            token: {type: 'string'},
            user: getModelSchemaRef(UserModel, {exclude: ['password']}),
            requiresTwoFactor: {type: 'boolean'},
          },
        },
      },
    },
  })
  async login(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['email', 'password'],
            properties: {
              email: {type: 'string', format: 'email'},
              password: {type: 'string', minLength: 8},
              twoFactorToken: {type: 'string'},
            },
          },
        },
      },
    })
    credentials: {email: string; password: string; twoFactorToken?: string},
  ): Promise<{token: string; user: UserModel; requiresTwoFactor?: boolean}> {
    // Verify credentials manually
    const user = await this.verifyCredentials(credentials);

    // Check if email is verified (optional - warn but allow login)
    if (!user.emailVerified) {
      console.log(`⚠️ User ${user.email} logging in with unverified email`);
      // Allow login but could add a flag to the response
    }

    // Check if 2FA is enabled
    if (user.twoFactorEnabled) {
      if (!credentials.twoFactorToken) {
        return {
          token: '',
          user,
          requiresTwoFactor: true,
        };
      }

      // Verify 2FA token
      if (this.securityService) {
        const isValid2FA = await this.securityService.verifyTwoFactorToken(user.id, credentials.twoFactorToken);
        if (!isValid2FA) {
          throw new HttpErrors.Unauthorized('Invalid two-factor authentication token');
        }
      } else {
        throw new HttpErrors.Unauthorized('Two-factor authentication service unavailable');
      }
    }

    // Convert a User object into a UserProfile object (reduced set of properties)
    const userProfile = this.convertToUserProfile(user);

    // Create a JSON Web Token based on the user profile
    const token = await this.jwtService.generateToken(userProfile);

    return {token, user};
  }

  @post('/auth/verify-email')
  @response(200, {
    description: 'Email verification',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async verifyEmail(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['token'],
            properties: {
              token: {type: 'string'},
            },
          },
        },
      },
    })
    request: {token: string},
  ): Promise<{message: string}> {
    const user = await this.userRepository.findOne({
      where: {
        emailVerificationToken: request.token,
        emailVerificationExpires: {gt: new Date()},
      },
    });

    if (!user) {
      throw new HttpErrors.BadRequest('Invalid or expired verification token');
    }

    await this.userRepository.updateById(user.id, {
      emailVerified: true,
      emailVerificationToken: undefined,
      emailVerificationExpires: undefined,
      updatedAt: new Date(),
    });

    return {message: 'Email verified successfully'};
  }

  @post('/auth/forgot-password')
  @response(200, {
    description: 'Password reset request',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async forgotPassword(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              email: {type: 'string', format: 'email'},
              phone: {type: 'string'},
            },
            anyOf: [
              {required: ['email']},
              {required: ['phone']}
            ],
          },
        },
      },
    })
    request: {email?: string; phone?: string},
  ): Promise<{message: string}> {
    if (!request.email && !request.phone) {
      throw new HttpErrors.BadRequest('Either email or phone is required');
    }

    let user: any = null;

    if (request.email) {
      user = await this.userRepository.findOne({
        where: {email: request.email},
      });
    } else if (request.phone) {
      user = await this.userRepository.findOne({
        where: {phone: request.phone},
      });
    }

    if (!user) {
      // Don't reveal if email/phone exists or not
      return {message: 'If the email/phone exists, a password reset link has been sent'};
    }

    if (!this.securityService) {
      throw new HttpErrors.ServiceUnavailable('Security service is not available');
    }
    const resetToken = await this.securityService.generatePasswordResetToken();
    const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    await this.userRepository.updateById(user.id, {
      passwordResetToken: resetToken,
      passwordResetExpires: resetExpires,
      updatedAt: new Date(),
    });

    try {
      if (this.emailService) {
        await this.emailService.sendPasswordResetEmail(user.email, resetToken);
      }
    } catch (error) {
      console.error('Failed to send password reset email:', error);
    }

    return {message: 'If the email exists, a password reset link has been sent'};
  }

  @post('/auth/reset-password')
  @response(200, {
    description: 'Password reset',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async resetPassword(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['token', 'password'],
            properties: {
              token: {type: 'string'},
              password: {type: 'string', minLength: 8},
            },
          },
        },
      },
    })
    request: {token: string; password: string},
  ): Promise<{message: string}> {
    const user = await this.userRepository.findOne({
      where: {
        passwordResetToken: request.token,
        passwordResetExpires: {gt: new Date()},
      },
    });

    if (!user) {
      throw new HttpErrors.BadRequest('Invalid or expired reset token');
    }

    // Validate password strength
    if (!this.securityService) {
      throw new HttpErrors.ServiceUnavailable('Security service is not available');
    }

    const passwordValidation = await this.securityService.validatePasswordStrength(request.password);
    if (!passwordValidation.isValid) {
      throw new HttpErrors.BadRequest(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
    }

    // Hash new password
    const hashedPassword = await this.securityService.hashPassword(request.password);

    // Update password
    await this.userRepository.updateById(user.id, {
      password: hashedPassword,
      updatedAt: new Date(),
    });

    // Clear reset token
    await this.userRepository.updateById(user.id, {
      passwordResetToken: undefined,
      passwordResetExpires: undefined,
      updatedAt: new Date(),
    });

    return {message: 'Password reset successfully'};
  }

  // Helper methods for authentication
  private async verifyCredentials(credentials: {email: string; password: string}): Promise<UserModel> {
    const invalidCredentialsError = 'Invalid email or password.';

    const foundUser = await this.userRepository.findOne({
      where: {email: credentials.email},
    });
    if (!foundUser) {
      throw new HttpErrors.Unauthorized(invalidCredentialsError);
    }

    // Check if user is active
    if (!foundUser.isActive) {
      throw new HttpErrors.Unauthorized('Account is deactivated.');
    }

    // Check if account is locked
    if (foundUser.lockUntil && foundUser.lockUntil > new Date()) {
      throw new HttpErrors.Unauthorized('Account is temporarily locked. Please try again later.');
    }

    if (!foundUser.password) {
      throw new HttpErrors.Unauthorized(invalidCredentialsError);
    }

    const passwordMatched = await compare(
      credentials.password,
      foundUser.password,
    );

    if (!passwordMatched) {
      // Increment login attempts
      await this.handleFailedLogin(foundUser);
      throw new HttpErrors.Unauthorized(invalidCredentialsError);
    }

    // Reset login attempts on successful login
    await this.handleSuccessfulLogin(foundUser);

    return foundUser;
  }

  private convertToUserProfile(user: UserModel): UserProfile {
    return {
      [securityId]: user.id.toString(),
      name: `${user.firstName} ${user.lastName}`,
      id: user.id,
      email: user.email,
      roles: user.roles,
    };
  }

  private async handleFailedLogin(user: UserModel): Promise<void> {
    const maxAttempts = 5;
    const lockTime = 30 * 60 * 1000; // 30 minutes

    const attempts = (user.loginAttempts || 0) + 1;
    const updateData: Partial<UserModel> = {
      loginAttempts: attempts,
      updatedAt: new Date(),
    };

    if (attempts >= maxAttempts) {
      updateData.lockUntil = new Date(Date.now() + lockTime);
    }

    await this.userRepository.updateById(user.id, updateData);
  }

  private async handleSuccessfulLogin(user: UserModel): Promise<void> {
    await this.userRepository.updateById(user.id, {
      loginAttempts: 0,
      lockUntil: undefined,
      lastLoginAt: new Date(),
      updatedAt: new Date(),
    });
  }

  @post('/auth/change-password')
  @authenticate('jwt')
  @response(200, {
    description: 'Change password',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async changePassword(
    @inject(SecurityBindings.USER) currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['currentPassword', 'newPassword'],
            properties: {
              currentPassword: {type: 'string'},
              newPassword: {type: 'string'},
              twoFactorToken: {type: 'string'},
            },
          },
        },
      },
    })
    request: {currentPassword: string; newPassword: string; twoFactorToken?: string},
  ): Promise<{message: string}> {
    const userId = currentUserProfile[securityId];
    const user = await this.userRepository.findById(userId);

    if (!user) {
      throw new HttpErrors.NotFound('User not found');
    }

    // Verify current password
    if (!user.password) {
      throw new HttpErrors.BadRequest('User has no password set');
    }

    const isCurrentPasswordValid = await compare(request.currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new HttpErrors.BadRequest('Current password is incorrect');
    }

    // Check if 2FA is enabled and token is required
    if (user.twoFactorEnabled) {
      if (!request.twoFactorToken) {
        throw new HttpErrors.BadRequest('Two-factor authentication token is required');
      }

      // Verify 2FA token
      const speakeasy = require('speakeasy');
      const verified = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: 'base32',
        token: request.twoFactorToken,
        window: 2,
      });

      if (!verified) {
        throw new HttpErrors.BadRequest('Invalid two-factor authentication token');
      }
    }

    // Validate new password strength
    if (this.securityService) {
      const validation = await this.securityService.validatePasswordStrength(request.newPassword);
      if (!validation.isValid) {
        throw new HttpErrors.BadRequest(`Password validation failed: ${validation.errors.join(', ')}`);
      }
    }

    // Hash new password
    const hashedNewPassword = await hash(request.newPassword, 12);

    // Update password
    await this.userRepository.updateById(userId, {
      password: hashedNewPassword,
      updatedAt: new Date(),
    });

    return {message: 'Password changed successfully'};
  }

  @get('/auth/oauth/{provider}/url')
  @response(200, {
    description: 'Get OAuth authorization URL',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            url: {type: 'string'},
            state: {type: 'string'},
          },
        },
      },
    },
  })
  async getOAuthUrl(
    @param.path.string('provider') provider: string,
  ): Promise<{url: string; state?: string}> {
    if (!['google', 'github', 'microsoft'].includes(provider)) {
      throw new HttpErrors.BadRequest('Unsupported OAuth provider');
    }

    if (!this.oauthService) {
      throw new HttpErrors.ServiceUnavailable('OAuth service is not available');
    }

    const state = Math.random().toString(36).substring(2, 15);
    const url = this.oauthService.generateOAuthUrl(provider, state);

    return {url, state};
  }

  @post('/auth/oauth/{provider}/callback')
  @response(200, {
    description: 'Handle OAuth callback',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            token: {type: 'string'},
            user: {type: 'object'},
            isNewUser: {type: 'boolean'},
          },
        },
      },
    },
  })
  async handleOAuthCallback(
    @param.path.string('provider') provider: string,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['code'],
            properties: {
              code: {type: 'string'},
              state: {type: 'string'},
            },
          },
        },
      },
    })
    request: {code: string; state?: string},
  ): Promise<{token: string; user: any; isNewUser: boolean}> {
    if (!['google', 'github', 'microsoft'].includes(provider)) {
      throw new HttpErrors.BadRequest('Unsupported OAuth provider');
    }

    if (!this.oauthService) {
      throw new HttpErrors.ServiceUnavailable('OAuth service is not available');
    }

    // Exchange code for access token
    const accessToken = await this.oauthService.exchangeCodeForToken(provider, request.code);

    // Get user info from OAuth provider
    let oauthUserData;
    switch (provider) {
      case 'google':
        oauthUserData = await this.oauthService.verifyGoogleToken(accessToken);
        break;
      case 'github':
        oauthUserData = await this.oauthService.verifyGitHubToken(accessToken);
        break;
      case 'microsoft':
        oauthUserData = await this.oauthService.verifyMicrosoftToken(accessToken);
        break;
      default:
        throw new HttpErrors.BadRequest('Unsupported OAuth provider');
    }

    // Find or create user
    const user = await this.oauthService.findOrCreateOAuthUser(provider, oauthUserData);
    const isNewUser = !user.createdAt || (new Date().getTime() - new Date(user.createdAt).getTime()) < 60000; // Created within last minute

    // Generate JWT token
    const userProfile = this.convertToUserProfile(user);
    const token = await this.jwtService.generateToken(userProfile);

    return {
      token,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        avatarUrl: user.avatarUrl,
        oauthProvider: user.oauthProvider,
        roles: user.roles,
      },
      isNewUser,
    };
  }
}
