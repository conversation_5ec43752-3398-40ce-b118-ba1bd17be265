{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nlet MatRippleModule = /*#__PURE__*/(() => {\n  class MatRippleModule {\n    static ɵfac = function MatRippleModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRippleModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatRippleModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n  return MatRippleModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatRippleModule as M };\n//# sourceMappingURL=index-BFRo2fUq.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}