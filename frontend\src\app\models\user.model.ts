export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  twoFactorEnabled: boolean;
  roles: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  // OAuth fields
  googleId?: string;
  githubId?: string;
  microsoftId?: string;
  oauthProvider?: string;
  avatarUrl?: string;
}

export interface UserRegistration {
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  password: string;
  confirmPassword: string;
}

export interface UserLogin {
  email: string;
  password: string;
  twoFactorToken?: string;
}

export interface LoginResponse {
  token: string;
  user: User;
  requiresTwoFactor?: boolean;
}

export interface PasswordReset {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface PasswordChange {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface TwoFactorSetup {
  secret: string;
  qrCode: string;
}

export interface OTPRequest {
  identifier: string; // email or phone
  type: 'login' | 'verification' | '2fa';
}

export interface OTPVerification {
  identifier: string;
  code: string;
  type: 'login' | 'verification' | '2fa';
}

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
}

// OAuth interfaces
export interface OAuthProvider {
  name: 'google' | 'github' | 'microsoft';
  displayName: string;
  icon: string;
  color: string;
}

export interface OAuthUrlResponse {
  url: string;
}

export interface OAuthCallbackRequest {
  code: string;
  state?: string;
}

// Change password interface
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  twoFactorToken?: string;
}
