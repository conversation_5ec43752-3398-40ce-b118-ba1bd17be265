import { TokenService } from '@loopback/authentication';
import { UserRepository } from '../repositories';
import { OAuthService } from '../services';
export declare class OAuthController {
    jwtService: TokenService;
    protected userRepository: UserRepository;
    oauthService: OAuthService;
    constructor(jwtService: TokenService, userRepository: UserRepository, oauthService: OAuthService);
    getOAuthUrl(provider: string): Promise<{
        url: string;
        state: string;
    }>;
    handleOAuthCallback(provider: string, request: {
        code: string;
        state?: string;
    }): Promise<{
        token: string;
        user: any;
        isNewUser: boolean;
    }>;
    disconnectOAuth(provider: string, request: {
        userId: string;
    }): Promise<{
        message: string;
    }>;
}
