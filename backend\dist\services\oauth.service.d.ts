import { UserRepository } from '../repositories';
import { User } from '../models';
export declare class OAuthService {
    protected userRepository: UserRepository;
    private googleClient;
    constructor(userRepository: UserRepository);
    verifyGoogleToken(token: string): Promise<any>;
    verifyGitHubToken(accessToken: string): Promise<any>;
    verifyMicrosoftToken(accessToken: string): Promise<any>;
    findOrCreateOAuthUser(provider: string, oauthData: any): Promise<User>;
    generateOAuthUrl(provider: string, state?: string): string;
    exchangeCodeForToken(provider: string, code: string): Promise<string>;
}
