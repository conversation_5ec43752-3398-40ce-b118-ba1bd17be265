# Secure Backend Authentication System

A comprehensive, production-ready authentication system built with LoopBack 4, featuring 2FA, OAuth integration, and Brevo email service.

## 🚀 Features

### 🔐 Authentication
- **JWT-based authentication** with secure token handling
- **Password hashing** using bcrypt with configurable rounds
- **Email verification** with secure token-based verification
- **Password reset** functionality with time-limited tokens
- **Account lockout** after failed login attempts
- **Change password** with 2FA verification

### 🔒 Two-Factor Authentication (2FA)
- **TOTP (Time-based One-Time Password)** compatible with:
  - Google Authenticator
  - Microsoft Authenticator
  - Authy (by <PERSON><PERSON><PERSON>)
  - Any RFC 6238 TOTP app
- **Email OTP** for 2FA verification
- **SMS OTP** for 2FA verification (demo mode)
- **QR Code generation** for easy authenticator app setup

### 🌐 OAuth Integration
- **Google OAuth 2.0** - Complete integration
- **GitHub OAuth** - User profile and email access
- **Microsoft OAuth** - Microsoft Graph API integration
- **Unified OAuth handling** with automatic user creation/linking

### 📧 Email Service (Brevo Integration)
- **Brevo REST API** integration for production email sending
- **Fallback console logging** for development
- **HTML email templates** for all email types:
  - Email verification
  - Password reset
  - 2FA OTP codes
  - Login OTP codes

### 🛡️ Security Features
- **CORS configuration** for frontend integration
- **Rate limiting** to prevent abuse
- **Input validation** and sanitization
- **Secure headers** with Helmet.js
- **Environment-based configuration**
- **SQL injection prevention**
- **XSS protection**

## 📋 Prerequisites

- Node.js 18+ or 20+
- PostgreSQL (or use in-memory database for development)
- Brevo account (optional, for production email)
- OAuth app credentials (optional, for OAuth login)

## 🔧 Installation

1. **Clone and install dependencies:**
```bash
cd backend
npm install
```

2. **Configure environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Build the project:**
```bash
npm run build
```

4. **Start the server:**
```bash
npm start
# or for development with auto-reload:
npm run dev
```

## ⚙️ Configuration

### Required Environment Variables
```env
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
DATABASE_URL=postgresql://user:password@localhost:5432/database
FRONTEND_URL=http://localhost:4200
```

### Optional Environment Variables
```env
# Brevo Email Service
BREVO_API_KEY=your_brevo_api_key

# OAuth Providers
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret

# SMS Service (Twilio)
TWILIO_SID=your_twilio_sid
TWILIO_TOKEN=your_twilio_token
TWILIO_PHONE=+**********
```

## 🧪 Testing

### Run All Tests
```bash
node scripts/test-all.js
```

### Individual Test Suites
```bash
# Authentication tests
node scripts/test-auth.js

# Email functionality tests
node scripts/test-email.js

# 2FA functionality tests
node scripts/test-2fa.js

# Complete system demonstration
node scripts/demo-complete-system.js
```

## 📚 API Documentation

### Authentication Endpoints

#### POST /auth/signup
Register a new user
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+**********"
}
```

#### POST /auth/login
Login with email and password
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "twoFactorToken": "123456" // Optional, required if 2FA is enabled
}
```

#### POST /auth/change-password
Change user password (requires authentication)
```json
{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewPassword456!",
  "twoFactorToken": "123456" // Required if 2FA is enabled
}
```

### Two-Factor Authentication Endpoints

#### POST /2fa/setup
Setup 2FA for authenticated user
```json
Response: {
  "secret": "base32-encoded-secret",
  "qrCode": "data:image/png;base64,..."
}
```

#### POST /2fa/verify
Enable 2FA with verification token
```json
{
  "token": "123456"
}
```

#### POST /2fa/send-email
Send 2FA code via email
```json
Response: {
  "message": "Email sent successfully"
}
```

### OAuth Endpoints

#### GET /auth/oauth/{provider}/url
Get OAuth authorization URL for provider (google, github, microsoft)
```json
Response: {
  "url": "https://accounts.google.com/o/oauth2/v2/auth?..."
}
```

#### POST /auth/oauth/{provider}/callback
Handle OAuth callback
```json
{
  "code": "authorization_code_from_provider"
}
```

## 🔧 Brevo Email Setup

1. **Sign up for Brevo:**
   - Visit https://www.brevo.com/
   - Create a free account

2. **Get API Key:**
   - Go to https://app.brevo.com/settings/keys/api
   - Create a new API key
   - Copy the key

3. **Configure in .env:**
   ```env
   BREVO_API_KEY=your_actual_brevo_api_key
   EMAIL_FROM=<EMAIL>
   EMAIL_FROM_NAME=Your App Name
   ```

4. **Restart the server:**
   ```bash
   npm start
   ```

## 🔑 OAuth Setup

### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add redirect URI: `http://localhost:3003/auth/oauth/callback`

### GitHub OAuth
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App
3. Set Authorization callback URL: `http://localhost:3003/auth/oauth/callback`

### Microsoft OAuth
1. Go to [Azure Portal](https://portal.azure.com/)
2. Register a new application
3. Add redirect URI: `http://localhost:3003/auth/oauth/callback`

## 🏗️ Architecture

### Modular Design
- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic and external integrations
- **Models**: Data structure definitions
- **Repositories**: Database access layer
- **Middleware**: Security and request processing

### Security Layers
1. **Input Validation**: Joi schema validation
2. **Authentication**: JWT token verification
3. **Authorization**: Role-based access control
4. **Rate Limiting**: Prevent abuse and DoS attacks
5. **CORS**: Cross-origin request security
6. **Headers**: Security headers with Helmet.js

## 🚀 Production Deployment

### Security Checklist
- [ ] Use strong JWT secrets (256-bit minimum)
- [ ] Configure HTTPS with valid SSL certificates
- [ ] Set up production database with connection pooling
- [ ] Configure real email service (Brevo)
- [ ] Set up SMS service (Twilio)
- [ ] Configure OAuth applications with production URLs
- [ ] Enable rate limiting and monitoring
- [ ] Set up logging and error tracking
- [ ] Configure backup and disaster recovery

### Environment Configuration
```env
NODE_ENV=production
PORT=443
JWT_SECRET=your-production-jwt-secret-256-bits-minimum
DATABASE_URL=***************************************/database
FRONTEND_URL=https://yourdomain.com
BREVO_API_KEY=your-production-brevo-key
```

## 📊 Monitoring and Logging

The system includes comprehensive logging for:
- Authentication attempts
- 2FA operations
- Email sending
- OAuth flows
- Security events
- Error tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For support and questions:
- Check the test scripts for usage examples
- Review the API documentation at `/explorer`
- Run the demo script for a complete walkthrough
- Check server logs for detailed error information

---

**🎉 This system is production-ready and includes all modern security best practices!**
