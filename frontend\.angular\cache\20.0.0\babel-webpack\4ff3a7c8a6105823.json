{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"../../../services/oauth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction LoginComponent_form_12_mat_spinner_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 25);\n  }\n}\nfunction LoginComponent_form_12_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_12_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_12_button_33_Template_button_click_0_listener() {\n      const provider_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loginWithOAuth(provider_r4.name));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const provider_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"border-color\", provider_r4.color);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(provider_r4.icon);\n    i0.ɵɵstyleProp(\"color\", provider_r4.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(provider_r4.displayName);\n  }\n}\nfunction LoginComponent_form_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 7);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_12_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"mat-form-field\", 8)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 9);\n    i0.ɵɵelementStart(5, \"mat-icon\", 10);\n    i0.ɵɵtext(6, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 8)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 11);\n    i0.ɵɵelementStart(13, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_12_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hidePassword = !ctx_r1.hidePassword);\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-error\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 13);\n    i0.ɵɵtemplate(19, LoginComponent_form_12_mat_spinner_19_Template, 1, 0, \"mat-spinner\", 14)(20, LoginComponent_form_12_span_20_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 16)(22, \"span\");\n    i0.ɵɵtext(23, \"or\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_12_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Login with OTP \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 18)(29, \"div\", 16)(30, \"span\");\n    i0.ɵɵtext(31, \"or continue with\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 19);\n    i0.ɵɵtemplate(33, LoginComponent_form_12_button_33_Template, 4, 8, \"button\", 20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 21)(35, \"a\", 22);\n    i0.ɵɵtext(36, \"Forgot Password?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 23)(38, \"span\");\n    i0.ɵɵtext(39, \"Don't have an account? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"a\", 24);\n    i0.ɵɵtext(41, \"Sign Up\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.loginForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r1.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"password\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.oauthProviders);\n  }\n}\nfunction LoginComponent_form_13_mat_spinner_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 25);\n  }\n}\nfunction LoginComponent_form_13_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 7);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_13_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTwoFactorSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"mat-icon\", 28);\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Two-Factor Authentication\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 8)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Authentication Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 29);\n    i0.ɵɵelementStart(12, \"mat-icon\", 10);\n    i0.ɵɵtext(13, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-error\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"button\", 13);\n    i0.ɵɵtemplate(17, LoginComponent_form_13_mat_spinner_17_Template, 1, 0, \"mat-spinner\", 14)(18, LoginComponent_form_13_span_18_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_13_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.backToLogin());\n    });\n    i0.ɵɵelementStart(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.twoFactorForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.twoFactorForm, \"twoFactorToken\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_14_button_16_mat_spinner_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 25);\n  }\n}\nfunction LoginComponent_form_14_button_16_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Send OTP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_14_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵtemplate(1, LoginComponent_form_14_button_16_mat_spinner_1_Template, 1, 0, \"mat-spinner\", 14)(2, LoginComponent_form_14_button_16_span_2_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_14_div_17_mat_spinner_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 25);\n  }\n}\nfunction LoginComponent_form_14_div_17_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_14_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 8)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Enter OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 35);\n    i0.ɵɵelementStart(5, \"mat-icon\", 10);\n    i0.ɵɵtext(6, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_div_17_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loginWithOTP());\n    });\n    i0.ɵɵtemplate(10, LoginComponent_form_14_div_17_mat_spinner_10_Template, 1, 0, \"mat-spinner\", 14)(11, LoginComponent_form_14_div_17_span_11_Template, 2, 0, \"span\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_div_17_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Resend OTP \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"code\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 31)(1, \"div\", 27)(2, \"mat-icon\", 28);\n    i0.ɵɵtext(3, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Login with OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Enter your email or phone number to receive a one-time password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 8)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Email or Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 32);\n    i0.ɵɵelementStart(12, \"mat-icon\", 10);\n    i0.ɵɵtext(13, \"contact_mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-error\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, LoginComponent_form_14_button_16_Template, 3, 3, \"button\", 33)(17, LoginComponent_form_14_div_17_Template, 16, 4, \"div\", 15);\n    i0.ɵɵelementStart(18, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.otpForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"identifier\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.otpSent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpSent);\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, oauthService, router, route, snackBar) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.oauthService = oauthService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.loading = false;\n    this.hidePassword = true;\n    this.showOTPLogin = false;\n    this.showTwoFactor = false;\n    this.otpSent = false;\n    this.returnUrl = '';\n    this.oauthProviders = [];\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]]\n    });\n    this.otpForm = this.formBuilder.group({\n      identifier: ['', [Validators.required]],\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n    this.twoFactorForm = this.formBuilder.group({\n      twoFactorToken: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n  ngOnInit() {\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    this.oauthProviders = this.oauthService.getAvailableProviders();\n    if (this.authService.isAuthenticated) {\n      this.router.navigate([this.returnUrl]);\n    }\n    const message = this.route.snapshot.queryParams['message'];\n    if (message) {\n      this.snackBar.open(message, 'Close', {\n        duration: 5000\n      });\n    }\n    // Handle OAuth callback\n    const code = this.route.snapshot.queryParams['code'];\n    const state = this.route.snapshot.queryParams['state'];\n    if (code) {\n      this.handleOAuthCallback(code, state);\n    }\n  }\n  onSubmit() {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched(this.loginForm);\n      return;\n    }\n    this.loading = true;\n    const credentials = this.loginForm.value;\n    this.authService.login(credentials).subscribe({\n      next: response => {\n        if (response.requiresTwoFactor) {\n          this.showTwoFactor = true;\n          this.snackBar.open('Please enter your two-factor authentication code', 'Close', {\n            duration: 5000\n          });\n        } else {\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n        }\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Login failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  onTwoFactorSubmit() {\n    if (this.twoFactorForm.invalid) {\n      this.markFormGroupTouched(this.twoFactorForm);\n      return;\n    }\n    this.loading = true;\n    const credentials = {\n      ...this.loginForm.value,\n      twoFactorToken: this.twoFactorForm.value.twoFactorToken\n    };\n    this.authService.login(credentials).subscribe({\n      next: response => {\n        this.snackBar.open('Login successful!', 'Close', {\n          duration: 3000\n        });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Two-factor authentication failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  sendOTP() {\n    const identifier = this.otpForm.get('identifier')?.value;\n    if (!identifier) {\n      this.snackBar.open('Please enter email or phone number', 'Close', {\n        duration: 3000\n      });\n      return;\n    }\n    this.loading = true;\n    const request = {\n      identifier,\n      type: 'login'\n    };\n    this.authService.sendOTP(request).subscribe({\n      next: () => {\n        this.otpSent = true;\n        this.snackBar.open('OTP sent successfully!', 'Close', {\n          duration: 3000\n        });\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'Failed to send OTP', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  loginWithOTP() {\n    if (this.otpForm.invalid) {\n      this.markFormGroupTouched(this.otpForm);\n      return;\n    }\n    this.loading = true;\n    const {\n      identifier,\n      code\n    } = this.otpForm.value;\n    this.authService.loginWithOTP(identifier, code).subscribe({\n      next: () => {\n        this.snackBar.open('Login successful!', 'Close', {\n          duration: 3000\n        });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'OTP login failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n  toggleOTPLogin() {\n    this.showOTPLogin = !this.showOTPLogin;\n    this.showTwoFactor = false;\n    this.otpSent = false;\n    this.otpForm.reset();\n  }\n  backToLogin() {\n    this.showTwoFactor = false;\n    this.showOTPLogin = false;\n    this.otpSent = false;\n  }\n  // OAuth methods\n  loginWithOAuth(provider) {\n    this.loading = true;\n    this.oauthService.initiateOAuthLogin(provider);\n  }\n  handleOAuthCallback(code, state) {\n    this.loading = true;\n    this.oauthService.handleOAuthCallback(code, state).subscribe({\n      next: response => {\n        this.snackBar.open('OAuth login successful!', 'Close', {\n          duration: 3000\n        });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: error => {\n        this.snackBar.open(error.message || 'OAuth login failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n        // Remove OAuth parameters from URL\n        this.router.navigate([], {\n          relativeTo: this.route,\n          queryParams: {},\n          replaceUrl: true\n        });\n      }\n    });\n  }\n  getFieldError(form, fieldName) {\n    const field = form.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['email']) return 'Please enter a valid email';\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['pattern']) return 'Please enter a valid format';\n    }\n    return '';\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    standalone: false,\n    decls: 15,\n    vars: 3,\n    consts: [[1, \"auth-container\"], [1, \"auth-card\", \"fade-in\"], [1, \"auth-header\"], [1, \"security-badge\"], [1, \"auth-content\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"divider\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"w-100\", 3, \"click\"], [1, \"oauth-section\", \"mt-3\"], [1, \"oauth-buttons\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", \"class\", \"oauth-button\", 3, \"border-color\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"text-primary\"], [1, \"text-center\", \"mt-2\"], [\"routerLink\", \"/auth/register\", 1, \"text-primary\"], [\"diameter\", \"20\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"oauth-button\", 3, \"click\", \"disabled\"], [1, \"text-center\", \"mb-3\"], [\"color\", \"primary\", 2, \"font-size\", \"48px\", \"width\", \"48px\", \"height\", \"48px\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"w-100\", \"mt-2\", 3, \"click\"], [3, \"formGroup\"], [\"matInput\", \"\", \"formControlName\", \"identifier\", \"placeholder\", \"<EMAIL> or +1234567890\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", \"class\", \"submit-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n        i0.ɵɵtext(4, \"Welcome Back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"Sign in to your secure account\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n        i0.ɵɵtext(9, \"security\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(10, \" Secure Login \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 4);\n        i0.ɵɵtemplate(12, LoginComponent_form_12_Template, 42, 9, \"form\", 5)(13, LoginComponent_form_13_Template, 23, 5, \"form\", 5)(14, LoginComponent_form_14_Template, 22, 4, \"form\", 6);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngIf\", !ctx.showOTPLogin && !ctx.showTwoFactor);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showTwoFactor);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showOTPLogin);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i4.RouterLink, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i9.MatIconButton, i10.MatIcon, i11.MatProgressSpinner],\n    styles: [\".w-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.mt-2[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem;\\n}\\n\\n.mt-3[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n}\\n\\n.mb-3[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #3f51b5;\\n  text-decoration: none;\\n}\\n.text-primary[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  position: relative;\\n  text-align: center;\\n  margin: 1.5rem 0;\\n}\\n.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: #e0e0e0;\\n}\\n.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 0 1rem;\\n  color: #666;\\n  font-size: 0.875rem;\\n}\\n\\n.oauth-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.75rem;\\n  padding: 0.75rem 1rem;\\n  width: 100%;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "LoginComponent_form_12_button_33_Template_button_click_0_listener", "provider_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "loginWithOAuth", "name", "ɵɵstyleProp", "color", "ɵɵproperty", "loading", "ɵɵadvance", "ɵɵclassMap", "icon", "ɵɵtextInterpolate", "displayName", "LoginComponent_form_12_Template_form_ngSubmit_0_listener", "_r1", "onSubmit", "LoginComponent_form_12_Template_button_click_13_listener", "hidePassword", "ɵɵtemplate", "LoginComponent_form_12_mat_spinner_19_Template", "LoginComponent_form_12_span_20_Template", "LoginComponent_form_12_Template_button_click_24_listener", "toggleOTPLogin", "LoginComponent_form_12_button_33_Template", "loginForm", "getFieldError", "oauthProviders", "LoginComponent_form_13_Template_form_ngSubmit_0_listener", "_r5", "onTwoFactorSubmit", "LoginComponent_form_13_mat_spinner_17_Template", "LoginComponent_form_13_span_18_Template", "LoginComponent_form_13_Template_button_click_19_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twoFactorForm", "LoginComponent_form_14_button_16_Template_button_click_0_listener", "_r7", "sendOTP", "LoginComponent_form_14_button_16_mat_spinner_1_Template", "LoginComponent_form_14_button_16_span_2_Template", "LoginComponent_form_14_div_17_Template_button_click_9_listener", "_r8", "loginWithOTP", "LoginComponent_form_14_div_17_mat_spinner_10_Template", "LoginComponent_form_14_div_17_span_11_Template", "LoginComponent_form_14_div_17_Template_button_click_12_listener", "otpForm", "LoginComponent_form_14_button_16_Template", "LoginComponent_form_14_div_17_Template", "LoginComponent_form_14_Template_button_click_18_listener", "_r6", "otpSent", "LoginComponent", "constructor", "formBuilder", "authService", "oauthService", "router", "route", "snackBar", "showOTPLogin", "showTwoFactor", "returnUrl", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "identifier", "code", "pattern", "twoFactorToken", "ngOnInit", "snapshot", "queryParams", "getAvailableProviders", "isAuthenticated", "navigate", "message", "open", "duration", "state", "handleOAuthCallback", "invalid", "markFormGroupTouched", "credentials", "value", "login", "subscribe", "next", "response", "requiresTwoFactor", "error", "get", "request", "type", "reset", "provider", "initiateOAuthLogin", "relativeTo", "replaceUrl", "form", "fieldName", "field", "errors", "touched", "<PERSON><PERSON><PERSON><PERSON>", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "OAuthService", "i4", "Router", "ActivatedRoute", "i5", "MatSnackBar", "_2", "selectors", "standalone", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "LoginComponent_form_12_Template", "LoginComponent_form_13_Template", "LoginComponent_form_14_Template"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\login\\login.component.ts", "C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../../services/auth.service';\nimport { OAuthService } from '../../../services/oauth.service';\nimport { UserLogin, OTPRequest, OAuthProvider } from '../../../models/user.model';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss'],\n  standalone: false\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  otpForm: FormGroup;\n  twoFactorForm: FormGroup;\n  \n  loading = false;\n  hidePassword = true;\n  showOTPLogin = false;\n  showTwoFactor = false;\n  otpSent = false;\n  returnUrl = '';\n  oauthProviders: OAuthProvider[] = [];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private oauthService: OAuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8)]]\n    });\n\n    this.otpForm = this.formBuilder.group({\n      identifier: ['', [Validators.required]],\n      code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n\n    this.twoFactorForm = this.formBuilder.group({\n      twoFactorToken: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n\n  ngOnInit(): void {\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    this.oauthProviders = this.oauthService.getAvailableProviders();\n\n    if (this.authService.isAuthenticated) {\n      this.router.navigate([this.returnUrl]);\n    }\n\n    const message = this.route.snapshot.queryParams['message'];\n    if (message) {\n      this.snackBar.open(message, 'Close', { duration: 5000 });\n    }\n\n    // Handle OAuth callback\n    const code = this.route.snapshot.queryParams['code'];\n    const state = this.route.snapshot.queryParams['state'];\n    if (code) {\n      this.handleOAuthCallback(code, state);\n    }\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched(this.loginForm);\n      return;\n    }\n\n    this.loading = true;\n    const credentials: UserLogin = this.loginForm.value;\n\n    this.authService.login(credentials).subscribe({\n      next: (response) => {\n        if (response.requiresTwoFactor) {\n          this.showTwoFactor = true;\n          this.snackBar.open('Please enter your two-factor authentication code', 'Close', {\n            duration: 5000\n          });\n        } else {\n          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n          this.router.navigate([this.returnUrl]);\n        }\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  onTwoFactorSubmit(): void {\n    if (this.twoFactorForm.invalid) {\n      this.markFormGroupTouched(this.twoFactorForm);\n      return;\n    }\n\n    this.loading = true;\n    const credentials: UserLogin = {\n      ...this.loginForm.value,\n      twoFactorToken: this.twoFactorForm.value.twoFactorToken\n    };\n\n    this.authService.login(credentials).subscribe({\n      next: (response) => {\n        this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Two-factor authentication failed', 'Close', {\n          duration: 5000\n        });\n        this.loading = false;\n      }\n    });\n  }\n\n  sendOTP(): void {\n    const identifier = this.otpForm.get('identifier')?.value;\n    if (!identifier) {\n      this.snackBar.open('Please enter email or phone number', 'Close', { duration: 3000 });\n      return;\n    }\n\n    this.loading = true;\n    const request: OTPRequest = {\n      identifier,\n      type: 'login'\n    };\n\n    this.authService.sendOTP(request).subscribe({\n      next: () => {\n        this.otpSent = true;\n        this.snackBar.open('OTP sent successfully!', 'Close', { duration: 3000 });\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'Failed to send OTP', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  loginWithOTP(): void {\n    if (this.otpForm.invalid) {\n      this.markFormGroupTouched(this.otpForm);\n      return;\n    }\n\n    this.loading = true;\n    const { identifier, code } = this.otpForm.value;\n\n    this.authService.loginWithOTP(identifier, code).subscribe({\n      next: () => {\n        this.snackBar.open('Login successful!', 'Close', { duration: 3000 });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'OTP login failed', 'Close', { duration: 5000 });\n        this.loading = false;\n      }\n    });\n  }\n\n  toggleOTPLogin(): void {\n    this.showOTPLogin = !this.showOTPLogin;\n    this.showTwoFactor = false;\n    this.otpSent = false;\n    this.otpForm.reset();\n  }\n\n  backToLogin(): void {\n    this.showTwoFactor = false;\n    this.showOTPLogin = false;\n    this.otpSent = false;\n  }\n\n  // OAuth methods\n  loginWithOAuth(provider: 'google' | 'github' | 'microsoft'): void {\n    this.loading = true;\n    this.oauthService.initiateOAuthLogin(provider);\n  }\n\n  private handleOAuthCallback(code: string, state?: string): void {\n    this.loading = true;\n    this.oauthService.handleOAuthCallback(code, state).subscribe({\n      next: (response) => {\n        this.snackBar.open('OAuth login successful!', 'Close', { duration: 3000 });\n        this.router.navigate([this.returnUrl]);\n        this.loading = false;\n      },\n      error: (error) => {\n        this.snackBar.open(error.message || 'OAuth login failed', 'Close', { duration: 5000 });\n        this.loading = false;\n        // Remove OAuth parameters from URL\n        this.router.navigate([], {\n          relativeTo: this.route,\n          queryParams: {},\n          replaceUrl: true\n        });\n      }\n    });\n  }\n\n  getFieldError(form: FormGroup, fieldName: string): string {\n    const field = form.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} is required`;\n      if (field.errors['email']) return 'Please enter a valid email';\n      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      if (field.errors['pattern']) return 'Please enter a valid format';\n    }\n    return '';\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n}\n", "<div class=\"auth-container\">\n  <div class=\"auth-card fade-in\">\n    <!-- Header -->\n    <div class=\"auth-header\">\n      <h1>Welcome Back</h1>\n      <p>Sign in to your secure account</p>\n      <div class=\"security-badge\">\n        <mat-icon>security</mat-icon>\n        Secure Login\n      </div>\n    </div>\n\n    <div class=\"auth-content\">\n      <!-- Regular Login Form -->\n      <form *ngIf=\"!showOTPLogin && !showTwoFactor\" [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Email Address</mat-label>\n          <input matInput type=\"email\" formControlName=\"email\" autocomplete=\"email\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError(loginForm, 'email') }}</mat-error>\n        </mat-form-field>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Password</mat-label>\n          <input matInput [type]=\"hidePassword ? 'password' : 'text'\" formControlName=\"password\" autocomplete=\"current-password\">\n          <button mat-icon-button matSuffix (click)=\"hidePassword = !hidePassword\" type=\"button\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError(loginForm, 'password') }}</mat-error>\n        </mat-form-field>\n\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"submit-button\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Sign In</span>\n        </button>\n\n        <div class=\"divider\">\n          <span>or</span>\n        </div>\n\n        <button mat-stroked-button type=\"button\" class=\"w-100\" (click)=\"toggleOTPLogin()\">\n          <mat-icon>sms</mat-icon>\n          Login with OTP\n        </button>\n\n        <!-- OAuth Login Buttons -->\n        <div class=\"oauth-section mt-3\">\n          <div class=\"divider\">\n            <span>or continue with</span>\n          </div>\n\n          <div class=\"oauth-buttons\">\n            <button *ngFor=\"let provider of oauthProviders\"\n                    mat-stroked-button\n                    type=\"button\"\n                    class=\"oauth-button\"\n                    [style.border-color]=\"provider.color\"\n                    (click)=\"loginWithOAuth(provider.name)\"\n                    [disabled]=\"loading\">\n              <i [class]=\"provider.icon\" [style.color]=\"provider.color\"></i>\n              <span>{{ provider.displayName }}</span>\n            </button>\n          </div>\n        </div>\n\n        <div class=\"text-center mt-3\">\n          <a routerLink=\"/auth/forgot-password\" class=\"text-primary\">Forgot Password?</a>\n        </div>\n\n        <div class=\"text-center mt-2\">\n          <span>Don't have an account? </span>\n          <a routerLink=\"/auth/register\" class=\"text-primary\">Sign Up</a>\n        </div>\n      </form>\n\n      <!-- Two-Factor Authentication Form -->\n      <form *ngIf=\"showTwoFactor\" [formGroup]=\"twoFactorForm\" (ngSubmit)=\"onTwoFactorSubmit()\">\n        <div class=\"text-center mb-3\">\n          <mat-icon color=\"primary\" style=\"font-size: 48px; width: 48px; height: 48px;\">security</mat-icon>\n          <h3>Two-Factor Authentication</h3>\n          <p>Enter the 6-digit code from your authenticator app</p>\n        </div>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Authentication Code</mat-label>\n          <input matInput formControlName=\"twoFactorToken\" placeholder=\"000000\" maxlength=\"6\" autocomplete=\"one-time-code\">\n          <mat-icon matSuffix>verified_user</mat-icon>\n          <mat-error>{{ getFieldError(twoFactorForm, 'twoFactorToken') }}</mat-error>\n        </mat-form-field>\n\n        <button mat-raised-button color=\"primary\" type=\"submit\" class=\"submit-button\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Verify & Sign In</span>\n        </button>\n\n        <button mat-button type=\"button\" class=\"w-100 mt-2\" (click)=\"backToLogin()\">\n          <mat-icon>arrow_back</mat-icon>\n          Back to Login\n        </button>\n      </form>\n\n      <!-- OTP Login Form -->\n      <form *ngIf=\"showOTPLogin\" [formGroup]=\"otpForm\">\n        <div class=\"text-center mb-3\">\n          <mat-icon color=\"primary\" style=\"font-size: 48px; width: 48px; height: 48px;\">sms</mat-icon>\n          <h3>Login with OTP</h3>\n          <p>Enter your email or phone number to receive a one-time password</p>\n        </div>\n\n        <mat-form-field class=\"form-field\" appearance=\"outline\">\n          <mat-label>Email or Phone Number</mat-label>\n          <input matInput formControlName=\"identifier\" placeholder=\"<EMAIL> or +1234567890\">\n          <mat-icon matSuffix>contact_mail</mat-icon>\n          <mat-error>{{ getFieldError(otpForm, 'identifier') }}</mat-error>\n        </mat-form-field>\n\n        <button *ngIf=\"!otpSent\" mat-raised-button color=\"accent\" type=\"button\" class=\"submit-button\" \n                (click)=\"sendOTP()\" [disabled]=\"loading\">\n          <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n          <span *ngIf=\"!loading\">Send OTP</span>\n        </button>\n\n        <div *ngIf=\"otpSent\">\n          <mat-form-field class=\"form-field\" appearance=\"outline\">\n            <mat-label>Enter OTP</mat-label>\n            <input matInput formControlName=\"code\" placeholder=\"000000\" maxlength=\"6\" autocomplete=\"one-time-code\">\n            <mat-icon matSuffix>lock</mat-icon>\n            <mat-error>{{ getFieldError(otpForm, 'code') }}</mat-error>\n          </mat-form-field>\n\n          <button mat-raised-button color=\"primary\" type=\"button\" class=\"submit-button\" \n                  (click)=\"loginWithOTP()\" [disabled]=\"loading\">\n            <mat-spinner *ngIf=\"loading\" diameter=\"20\"></mat-spinner>\n            <span *ngIf=\"!loading\">Verify & Sign In</span>\n          </button>\n\n          <button mat-button type=\"button\" class=\"w-100 mt-2\" (click)=\"sendOTP()\">\n            <mat-icon>refresh</mat-icon>\n            Resend OTP\n          </button>\n        </div>\n\n        <button mat-button type=\"button\" class=\"w-100 mt-2\" (click)=\"toggleOTPLogin()\">\n          <mat-icon>arrow_back</mat-icon>\n          Back to Login\n        </button>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;IC+BzDC,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAE,cAAA,WAAuB;IAAAF,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAmBnCJ,EAAA,CAAAE,cAAA,iBAM6B;IADrBF,EAAA,CAAAK,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAP,WAAA,CAAAQ,IAAA,CAA6B;IAAA,EAAC;IAE7Cf,EAAA,CAAAC,SAAA,QAA8D;IAC9DD,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAA0B;IAClCH,EADkC,CAAAI,YAAA,EAAO,EAChC;;;;;IALDJ,EAAA,CAAAgB,WAAA,iBAAAT,WAAA,CAAAU,KAAA,CAAqC;IAErCjB,EAAA,CAAAkB,UAAA,aAAAP,MAAA,CAAAQ,OAAA,CAAoB;IACvBnB,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAAqB,UAAA,CAAAd,WAAA,CAAAe,IAAA,CAAuB;IAACtB,EAAA,CAAAgB,WAAA,UAAAT,WAAA,CAAAU,KAAA,CAA8B;IACnDjB,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAuB,iBAAA,CAAAhB,WAAA,CAAAiB,WAAA,CAA0B;;;;;;IA9CxCxB,EAAA,CAAAE,cAAA,cAA8F;IAAxBF,EAAA,CAAAK,UAAA,sBAAAoB,yDAAA;MAAAzB,EAAA,CAAAQ,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAYF,MAAA,CAAAgB,QAAA,EAAU;IAAA,EAAC;IAEzF3B,EADF,CAAAE,cAAA,wBAAwD,gBAC3C;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAY;IACpCJ,EAAA,CAAAC,SAAA,eAA0E;IAC1ED,EAAA,CAAAE,cAAA,mBAAoB;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACpCJ,EAAA,CAAAE,cAAA,gBAAW;IAAAF,EAAA,CAAAG,MAAA,GAAuC;IACpDH,EADoD,CAAAI,YAAA,EAAY,EAC/C;IAGfJ,EADF,CAAAE,cAAA,wBAAwD,iBAC3C;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC/BJ,EAAA,CAAAC,SAAA,iBAAuH;IACvHD,EAAA,CAAAE,cAAA,kBAAuF;IAArDF,EAAA,CAAAK,UAAA,mBAAAuB,yDAAA;MAAA5B,EAAA,CAAAQ,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAkB,YAAA,IAAAlB,MAAA,CAAAkB,YAAA;IAAA,EAAsC;IACtE7B,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,IAAoD;IAChEH,EADgE,CAAAI,YAAA,EAAW,EAClE;IACTJ,EAAA,CAAAE,cAAA,iBAAW;IAAAF,EAAA,CAAAG,MAAA,IAA0C;IACvDH,EADuD,CAAAI,YAAA,EAAY,EAClD;IAEjBJ,EAAA,CAAAE,cAAA,kBAAmG;IAEjGF,EADA,CAAA8B,UAAA,KAAAC,8CAAA,0BAA2C,KAAAC,uCAAA,mBACpB;IACzBhC,EAAA,CAAAI,YAAA,EAAS;IAGPJ,EADF,CAAAE,cAAA,eAAqB,YACb;IAAAF,EAAA,CAAAG,MAAA,UAAE;IACVH,EADU,CAAAI,YAAA,EAAO,EACX;IAENJ,EAAA,CAAAE,cAAA,kBAAkF;IAA3BF,EAAA,CAAAK,UAAA,mBAAA4B,yDAAA;MAAAjC,EAAA,CAAAQ,aAAA,CAAAkB,GAAA;MAAA,MAAAf,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAuB,cAAA,EAAgB;IAAA,EAAC;IAC/ElC,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACxBJ,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAKLJ,EAFJ,CAAAE,cAAA,eAAgC,eACT,YACb;IAAAF,EAAA,CAAAG,MAAA,wBAAgB;IACxBH,EADwB,CAAAI,YAAA,EAAO,EACzB;IAENJ,EAAA,CAAAE,cAAA,eAA2B;IACzBF,EAAA,CAAA8B,UAAA,KAAAK,yCAAA,qBAM6B;IAKjCnC,EADE,CAAAI,YAAA,EAAM,EACF;IAGJJ,EADF,CAAAE,cAAA,eAA8B,aAC+B;IAAAF,EAAA,CAAAG,MAAA,wBAAgB;IAC7EH,EAD6E,CAAAI,YAAA,EAAI,EAC3E;IAGJJ,EADF,CAAAE,cAAA,eAA8B,YACtB;IAAAF,EAAA,CAAAG,MAAA,+BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACpCJ,EAAA,CAAAE,cAAA,aAAoD;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAE/DH,EAF+D,CAAAI,YAAA,EAAI,EAC3D,EACD;;;;IA3DuCJ,EAAA,CAAAkB,UAAA,cAAAP,MAAA,CAAAyB,SAAA,CAAuB;IAKtDpC,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAAuB,iBAAA,CAAAZ,MAAA,CAAA0B,aAAA,CAAA1B,MAAA,CAAAyB,SAAA,WAAuC;IAKlCpC,EAAA,CAAAoB,SAAA,GAA2C;IAA3CpB,EAAA,CAAAkB,UAAA,SAAAP,MAAA,CAAAkB,YAAA,uBAA2C;IAE/C7B,EAAA,CAAAoB,SAAA,GAAoD;IAApDpB,EAAA,CAAAuB,iBAAA,CAAAZ,MAAA,CAAAkB,YAAA,mCAAoD;IAErD7B,EAAA,CAAAoB,SAAA,GAA0C;IAA1CpB,EAAA,CAAAuB,iBAAA,CAAAZ,MAAA,CAAA0B,aAAA,CAAA1B,MAAA,CAAAyB,SAAA,cAA0C;IAGuBpC,EAAA,CAAAoB,SAAA,EAAoB;IAApBpB,EAAA,CAAAkB,UAAA,aAAAP,MAAA,CAAAQ,OAAA,CAAoB;IAClFnB,EAAA,CAAAoB,SAAA,EAAa;IAAbpB,EAAA,CAAAkB,UAAA,SAAAP,MAAA,CAAAQ,OAAA,CAAa;IACpBnB,EAAA,CAAAoB,SAAA,EAAc;IAAdpB,EAAA,CAAAkB,UAAA,UAAAP,MAAA,CAAAQ,OAAA,CAAc;IAmBUnB,EAAA,CAAAoB,SAAA,IAAiB;IAAjBpB,EAAA,CAAAkB,UAAA,YAAAP,MAAA,CAAA2B,cAAA,CAAiB;;;;;IAuChDtC,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAE,cAAA,WAAuB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAhBlDJ,EAAA,CAAAE,cAAA,cAAyF;IAAjCF,EAAA,CAAAK,UAAA,sBAAAkC,yDAAA;MAAAvC,EAAA,CAAAQ,aAAA,CAAAgC,GAAA;MAAA,MAAA7B,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAYF,MAAA,CAAA8B,iBAAA,EAAmB;IAAA,EAAC;IAEpFzC,EADF,CAAAE,cAAA,cAA8B,mBACkD;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACjGJ,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAG,MAAA,yDAAkD;IACvDH,EADuD,CAAAI,YAAA,EAAI,EACrD;IAGJJ,EADF,CAAAE,cAAA,wBAAwD,gBAC3C;IAAAF,EAAA,CAAAG,MAAA,2BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC1CJ,EAAA,CAAAC,SAAA,iBAAiH;IACjHD,EAAA,CAAAE,cAAA,oBAAoB;IAAAF,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5CJ,EAAA,CAAAE,cAAA,iBAAW;IAAAF,EAAA,CAAAG,MAAA,IAAoD;IACjEH,EADiE,CAAAI,YAAA,EAAY,EAC5D;IAEjBJ,EAAA,CAAAE,cAAA,kBAAmG;IAEjGF,EADA,CAAA8B,UAAA,KAAAY,8CAAA,0BAA2C,KAAAC,uCAAA,mBACpB;IACzB3C,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,kBAA4E;IAAxBF,EAAA,CAAAK,UAAA,mBAAAuC,yDAAA;MAAA5C,EAAA,CAAAQ,aAAA,CAAAgC,GAAA;MAAA,MAAA7B,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAkC,WAAA,EAAa;IAAA,EAAC;IACzE7C,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,uBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACJ;;;;IAvBqBJ,EAAA,CAAAkB,UAAA,cAAAP,MAAA,CAAAmC,aAAA,CAA2B;IAWxC9C,EAAA,CAAAoB,SAAA,IAAoD;IAApDpB,EAAA,CAAAuB,iBAAA,CAAAZ,MAAA,CAAA0B,aAAA,CAAA1B,MAAA,CAAAmC,aAAA,oBAAoD;IAGa9C,EAAA,CAAAoB,SAAA,EAAoB;IAApBpB,EAAA,CAAAkB,UAAA,aAAAP,MAAA,CAAAQ,OAAA,CAAoB;IAClFnB,EAAA,CAAAoB,SAAA,EAAa;IAAbpB,EAAA,CAAAkB,UAAA,SAAAP,MAAA,CAAAQ,OAAA,CAAa;IACpBnB,EAAA,CAAAoB,SAAA,EAAc;IAAdpB,EAAA,CAAAkB,UAAA,UAAAP,MAAA,CAAAQ,OAAA,CAAc;;;;;IA0BrBnB,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAE,cAAA,WAAuB;IAAAF,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAHxCJ,EAAA,CAAAE,cAAA,iBACiD;IAAzCF,EAAA,CAAAK,UAAA,mBAAA0C,kEAAA;MAAA/C,EAAA,CAAAQ,aAAA,CAAAwC,GAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAsC,OAAA,EAAS;IAAA,EAAC;IAEzBjD,EADA,CAAA8B,UAAA,IAAAoB,uDAAA,0BAA2C,IAAAC,gDAAA,mBACpB;IACzBnD,EAAA,CAAAI,YAAA,EAAS;;;;IAHmBJ,EAAA,CAAAkB,UAAA,aAAAP,MAAA,CAAAQ,OAAA,CAAoB;IAChCnB,EAAA,CAAAoB,SAAA,EAAa;IAAbpB,EAAA,CAAAkB,UAAA,SAAAP,MAAA,CAAAQ,OAAA,CAAa;IACpBnB,EAAA,CAAAoB,SAAA,EAAc;IAAdpB,EAAA,CAAAkB,UAAA,UAAAP,MAAA,CAAAQ,OAAA,CAAc;;;;;IAanBnB,EAAA,CAAAC,SAAA,sBAAyD;;;;;IACzDD,EAAA,CAAAE,cAAA,WAAuB;IAAAF,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAT9CJ,EAFJ,CAAAE,cAAA,UAAqB,wBACqC,gBAC3C;IAAAF,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAChCJ,EAAA,CAAAC,SAAA,gBAAuG;IACvGD,EAAA,CAAAE,cAAA,mBAAoB;IAAAF,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACnCJ,EAAA,CAAAE,cAAA,gBAAW;IAAAF,EAAA,CAAAG,MAAA,GAAoC;IACjDH,EADiD,CAAAI,YAAA,EAAY,EAC5C;IAEjBJ,EAAA,CAAAE,cAAA,iBACsD;IAA9CF,EAAA,CAAAK,UAAA,mBAAA+C,+DAAA;MAAApD,EAAA,CAAAQ,aAAA,CAAA6C,GAAA;MAAA,MAAA1C,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA2C,YAAA,EAAc;IAAA,EAAC;IAE9BtD,EADA,CAAA8B,UAAA,KAAAyB,qDAAA,0BAA2C,KAAAC,8CAAA,mBACpB;IACzBxD,EAAA,CAAAI,YAAA,EAAS;IAETJ,EAAA,CAAAE,cAAA,kBAAwE;IAApBF,EAAA,CAAAK,UAAA,mBAAAoD,gEAAA;MAAAzD,EAAA,CAAAQ,aAAA,CAAA6C,GAAA;MAAA,MAAA1C,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAsC,OAAA,EAAS;IAAA,EAAC;IACrEjD,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5BJ,EAAA,CAAAG,MAAA,oBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACL;;;;IAbSJ,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAuB,iBAAA,CAAAZ,MAAA,CAAA0B,aAAA,CAAA1B,MAAA,CAAA+C,OAAA,UAAoC;IAIhB1D,EAAA,CAAAoB,SAAA,EAAoB;IAApBpB,EAAA,CAAAkB,UAAA,aAAAP,MAAA,CAAAQ,OAAA,CAAoB;IACrCnB,EAAA,CAAAoB,SAAA,EAAa;IAAbpB,EAAA,CAAAkB,UAAA,SAAAP,MAAA,CAAAQ,OAAA,CAAa;IACpBnB,EAAA,CAAAoB,SAAA,EAAc;IAAdpB,EAAA,CAAAkB,UAAA,UAAAP,MAAA,CAAAQ,OAAA,CAAc;;;;;;IA7BvBnB,EAFJ,CAAAE,cAAA,eAAiD,cACjB,mBACkD;IAAAF,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC5FJ,EAAA,CAAAE,cAAA,SAAI;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvBJ,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAG,MAAA,sEAA+D;IACpEH,EADoE,CAAAI,YAAA,EAAI,EAClE;IAGJJ,EADF,CAAAE,cAAA,wBAAwD,gBAC3C;IAAAF,EAAA,CAAAG,MAAA,6BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAY;IAC5CJ,EAAA,CAAAC,SAAA,iBAA4F;IAC5FD,EAAA,CAAAE,cAAA,oBAAoB;IAAAF,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC3CJ,EAAA,CAAAE,cAAA,iBAAW;IAAAF,EAAA,CAAAG,MAAA,IAA0C;IACvDH,EADuD,CAAAI,YAAA,EAAY,EAClD;IAQjBJ,EANA,CAAA8B,UAAA,KAAA6B,yCAAA,qBACiD,KAAAC,sCAAA,mBAK5B;IAoBrB5D,EAAA,CAAAE,cAAA,kBAA+E;IAA3BF,EAAA,CAAAK,UAAA,mBAAAwD,yDAAA;MAAA7D,EAAA,CAAAQ,aAAA,CAAAsD,GAAA;MAAA,MAAAnD,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAuB,cAAA,EAAgB;IAAA,EAAC;IAC5ElC,EAAA,CAAAE,cAAA,gBAAU;IAAAF,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,uBACF;IACFH,EADE,CAAAI,YAAA,EAAS,EACJ;;;;IA5CoBJ,EAAA,CAAAkB,UAAA,cAAAP,MAAA,CAAA+C,OAAA,CAAqB;IAWjC1D,EAAA,CAAAoB,SAAA,IAA0C;IAA1CpB,EAAA,CAAAuB,iBAAA,CAAAZ,MAAA,CAAA0B,aAAA,CAAA1B,MAAA,CAAA+C,OAAA,gBAA0C;IAG9C1D,EAAA,CAAAoB,SAAA,EAAc;IAAdpB,EAAA,CAAAkB,UAAA,UAAAP,MAAA,CAAAoD,OAAA,CAAc;IAMjB/D,EAAA,CAAAoB,SAAA,EAAa;IAAbpB,EAAA,CAAAkB,UAAA,SAAAP,MAAA,CAAAoD,OAAA,CAAa;;;AD5G3B,OAAM,MAAOC,cAAc;EAazBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,YAA0B,EAC1BC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IALrB,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IAdlB,KAAApD,OAAO,GAAG,KAAK;IACf,KAAAU,YAAY,GAAG,IAAI;IACnB,KAAA2C,YAAY,GAAG,KAAK;IACpB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAV,OAAO,GAAG,KAAK;IACf,KAAAW,SAAS,GAAG,EAAE;IACd,KAAApC,cAAc,GAAoB,EAAE;IAUlC,IAAI,CAACF,SAAS,GAAG,IAAI,CAAC8B,WAAW,CAACS,KAAK,CAAC;MACtCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC7E,UAAU,CAAC8E,QAAQ,EAAE9E,UAAU,CAAC6E,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC/E,UAAU,CAAC8E,QAAQ,EAAE9E,UAAU,CAACgF,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;IAEF,IAAI,CAACrB,OAAO,GAAG,IAAI,CAACQ,WAAW,CAACS,KAAK,CAAC;MACpCK,UAAU,EAAE,CAAC,EAAE,EAAE,CAACjF,UAAU,CAAC8E,QAAQ,CAAC,CAAC;MACvCI,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClF,UAAU,CAAC8E,QAAQ,EAAE9E,UAAU,CAACmF,OAAO,CAAC,SAAS,CAAC,CAAC;KAChE,CAAC;IAEF,IAAI,CAACpC,aAAa,GAAG,IAAI,CAACoB,WAAW,CAACS,KAAK,CAAC;MAC1CQ,cAAc,EAAE,CAAC,EAAE,EAAE,CAACpF,UAAU,CAAC8E,QAAQ,EAAE9E,UAAU,CAACmF,OAAO,CAAC,SAAS,CAAC,CAAC;KAC1E,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACV,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACe,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;IAC7E,IAAI,CAAChD,cAAc,GAAG,IAAI,CAAC8B,YAAY,CAACmB,qBAAqB,EAAE;IAE/D,IAAI,IAAI,CAACpB,WAAW,CAACqB,eAAe,EAAE;MACpC,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC;IACxC;IAEA,MAAMgB,OAAO,GAAG,IAAI,CAACpB,KAAK,CAACe,QAAQ,CAACC,WAAW,CAAC,SAAS,CAAC;IAC1D,IAAII,OAAO,EAAE;MACX,IAAI,CAACnB,QAAQ,CAACoB,IAAI,CAACD,OAAO,EAAE,OAAO,EAAE;QAAEE,QAAQ,EAAE;MAAI,CAAE,CAAC;IAC1D;IAEA;IACA,MAAMX,IAAI,GAAG,IAAI,CAACX,KAAK,CAACe,QAAQ,CAACC,WAAW,CAAC,MAAM,CAAC;IACpD,MAAMO,KAAK,GAAG,IAAI,CAACvB,KAAK,CAACe,QAAQ,CAACC,WAAW,CAAC,OAAO,CAAC;IACtD,IAAIL,IAAI,EAAE;MACR,IAAI,CAACa,mBAAmB,CAACb,IAAI,EAAEY,KAAK,CAAC;IACvC;EACF;EAEAlE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACS,SAAS,CAAC2D,OAAO,EAAE;MAC1B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAC5D,SAAS,CAAC;MACzC;IACF;IAEA,IAAI,CAACjB,OAAO,GAAG,IAAI;IACnB,MAAM8E,WAAW,GAAc,IAAI,CAAC7D,SAAS,CAAC8D,KAAK;IAEnD,IAAI,CAAC/B,WAAW,CAACgC,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;UAC9B,IAAI,CAAC9B,aAAa,GAAG,IAAI;UACzB,IAAI,CAACF,QAAQ,CAACoB,IAAI,CAAC,kDAAkD,EAAE,OAAO,EAAE;YAC9EC,QAAQ,EAAE;WACX,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACrB,QAAQ,CAACoB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE,IAAI,CAACvB,MAAM,CAACoB,QAAQ,CAAC,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC;QACxC;QACA,IAAI,CAACvD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjC,QAAQ,CAACoB,IAAI,CAACa,KAAK,CAACd,OAAO,IAAI,cAAc,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QAChF,IAAI,CAACzE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAsB,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACK,aAAa,CAACiD,OAAO,EAAE;MAC9B,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAClD,aAAa,CAAC;MAC7C;IACF;IAEA,IAAI,CAAC3B,OAAO,GAAG,IAAI;IACnB,MAAM8E,WAAW,GAAc;MAC7B,GAAG,IAAI,CAAC7D,SAAS,CAAC8D,KAAK;MACvBf,cAAc,EAAE,IAAI,CAACrC,aAAa,CAACoD,KAAK,CAACf;KAC1C;IAED,IAAI,CAAChB,WAAW,CAACgC,KAAK,CAACF,WAAW,CAAC,CAACG,SAAS,CAAC;MAC5CC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC/B,QAAQ,CAACoB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpE,IAAI,CAACvB,MAAM,CAACoB,QAAQ,CAAC,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC;QACtC,IAAI,CAACvD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjC,QAAQ,CAACoB,IAAI,CAACa,KAAK,CAACd,OAAO,IAAI,kCAAkC,EAAE,OAAO,EAAE;UAC/EE,QAAQ,EAAE;SACX,CAAC;QACF,IAAI,CAACzE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA8B,OAAOA,CAAA;IACL,MAAM+B,UAAU,GAAG,IAAI,CAACtB,OAAO,CAAC+C,GAAG,CAAC,YAAY,CAAC,EAAEP,KAAK;IACxD,IAAI,CAAClB,UAAU,EAAE;MACf,IAAI,CAACT,QAAQ,CAACoB,IAAI,CAAC,oCAAoC,EAAE,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACrF;IACF;IAEA,IAAI,CAACzE,OAAO,GAAG,IAAI;IACnB,MAAMuF,OAAO,GAAe;MAC1B1B,UAAU;MACV2B,IAAI,EAAE;KACP;IAED,IAAI,CAACxC,WAAW,CAAClB,OAAO,CAACyD,OAAO,CAAC,CAACN,SAAS,CAAC;MAC1CC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACtC,OAAO,GAAG,IAAI;QACnB,IAAI,CAACQ,QAAQ,CAACoB,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzE,IAAI,CAACzE,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjC,QAAQ,CAACoB,IAAI,CAACa,KAAK,CAACd,OAAO,IAAI,oBAAoB,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QACtF,IAAI,CAACzE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACI,OAAO,CAACqC,OAAO,EAAE;MACxB,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACtC,OAAO,CAAC;MACvC;IACF;IAEA,IAAI,CAACvC,OAAO,GAAG,IAAI;IACnB,MAAM;MAAE6D,UAAU;MAAEC;IAAI,CAAE,GAAG,IAAI,CAACvB,OAAO,CAACwC,KAAK;IAE/C,IAAI,CAAC/B,WAAW,CAACb,YAAY,CAAC0B,UAAU,EAAEC,IAAI,CAAC,CAACmB,SAAS,CAAC;MACxDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC9B,QAAQ,CAACoB,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpE,IAAI,CAACvB,MAAM,CAACoB,QAAQ,CAAC,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC;QACtC,IAAI,CAACvD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjC,QAAQ,CAACoB,IAAI,CAACa,KAAK,CAACd,OAAO,IAAI,kBAAkB,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QACpF,IAAI,CAACzE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAe,cAAcA,CAAA;IACZ,IAAI,CAACsC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACV,OAAO,GAAG,KAAK;IACpB,IAAI,CAACL,OAAO,CAACkD,KAAK,EAAE;EACtB;EAEA/D,WAAWA,CAAA;IACT,IAAI,CAAC4B,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACD,YAAY,GAAG,KAAK;IACzB,IAAI,CAACT,OAAO,GAAG,KAAK;EACtB;EAEA;EACAjD,cAAcA,CAAC+F,QAA2C;IACxD,IAAI,CAAC1F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACiD,YAAY,CAAC0C,kBAAkB,CAACD,QAAQ,CAAC;EAChD;EAEQf,mBAAmBA,CAACb,IAAY,EAAEY,KAAc;IACtD,IAAI,CAAC1E,OAAO,GAAG,IAAI;IACnB,IAAI,CAACiD,YAAY,CAAC0B,mBAAmB,CAACb,IAAI,EAAEY,KAAK,CAAC,CAACO,SAAS,CAAC;MAC3DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAAC/B,QAAQ,CAACoB,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC1E,IAAI,CAACvB,MAAM,CAACoB,QAAQ,CAAC,CAAC,IAAI,CAACf,SAAS,CAAC,CAAC;QACtC,IAAI,CAACvD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqF,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjC,QAAQ,CAACoB,IAAI,CAACa,KAAK,CAACd,OAAO,IAAI,oBAAoB,EAAE,OAAO,EAAE;UAAEE,QAAQ,EAAE;QAAI,CAAE,CAAC;QACtF,IAAI,CAACzE,OAAO,GAAG,KAAK;QACpB;QACA,IAAI,CAACkD,MAAM,CAACoB,QAAQ,CAAC,EAAE,EAAE;UACvBsB,UAAU,EAAE,IAAI,CAACzC,KAAK;UACtBgB,WAAW,EAAE,EAAE;UACf0B,UAAU,EAAE;SACb,CAAC;MACJ;KACD,CAAC;EACJ;EAEA3E,aAAaA,CAAC4E,IAAe,EAAEC,SAAiB;IAC9C,MAAMC,KAAK,GAAGF,IAAI,CAACR,GAAG,CAACS,SAAS,CAAC;IACjC,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGF,SAAS,cAAc;MAC/D,IAAIC,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,4BAA4B;MAC9D,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGF,SAAS,qBAAqBC,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,CAACE,cAAc,aAAa;MAC5H,IAAIH,KAAK,CAACC,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,6BAA6B;IACnE;IACA,OAAO,EAAE;EACX;EAEQpB,oBAAoBA,CAACuB,SAAoB;IAC/CC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAC5C,MAAMC,OAAO,GAAGN,SAAS,CAACd,GAAG,CAACmB,GAAG,CAAC;MAClCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qCAzNU/D,cAAc,EAAAhE,EAAA,CAAAgI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlI,EAAA,CAAAgI,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAApI,EAAA,CAAAgI,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAtI,EAAA,CAAAgI,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAxI,EAAA,CAAAgI,iBAAA,CAAAO,EAAA,CAAAE,cAAA,GAAAzI,EAAA,CAAAgI,iBAAA,CAAAU,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAd5E,cAAc;IAAA6E,SAAA;IAAAC,UAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVrBpJ,EAJN,CAAAE,cAAA,aAA4B,aACK,aAEJ,SACnB;QAAAF,EAAA,CAAAG,MAAA,mBAAY;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACrBJ,EAAA,CAAAE,cAAA,QAAG;QAAAF,EAAA,CAAAG,MAAA,qCAA8B;QAAAH,EAAA,CAAAI,YAAA,EAAI;QAEnCJ,EADF,CAAAE,cAAA,aAA4B,eAChB;QAAAF,EAAA,CAAAG,MAAA,eAAQ;QAAAH,EAAA,CAAAI,YAAA,EAAW;QAC7BJ,EAAA,CAAAG,MAAA,sBACF;QACFH,EADE,CAAAI,YAAA,EAAM,EACF;QAENJ,EAAA,CAAAE,cAAA,cAA0B;QA0FxBF,EAxFA,CAAA8B,UAAA,KAAAwH,+BAAA,mBAA8F,KAAAC,+BAAA,mBA8DL,KAAAC,+BAAA,mBA0BxC;QA+CvDxJ,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;QAvIOJ,EAAA,CAAAoB,SAAA,IAAqC;QAArCpB,EAAA,CAAAkB,UAAA,UAAAmI,GAAA,CAAA7E,YAAA,KAAA6E,GAAA,CAAA5E,aAAA,CAAqC;QA8DrCzE,EAAA,CAAAoB,SAAA,EAAmB;QAAnBpB,EAAA,CAAAkB,UAAA,SAAAmI,GAAA,CAAA5E,aAAA,CAAmB;QA0BnBzE,EAAA,CAAAoB,SAAA,EAAkB;QAAlBpB,EAAA,CAAAkB,UAAA,SAAAmI,GAAA,CAAA7E,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}