.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 2rem;
}

.container {
  max-width: 800px;
  margin: 0 auto;
}

h1 {
  color: #333;
  margin-bottom: 2rem;
}

mat-card {
  margin-bottom: 1.5rem;
}

.user-info {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;

  .user-avatar {
    img {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #e0e0e0;
    }
  }

  .user-details {
    flex: 1;

    p {
      margin: 0.5rem 0;

      strong {
        color: #333;
        margin-right: 0.5rem;
      }
    }
  }
}

.oauth-info {
  margin-top: 1rem;

  .oauth-provider {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    background: #f5f5f5;
    border-radius: 20px;
    font-size: 0.875rem;

    i {
      font-size: 1rem;
    }
  }
}

.account-status {
  margin-top: 1rem;

  .status-chips {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .status-chip {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    &.verified {
      background: #e8f5e8;
      color: #2e7d32;
      border: 1px solid #4caf50;
    }

    &.unverified {
      background: #fff3e0;
      color: #f57c00;
      border: 1px solid #ff9800;
    }

    &.enabled {
      background: #e3f2fd;
      color: #1976d2;
      border: 1px solid #2196f3;
    }

    &.disabled {
      background: #f5f5f5;
      color: #666;
      border: 1px solid #ccc;
    }
  }
}

.security-section {
  margin-top: 1.5rem;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
      color: #333;
    }

    .oauth-password-notice {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #666;
      font-size: 0.875rem;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

.change-password-form {
  background: #f9f9f9;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;

  .form-field {
    width: 100%;
    margin-bottom: 1rem;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;

    button {
      min-width: 120px;
    }
  }
}
