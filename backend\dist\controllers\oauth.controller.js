"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OAuthController = void 0;
const tslib_1 = require("tslib");
const core_1 = require("@loopback/core");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const authentication_jwt_1 = require("@loopback/authentication-jwt");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let OAuthController = class OAuthController {
    constructor(jwtService, userRepository, oauthService) {
        this.jwtService = jwtService;
        this.userRepository = userRepository;
        this.oauthService = oauthService;
    }
    async getOAuthUrl(provider) {
        if (!['google', 'github', 'microsoft'].includes(provider)) {
            throw new rest_1.HttpErrors.BadRequest('Unsupported OAuth provider');
        }
        const result = await this.oauthService.getAuthorizationUrl(provider);
        return result;
    }
    async handleOAuthCallback(provider, request) {
        if (!['google', 'github', 'microsoft'].includes(provider)) {
            throw new rest_1.HttpErrors.BadRequest('Unsupported OAuth provider');
        }
        // Exchange code for user info
        const userInfo = await this.oauthService.exchangeCodeForUser(provider, request.code, request.state);
        // Check if user exists
        const providerIdField = `${provider}Id`;
        let user = await this.userRepository.findOne({
            where: { [providerIdField]: userInfo.id },
        });
        let isNewUser = false;
        if (!user) {
            // Check if user exists with same email
            user = await this.userRepository.findOne({
                where: { email: userInfo.email },
            });
            if (user) {
                // Link OAuth account to existing user
                await this.userRepository.updateById(user.id, {
                    [providerIdField]: userInfo.id,
                    oauthProvider: provider,
                    avatarUrl: userInfo.avatarUrl || user.avatarUrl,
                    updatedAt: new Date(),
                });
            }
            else {
                // Create new user
                user = await this.userRepository.create({
                    email: userInfo.email,
                    firstName: userInfo.firstName || userInfo.name?.split(' ')[0] || 'User',
                    lastName: userInfo.lastName || userInfo.name?.split(' ').slice(1).join(' ') || '',
                    [providerIdField]: userInfo.id,
                    oauthProvider: provider,
                    avatarUrl: userInfo.avatarUrl,
                    emailVerified: true, // OAuth emails are considered verified
                    roles: ['user'],
                    isActive: true,
                });
                isNewUser = true;
            }
        }
        // Update last login
        await this.userRepository.updateById(user.id, {
            lastLoginAt: new Date(),
            updatedAt: new Date(),
        });
        // Generate JWT token
        const userProfile = {
            id: user.id,
            email: user.email,
            name: `${user.firstName} ${user.lastName}`,
            roles: user.roles,
        };
        const token = await this.jwtService.generateToken({
            id: user.id.toString(),
            email: user.email,
            name: `${user.firstName} ${user.lastName}`,
        });
        return {
            token,
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                avatarUrl: user.avatarUrl,
                oauthProvider: user.oauthProvider,
                roles: user.roles,
            },
            isNewUser,
        };
    }
    async disconnectOAuth(provider, request) {
        if (!['google', 'github', 'microsoft'].includes(provider)) {
            throw new rest_1.HttpErrors.BadRequest('Unsupported OAuth provider');
        }
        const user = await this.userRepository.findById(request.userId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        const providerIdField = `${provider}Id`;
        // Check if user has a password (can't disconnect if OAuth is only login method)
        if (!user.password && user.oauthProvider === provider) {
            throw new rest_1.HttpErrors.BadRequest('Cannot disconnect OAuth provider as it is your only login method. Please set a password first.');
        }
        await this.userRepository.updateById(request.userId, {
            [providerIdField]: null,
            oauthProvider: user.oauthProvider === provider ? null : user.oauthProvider,
            updatedAt: new Date(),
        });
        return { message: `${provider} account disconnected successfully` };
    }
};
exports.OAuthController = OAuthController;
tslib_1.__decorate([
    (0, rest_1.get)('/auth/oauth/{provider}/url'),
    (0, rest_1.response)(200, {
        description: 'Get OAuth authorization URL',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        url: { type: 'string' },
                        state: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('provider')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", Promise)
], OAuthController.prototype, "getOAuthUrl", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/oauth/{provider}/callback'),
    (0, rest_1.response)(200, {
        description: 'Handle OAuth callback',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        user: { type: 'object' },
                        isNewUser: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('provider')),
    tslib_1.__param(1, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['code'],
                    properties: {
                        code: { type: 'string' },
                        state: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OAuthController.prototype, "handleOAuthCallback", null);
tslib_1.__decorate([
    (0, rest_1.get)('/auth/oauth/{provider}/disconnect'),
    (0, rest_1.response)(200, {
        description: 'Disconnect OAuth provider',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('provider')),
    tslib_1.__param(1, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['userId'],
                    properties: {
                        userId: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], OAuthController.prototype, "disconnectOAuth", null);
exports.OAuthController = OAuthController = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)(authentication_jwt_1.TokenServiceBindings.TOKEN_SERVICE)),
    tslib_1.__param(1, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(2, (0, core_1.inject)('services.OAuthService')),
    tslib_1.__metadata("design:paramtypes", [Object, repositories_1.UserRepository,
        services_1.OAuthService])
], OAuthController);
//# sourceMappingURL=oauth.controller.js.map