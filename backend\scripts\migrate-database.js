const { Client } = require('pg');
require('dotenv').config();

async function migrateDatabase() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/secureapp'
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database');

    // Check if OAuth columns exist
    const checkColumns = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'user' AND column_name IN ('google_id', 'github_id', 'microsoft_id', 'oauth_provider', 'avatar_url');
    `;
    
    const existingColumns = await client.query(checkColumns);
    const existingColumnNames = existingColumns.rows.map(row => row.column_name);
    
    console.log('📋 Existing OAuth columns:', existingColumnNames);

    // Add missing OAuth columns
    const columnsToAdd = [
      { name: 'google_id', type: 'VARCHAR(255)' },
      { name: 'github_id', type: 'VARCHAR(255)' },
      { name: 'microsoft_id', type: 'VARCHAR(255)' },
      { name: 'oauth_provider', type: 'VARCHAR(50)' },
      { name: 'avatar_url', type: 'TEXT' }
    ];

    for (const column of columnsToAdd) {
      if (!existingColumnNames.includes(column.name)) {
        const addColumnQuery = `ALTER TABLE "user" ADD COLUMN ${column.name} ${column.type};`;
        await client.query(addColumnQuery);
        console.log(`✅ Added column: ${column.name}`);
      } else {
        console.log(`ℹ️  Column already exists: ${column.name}`);
      }
    }

    // Create indexes for OAuth columns for better performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_user_google_id ON "user"(google_id);',
      'CREATE INDEX IF NOT EXISTS idx_user_github_id ON "user"(github_id);',
      'CREATE INDEX IF NOT EXISTS idx_user_microsoft_id ON "user"(microsoft_id);',
      'CREATE INDEX IF NOT EXISTS idx_user_oauth_provider ON "user"(oauth_provider);'
    ];

    for (const indexQuery of indexes) {
      await client.query(indexQuery);
    }
    console.log('✅ Created OAuth indexes');

    // Verify the table structure
    const tableStructure = await client.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'user' 
      ORDER BY ordinal_position;
    `);

    console.log('\n📊 Current User table structure:');
    tableStructure.rows.forEach(row => {
      console.log(`   ${row.column_name}: ${row.data_type} (${row.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });

    console.log('\n🎉 Database migration completed successfully!');

  } catch (error) {
    console.error('❌ Database migration failed:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateDatabase().catch(console.error);
}

module.exports = { migrateDatabase };
