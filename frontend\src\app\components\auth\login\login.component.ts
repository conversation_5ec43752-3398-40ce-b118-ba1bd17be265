import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../../services/auth.service';
import { OAuthService } from '../../../services/oauth.service';
import { UserLogin, OTPRequest, OAuthProvider } from '../../../models/user.model';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: false
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  otpForm: FormGroup;
  twoFactorForm: FormGroup;
  
  loading = false;
  hidePassword = true;
  showOTPLogin = false;
  showTwoFactor = false;
  otpSent = false;
  returnUrl = '';
  oauthProviders: OAuthProvider[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private oauthService: OAuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8)]]
    });

    this.otpForm = this.formBuilder.group({
      identifier: ['', [Validators.required]],
      code: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });

    this.twoFactorForm = this.formBuilder.group({
      twoFactorToken: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }

  ngOnInit(): void {
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
    this.oauthProviders = this.oauthService.getAvailableProviders();

    if (this.authService.isAuthenticated) {
      this.router.navigate([this.returnUrl]);
    }

    const message = this.route.snapshot.queryParams['message'];
    if (message) {
      this.snackBar.open(message, 'Close', { duration: 5000 });
    }

    // Handle OAuth callback
    const code = this.route.snapshot.queryParams['code'];
    const state = this.route.snapshot.queryParams['state'];
    if (code) {
      this.handleOAuthCallback(code, state);
    }
  }

  onSubmit(): void {
    if (this.loginForm.invalid) {
      this.markFormGroupTouched(this.loginForm);
      return;
    }

    this.loading = true;
    const credentials: UserLogin = this.loginForm.value;

    this.authService.login(credentials).subscribe({
      next: (response) => {
        if (response.requiresTwoFactor) {
          this.showTwoFactor = true;
          this.snackBar.open('Please enter your two-factor authentication code', 'Close', {
            duration: 5000
          });
        } else {
          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
          this.router.navigate([this.returnUrl]);
        }
        this.loading = false;
      },
      error: (error) => {
        this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });
        this.loading = false;
      }
    });
  }

  onTwoFactorSubmit(): void {
    if (this.twoFactorForm.invalid) {
      this.markFormGroupTouched(this.twoFactorForm);
      return;
    }

    this.loading = true;
    const credentials: UserLogin = {
      ...this.loginForm.value,
      twoFactorToken: this.twoFactorForm.value.twoFactorToken
    };

    this.authService.login(credentials).subscribe({
      next: (response) => {
        this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
        this.router.navigate([this.returnUrl]);
        this.loading = false;
      },
      error: (error) => {
        this.snackBar.open(error.message || 'Two-factor authentication failed', 'Close', {
          duration: 5000
        });
        this.loading = false;
      }
    });
  }

  sendOTP(): void {
    const identifier = this.otpForm.get('identifier')?.value;
    if (!identifier) {
      this.snackBar.open('Please enter email or phone number', 'Close', { duration: 3000 });
      return;
    }

    this.loading = true;
    const request: OTPRequest = {
      identifier,
      type: 'login'
    };

    this.authService.sendOTP(request).subscribe({
      next: () => {
        this.otpSent = true;
        this.snackBar.open('OTP sent successfully!', 'Close', { duration: 3000 });
        this.loading = false;
      },
      error: (error) => {
        this.snackBar.open(error.message || 'Failed to send OTP', 'Close', { duration: 5000 });
        this.loading = false;
      }
    });
  }

  loginWithOTP(): void {
    if (this.otpForm.invalid) {
      this.markFormGroupTouched(this.otpForm);
      return;
    }

    this.loading = true;
    const { identifier, code } = this.otpForm.value;

    this.authService.loginWithOTP(identifier, code).subscribe({
      next: () => {
        this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
        this.router.navigate([this.returnUrl]);
        this.loading = false;
      },
      error: (error) => {
        this.snackBar.open(error.message || 'OTP login failed', 'Close', { duration: 5000 });
        this.loading = false;
      }
    });
  }

  toggleOTPLogin(): void {
    this.showOTPLogin = !this.showOTPLogin;
    this.showTwoFactor = false;
    this.otpSent = false;
    this.otpForm.reset();
  }

  backToLogin(): void {
    this.showTwoFactor = false;
    this.showOTPLogin = false;
    this.otpSent = false;
  }

  // OAuth methods
  loginWithOAuth(provider: 'google' | 'github' | 'microsoft'): void {
    this.loading = true;
    this.oauthService.initiateOAuthLogin(provider);
  }

  private handleOAuthCallback(code: string, state?: string): void {
    this.loading = true;
    this.oauthService.handleOAuthCallback(code, state).subscribe({
      next: (response) => {
        this.snackBar.open('OAuth login successful!', 'Close', { duration: 3000 });
        this.router.navigate([this.returnUrl]);
        this.loading = false;
      },
      error: (error) => {
        this.snackBar.open(error.message || 'OAuth login failed', 'Close', { duration: 5000 });
        this.loading = false;
        // Remove OAuth parameters from URL
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: {},
          replaceUrl: true
        });
      }
    });
  }

  getFieldError(form: FormGroup, fieldName: string): string {
    const field = form.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      if (field.errors['pattern']) return 'Please enter a valid format';
    }
    return '';
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }
}
