{"ast": null, "code": "import { O as OverlayContainer } from './overlay-module-Bd2UplUU.mjs';\nexport { B as BlockScrollStrategy, b as CdkConnectedOverlay, C as CdkOverlayOrigin, p as CloseScrollStrategy, l as ConnectedOverlayPositionChange, j as ConnectionPositionPair, F as FlexibleConnectedPositionStrategy, G as GlobalPositionStrategy, N as NoopScrollStrategy, a as Overlay, i as OverlayConfig, w as OverlayKeyboardDispatcher, t as OverlayModule, u as OverlayOutsideClickDispatcher, e as OverlayPositionBuilder, d as OverlayRef, R as RepositionScrollStrategy, S as STANDARD_DROPDOWN_ADJACENT_POSITIONS, g as STANDARD_DROPDOWN_BELOW_POSITIONS, n as ScrollStrategyOptions, k as ScrollingVisibility, s as createBlockScrollStrategy, q as createCloseScrollStrategy, h as createFlexibleConnectedPositionStrategy, f as createGlobalPositionStrategy, r as createNoopScrollStrategy, c as createOverlayRef, o as createRepositionScrollStrategy, m as validateHorizontalPosition, v as validateVerticalPosition } from './overlay-module-Bd2UplUU.mjs';\nimport * as i0 from '@angular/core';\nimport { inject, RendererFactory2, Injectable } from '@angular/core';\nexport { CdkScrollable, ScrollDispatcher, ViewportRuler, CdkFixedSizeVirtualScroll as ɵɵCdkFixedSizeVirtualScroll, CdkScrollableModule as ɵɵCdkScrollableModule, CdkVirtualForOf as ɵɵCdkVirtualForOf, CdkVirtualScrollViewport as ɵɵCdkVirtualScrollViewport, CdkVirtualScrollableElement as ɵɵCdkVirtualScrollableElement, CdkVirtualScrollableWindow as ɵɵCdkVirtualScrollableWindow } from './scrolling.mjs';\nexport { Dir as ɵɵDir } from './bidi.mjs';\nimport '@angular/common';\nimport './platform-DNDzkVcI.mjs';\nimport './shadow-dom-B0oHn41l.mjs';\nimport './test-environment-CT0XxPyp.mjs';\nimport './style-loader-B2sGQXxD.mjs';\nimport 'rxjs';\nimport './css-pixel-value-C_HEqLhI.mjs';\nimport './array-I1yfCXUO.mjs';\nimport './portal.mjs';\nimport './scrolling-BkvA05C8.mjs';\nimport 'rxjs/operators';\nimport './id-generator-LuoRZSid.mjs';\nimport './directionality-CChdj3az.mjs';\nimport './keycodes-CpHkExLC.mjs';\nimport './keycodes.mjs';\nimport './element-x4z00URv.mjs';\nimport './recycle-view-repeater-strategy-DoWdPqVw.mjs';\nimport './data-source-D34wiQZj.mjs';\n\n/**\n * Alternative to OverlayContainer that supports correct displaying of overlay elements in\n * Fullscreen mode\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/requestFullScreen\n *\n * Should be provided in the root component.\n */\nlet FullscreenOverlayContainer = /*#__PURE__*/(() => {\n  class FullscreenOverlayContainer extends OverlayContainer {\n    _renderer = inject(RendererFactory2).createRenderer(null, null);\n    _fullScreenEventName;\n    _cleanupFullScreenListener;\n    constructor() {\n      super();\n    }\n    ngOnDestroy() {\n      super.ngOnDestroy();\n      this._cleanupFullScreenListener?.();\n    }\n    _createContainer() {\n      const eventName = this._getEventName();\n      super._createContainer();\n      this._adjustParentForFullscreenChange();\n      if (eventName) {\n        this._cleanupFullScreenListener?.();\n        this._cleanupFullScreenListener = this._renderer.listen('document', eventName, () => {\n          this._adjustParentForFullscreenChange();\n        });\n      }\n    }\n    _adjustParentForFullscreenChange() {\n      if (this._containerElement) {\n        const fullscreenElement = this.getFullscreenElement();\n        const parent = fullscreenElement || this._document.body;\n        parent.appendChild(this._containerElement);\n      }\n    }\n    _getEventName() {\n      if (!this._fullScreenEventName) {\n        const _document = this._document;\n        if (_document.fullscreenEnabled) {\n          this._fullScreenEventName = 'fullscreenchange';\n        } else if (_document.webkitFullscreenEnabled) {\n          this._fullScreenEventName = 'webkitfullscreenchange';\n        } else if (_document.mozFullScreenEnabled) {\n          this._fullScreenEventName = 'mozfullscreenchange';\n        } else if (_document.msFullscreenEnabled) {\n          this._fullScreenEventName = 'MSFullscreenChange';\n        }\n      }\n      return this._fullScreenEventName;\n    }\n    /**\n     * When the page is put into fullscreen mode, a specific element is specified.\n     * Only that element and its children are visible when in fullscreen mode.\n     */\n    getFullscreenElement() {\n      const _document = this._document;\n      return _document.fullscreenElement || _document.webkitFullscreenElement || _document.mozFullScreenElement || _document.msFullscreenElement || null;\n    }\n    static ɵfac = function FullscreenOverlayContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FullscreenOverlayContainer)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: FullscreenOverlayContainer,\n      factory: FullscreenOverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return FullscreenOverlayContainer;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { FullscreenOverlayContainer, OverlayContainer };\n//# sourceMappingURL=overlay.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}