<div class="profile-container">
  <div class="container">
    <h1>Profile & Security Settings</h1>
    
    <mat-card>
      <mat-card-header>
        <mat-card-title>User Information</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="user-info">
          <div class="user-avatar" *ngIf="currentUser?.avatarUrl">
            <img [src]="currentUser!.avatarUrl" [alt]="(currentUser!.firstName || '') + ' ' + (currentUser!.lastName || '')">
          </div>
          <div class="user-details">
            <p><strong>Name:</strong> {{ currentUser?.firstName }} {{ currentUser?.lastName }}</p>
            <p><strong>Email:</strong> {{ currentUser?.email }}</p>
            <p><strong>Phone:</strong> {{ currentUser?.phone || 'Not provided' }}</p>
            <p><strong>Member since:</strong> {{ currentUser?.createdAt | date:'mediumDate' }}</p>

            <!-- OAuth Provider Info -->
            <div *ngIf="isOAuthUser()" class="oauth-info">
              <p><strong>Connected via:</strong>
                <span class="oauth-provider">
                  <i [class]="getOAuthProviderIcon()" [style.color]="getOAuthProviderColor()"></i>
                  {{ getOAuthProviderName() }}
                </span>
              </p>
            </div>

            <!-- Account Status -->
            <div class="account-status">
              <div class="status-chips">
                <div class="status-chip" [class.verified]="currentUser?.emailVerified" [class.unverified]="!currentUser?.emailVerified">
                  <mat-icon>{{ currentUser?.emailVerified ? 'verified' : 'warning' }}</mat-icon>
                  <span>Email {{ currentUser?.emailVerified ? 'Verified' : 'Unverified' }}</span>
                </div>
                <div class="status-chip" [class.enabled]="twoFactorStatus.enabled" [class.disabled]="!twoFactorStatus.enabled">
                  <mat-icon>security</mat-icon>
                  <span>2FA {{ twoFactorStatus.enabled ? 'Enabled' : 'Disabled' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="primary">
          <mat-icon>edit</mat-icon>
          Edit Profile
        </button>
      </mat-card-actions>
    </mat-card>

    <mat-card>
      <mat-card-header>
        <mat-card-title>Security Settings</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>Manage your account security settings including two-factor authentication and password.</p>

        <!-- Change Password Section -->
        <div class="security-section">
          <div class="section-header">
            <h3>Password</h3>
            <button mat-button color="primary" (click)="toggleChangePassword()" *ngIf="!isOAuthUser()">
              <mat-icon>{{ showChangePassword ? 'expand_less' : 'expand_more' }}</mat-icon>
              {{ showChangePassword ? 'Cancel' : 'Change Password' }}
            </button>
            <div *ngIf="isOAuthUser()" class="oauth-password-notice">
              <mat-icon color="warn">info</mat-icon>
              <span>Password managed by {{ getOAuthProviderName() }}</span>
            </div>
          </div>

          <!-- Change Password Form -->
          <div *ngIf="showChangePassword && !isOAuthUser()" class="change-password-form">
            <form [formGroup]="changePasswordForm" (ngSubmit)="onChangePassword()">
              <mat-form-field class="form-field" appearance="outline">
                <mat-label>Current Password</mat-label>
                <input matInput [type]="hideCurrentPassword ? 'password' : 'text'"
                       formControlName="currentPassword" autocomplete="current-password">
                <button mat-icon-button matSuffix (click)="hideCurrentPassword = !hideCurrentPassword" type="button">
                  <mat-icon>{{ hideCurrentPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error>{{ getFieldError('currentPassword') }}</mat-error>
              </mat-form-field>

              <mat-form-field class="form-field" appearance="outline">
                <mat-label>New Password</mat-label>
                <input matInput [type]="hideNewPassword ? 'password' : 'text'"
                       formControlName="newPassword" autocomplete="new-password">
                <button mat-icon-button matSuffix (click)="hideNewPassword = !hideNewPassword" type="button">
                  <mat-icon>{{ hideNewPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error>{{ getFieldError('newPassword') }}</mat-error>
              </mat-form-field>

              <mat-form-field class="form-field" appearance="outline">
                <mat-label>Confirm New Password</mat-label>
                <input matInput [type]="hideConfirmPassword ? 'password' : 'text'"
                       formControlName="confirmPassword" autocomplete="new-password">
                <button mat-icon-button matSuffix (click)="hideConfirmPassword = !hideConfirmPassword" type="button">
                  <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>
              </mat-form-field>

              <!-- 2FA Token Field (if 2FA is enabled) -->
              <mat-form-field *ngIf="twoFactorStatus.enabled" class="form-field" appearance="outline">
                <mat-label>2FA Code (Required)</mat-label>
                <input matInput formControlName="twoFactorToken" placeholder="000000"
                       maxlength="6" autocomplete="one-time-code">
                <mat-icon matSuffix>verified_user</mat-icon>
                <mat-hint>Enter the 6-digit code from your authenticator app</mat-hint>
                <mat-error>{{ getFieldError('twoFactorToken') }}</mat-error>
              </mat-form-field>

              <div class="form-actions">
                <button mat-raised-button color="primary" type="submit" [disabled]="loading">
                  <mat-spinner *ngIf="loading" diameter="20"></mat-spinner>
                  <span *ngIf="!loading">Change Password</span>
                </button>
                <button mat-button type="button" (click)="toggleChangePassword()">
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-button color="accent" routerLink="/dashboard/two-factor">
          <mat-icon>security</mat-icon>
          {{ twoFactorStatus.enabled ? 'Manage 2FA' : 'Setup 2FA' }}
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div>
