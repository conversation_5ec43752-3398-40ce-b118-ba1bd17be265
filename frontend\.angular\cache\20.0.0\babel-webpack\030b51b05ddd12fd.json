{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, DOCUMENT, signal, EventEmitter, Injectable } from '@angular/core';\n\n/**\n * Injection token used to inject the document into Directionality.\n * This is used so that the value can be faked in tests.\n *\n * We can't use the real document in tests because changing the real `dir` causes geometry-based\n * tests in Safari to fail.\n *\n * We also can't re-provide the DOCUMENT token from platform-browser because the unit tests\n * themselves use things like `querySelector` in test code.\n *\n * This token is defined in a separate file from Directionality as a workaround for\n * https://github.com/angular/angular/issues/22559\n *\n * @docs-private\n */\nconst DIR_DOCUMENT = /*#__PURE__*/new InjectionToken('cdk-dir-doc', {\n  providedIn: 'root',\n  factory: DIR_DOCUMENT_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction DIR_DOCUMENT_FACTORY() {\n  return inject(DOCUMENT);\n}\n\n/** Regex that matches locales with an RTL script. Taken from `goog.i18n.bidi.isRtlLanguage`. */\nconst RTL_LOCALE_PATTERN = /^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;\n/** Resolves a string value to a specific direction. */\nfunction _resolveDirectionality(rawValue) {\n  const value = rawValue?.toLowerCase() || '';\n  if (value === 'auto' && typeof navigator !== 'undefined' && navigator?.language) {\n    return RTL_LOCALE_PATTERN.test(navigator.language) ? 'rtl' : 'ltr';\n  }\n  return value === 'rtl' ? 'rtl' : 'ltr';\n}\n/**\n * The directionality (LTR / RTL) context for the application (or a subtree of it).\n * Exposes the current direction and a stream of direction changes.\n */\nlet Directionality = /*#__PURE__*/(() => {\n  class Directionality {\n    /** The current 'ltr' or 'rtl' value. */\n    get value() {\n      return this.valueSignal();\n    }\n    /**\n     * The current 'ltr' or 'rtl' value.\n     */\n    valueSignal = signal('ltr');\n    /** Stream that emits whenever the 'ltr' / 'rtl' state changes. */\n    change = new EventEmitter();\n    constructor() {\n      const _document = inject(DIR_DOCUMENT, {\n        optional: true\n      });\n      if (_document) {\n        const bodyDir = _document.body ? _document.body.dir : null;\n        const htmlDir = _document.documentElement ? _document.documentElement.dir : null;\n        this.valueSignal.set(_resolveDirectionality(bodyDir || htmlDir || 'ltr'));\n      }\n    }\n    ngOnDestroy() {\n      this.change.complete();\n    }\n    static ɵfac = function Directionality_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Directionality)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Directionality,\n      factory: Directionality.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return Directionality;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { Directionality as D, _resolveDirectionality as _, DIR_DOCUMENT as a };\n//# sourceMappingURL=directionality-CChdj3az.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}