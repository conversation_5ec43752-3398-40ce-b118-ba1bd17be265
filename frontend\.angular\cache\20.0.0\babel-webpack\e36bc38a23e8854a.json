{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { environment } from '../../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/payment.service\";\nimport * as i3 from \"../../../services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/divider\";\nfunction PaymentTestComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PaymentTestComponent_button_19_Template_button_click_0_listener() {\n      const testAmount_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectTestAmount(testAmount_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const testAmount_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"color\", ((tmp_2_0 = ctx_r2.paymentForm.get(\"currency\")) == null ? null : tmp_2_0.value) === testAmount_r2.currency ? \"primary\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", testAmount_r2.label, \" \");\n  }\n}\nfunction PaymentTestComponent_mat_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const currency_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", currency_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", currency_r4, \" \");\n  }\n}\nfunction PaymentTestComponent_mat_hint_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-hint\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getEquivalentAmount());\n  }\n}\nfunction PaymentTestComponent_mat_spinner_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 29);\n  }\n}\nfunction PaymentTestComponent_mat_icon_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"lock\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentTestComponent_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Pay Securely with Razorpay\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PaymentTestComponent_mat_card_57_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"div\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 36)(5, \"div\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 38);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 39);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 40)(13, \"div\", 41)(14, \"mat-icon\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"titlecase\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const payment_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatCurrency(payment_r5.amount, payment_r5.currency), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(payment_r5.description || \"Test Payment\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 7, payment_r5.createdAt, \"medium\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"ID: \", payment_r5.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", \"status-\" + payment_r5.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusIcon(payment_r5.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 10, payment_r5.status), \" \");\n  }\n}\nfunction PaymentTestComponent_mat_card_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 30)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Payment History \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7, \" Your recent test transactions \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 31);\n    i0.ɵɵtemplate(10, PaymentTestComponent_mat_card_57_div_10_Template, 18, 12, \"div\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.payments);\n  }\n}\nexport let PaymentTestComponent = /*#__PURE__*/(() => {\n  class PaymentTestComponent {\n    constructor(formBuilder, paymentService, authService, snackBar) {\n      this.formBuilder = formBuilder;\n      this.paymentService = paymentService;\n      this.authService = authService;\n      this.snackBar = snackBar;\n      this.loading = false;\n      this.payments = [];\n      this.supportedCurrencies = environment.razorpay.supportedCurrencies;\n      this.testAmounts = [{\n        amount: 100,\n        currency: 'INR',\n        label: '₹100 - Basic Test'\n      }, {\n        amount: 500,\n        currency: 'INR',\n        label: '₹500 - Standard Test'\n      }, {\n        amount: 1000,\n        currency: 'INR',\n        label: '₹1,000 - Premium Test'\n      }, {\n        amount: 5,\n        currency: 'USD',\n        label: '$5 - Basic Test'\n      }, {\n        amount: 25,\n        currency: 'USD',\n        label: '$25 - Standard Test'\n      }, {\n        amount: 50,\n        currency: 'USD',\n        label: '$50 - Premium Test'\n      }];\n      this.paymentForm = this.formBuilder.group({\n        amount: ['', [Validators.required, Validators.min(1), Validators.max(1000000)]],\n        currency: [environment.razorpay.currency, [Validators.required]],\n        description: ['Payment test transaction']\n      });\n    }\n    ngOnInit() {\n      this.loadPaymentHistory();\n    }\n    onSubmit() {\n      if (this.paymentForm.invalid) {\n        this.markFormGroupTouched(this.paymentForm);\n        return;\n      }\n      const paymentRequest = this.paymentForm.value;\n      const validation = this.paymentService.validateAmount(paymentRequest.amount, paymentRequest.currency);\n      if (!validation.valid) {\n        this.snackBar.open(validation.error, 'Close', {\n          duration: 5000\n        });\n        return;\n      }\n      this.loading = true;\n      this.paymentService.processPayment(paymentRequest).subscribe({\n        next: response => {\n          if (response.success) {\n            this.snackBar.open('Payment completed successfully!', 'Close', {\n              duration: 5000\n            });\n            this.loadPaymentHistory();\n            this.paymentForm.patchValue({\n              amount: '',\n              description: 'Payment test transaction'\n            });\n          } else {\n            this.snackBar.open(response.message || 'Payment failed', 'Close', {\n              duration: 5000\n            });\n          }\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'Payment processing failed', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    selectTestAmount(testAmount) {\n      this.paymentForm.patchValue({\n        amount: testAmount.amount,\n        currency: testAmount.currency,\n        description: `Test payment - ${testAmount.label}`\n      });\n    }\n    loadPaymentHistory() {\n      this.paymentService.getMyPayments().subscribe({\n        next: response => {\n          this.payments = response.payments;\n        },\n        error: error => {\n          console.error('Failed to load payment history:', error);\n        }\n      });\n    }\n    formatCurrency(amount, currency) {\n      return this.paymentService.formatCurrency(amount, currency);\n    }\n    getStatusIcon(status) {\n      return this.paymentService.getStatusIcon(status);\n    }\n    getFieldError(fieldName) {\n      const field = this.paymentForm.get(fieldName);\n      if (field?.errors && field.touched) {\n        if (field.errors['required']) return `${fieldName} is required`;\n        if (field.errors['min']) return `Minimum amount is ${field.errors['min'].min}`;\n        if (field.errors['max']) return `Maximum amount is ${field.errors['max'].max}`;\n      }\n      return '';\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n      });\n    }\n    convertAmount(amount, fromCurrency, toCurrency) {\n      return this.paymentService.convertCurrency(amount, fromCurrency, toCurrency);\n    }\n    getEquivalentAmount() {\n      const amount = this.paymentForm.get('amount')?.value;\n      const currency = this.paymentForm.get('currency')?.value;\n      if (!amount || !currency) return '';\n      const otherCurrency = currency === 'INR' ? 'USD' : 'INR';\n      const convertedAmount = this.convertAmount(amount, currency, otherCurrency);\n      return `≈ ${this.formatCurrency(convertedAmount, otherCurrency)}`;\n    }\n    static #_ = this.ɵfac = function PaymentTestComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaymentTestComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.PaymentService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PaymentTestComponent,\n      selectors: [[\"app-payment-test\"]],\n      standalone: false,\n      decls: 84,\n      vars: 11,\n      consts: [[1, \"payment-container\"], [1, \"container\"], [1, \"subtitle\"], [1, \"payment-card\"], [1, \"test-amounts\"], [1, \"amount-buttons\"], [\"mat-stroked-button\", \"\", 3, \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"my-3\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"currency-field\"], [\"formControlName\", \"currency\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\", 1, \"amount-field\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"amount\", \"min\", \"1\", \"step\", \"0.01\"], [4, \"ngIf\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"maxlength\", \"100\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"payment-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"security-notice\"], [\"class\", \"payment-history\", 4, \"ngIf\"], [1, \"testing-guide\"], [1, \"guide-section\"], [1, \"test-cards\"], [1, \"test-card\"], [\"mat-stroked-button\", \"\", 3, \"click\", \"color\"], [3, \"value\"], [\"diameter\", \"20\"], [1, \"payment-history\"], [1, \"payment-list\"], [\"class\", \"payment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"payment-item\"], [1, \"payment-info\"], [1, \"payment-amount\"], [1, \"payment-details\"], [1, \"payment-description\"], [1, \"payment-date\"], [1, \"payment-id\"], [1, \"payment-actions\"], [1, \"payment-status\", 3, \"ngClass\"]],\n      template: function PaymentTestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Payment Testing Interface\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 2);\n          i0.ɵɵtext(5, \"Test Razorpay integration with secure payment processing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"mat-card\", 3)(7, \"mat-card-header\")(8, \"mat-card-title\")(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"payment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Make a Test Payment \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-card-subtitle\");\n          i0.ɵɵtext(13, \" Test payments using Razorpay's secure checkout \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"mat-card-content\")(15, \"div\", 4)(16, \"h3\");\n          i0.ɵɵtext(17, \"Quick Test Amounts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 5);\n          i0.ɵɵtemplate(19, PaymentTestComponent_button_19_Template, 2, 2, \"button\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(20, \"mat-divider\", 7);\n          i0.ɵɵelementStart(21, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function PaymentTestComponent_Template_form_ngSubmit_21_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(22, \"div\", 9)(23, \"mat-form-field\", 10)(24, \"mat-label\");\n          i0.ɵɵtext(25, \"Currency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"mat-select\", 11);\n          i0.ɵɵtemplate(27, PaymentTestComponent_mat_option_27_Template, 2, 2, \"mat-option\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-icon\", 13);\n          i0.ɵɵtext(29, \"attach_money\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"mat-form-field\", 14)(31, \"mat-label\");\n          i0.ɵɵtext(32, \"Amount\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"input\", 15);\n          i0.ɵɵelementStart(34, \"span\", 13);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"mat-error\");\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(38, PaymentTestComponent_mat_hint_38_Template, 2, 1, \"mat-hint\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 17)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Description (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 18);\n          i0.ɵɵelementStart(43, \"mat-icon\", 13);\n          i0.ɵɵtext(44, \"description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"button\", 19);\n          i0.ɵɵtemplate(46, PaymentTestComponent_mat_spinner_46_Template, 1, 0, \"mat-spinner\", 20)(47, PaymentTestComponent_mat_icon_47_Template, 2, 0, \"mat-icon\", 16)(48, PaymentTestComponent_span_48_Template, 2, 0, \"span\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 21)(50, \"mat-icon\");\n          i0.ɵɵtext(51, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\")(53, \"strong\");\n          i0.ɵɵtext(54, \"Secure Payment Processing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"p\");\n          i0.ɵɵtext(56, \"All payments are processed securely through Razorpay's PCI DSS compliant platform.\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(57, PaymentTestComponent_mat_card_57_Template, 11, 1, \"mat-card\", 22);\n          i0.ɵɵelementStart(58, \"mat-card\", 23)(59, \"mat-card-header\")(60, \"mat-card-title\")(61, \"mat-icon\");\n          i0.ɵɵtext(62, \"help\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63, \" Testing Guide \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"mat-card-content\")(65, \"div\", 24)(66, \"h4\");\n          i0.ɵɵtext(67, \"Test Card Numbers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 25)(69, \"div\", 26)(70, \"strong\");\n          i0.ɵɵtext(71, \"Visa:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(72, \" ************** 1111 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 26)(74, \"strong\");\n          i0.ɵɵtext(75, \"Mastercard:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(76, \" ************** 4444 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"p\")(78, \"strong\");\n          i0.ɵɵtext(79, \"CVV:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(80, \" Any 3-4 digit number | \");\n          i0.ɵɵelementStart(81, \"strong\");\n          i0.ɵɵtext(82, \"Expiry:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(83, \" Any future date\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_3_0;\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngForOf\", ctx.testAmounts);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.paymentForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.supportedCurrencies);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(((tmp_3_0 = ctx.paymentForm.get(\"currency\")) == null ? null : tmp_3_0.value) === \"INR\" ? \"\\u20B9\" : \"$\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"amount\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.getEquivalentAmount());\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.payments.length > 0);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatFormField, i7.MatLabel, i7.MatHint, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i10.MatIcon, i11.MatSelect, i11.MatOption, i12.MatProgressSpinner, i13.MatDivider, i5.TitleCasePipe, i5.DatePipe],\n      styles: [\".my-3[_ngcontent-%COMP%]{margin:1.5rem 0}.form-row[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-bottom:1rem}.form-row[_ngcontent-%COMP%]   .currency-field[_ngcontent-%COMP%]{flex:0 0 120px}.form-row[_ngcontent-%COMP%]   .amount-field[_ngcontent-%COMP%]{flex:1}.test-amounts[_ngcontent-%COMP%]{margin-bottom:1.5rem}.test-amounts[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:1rem;color:#333}.test-amounts[_ngcontent-%COMP%]   .amount-buttons[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:.5rem}.test-amounts[_ngcontent-%COMP%]   .amount-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:.75rem;font-size:.875rem}.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]{margin-bottom:1.5rem}.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#333;margin-bottom:.75rem}.testing-guide[_ngcontent-%COMP%]   .guide-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0;color:#666}.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:.5rem;margin-bottom:1rem}.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]   .test-card[_ngcontent-%COMP%]{background:#f8f9fa;padding:.75rem;border-radius:6px;font-family:monospace;font-size:.875rem}.testing-guide[_ngcontent-%COMP%]   .test-cards[_ngcontent-%COMP%]   .test-card[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333}@media (max-width: 768px){.form-row[_ngcontent-%COMP%]{flex-direction:column;gap:0}.form-row[_ngcontent-%COMP%]   .currency-field[_ngcontent-%COMP%]{flex:1}.amount-buttons[_ngcontent-%COMP%], .test-cards[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n    });\n  }\n  return PaymentTestComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}