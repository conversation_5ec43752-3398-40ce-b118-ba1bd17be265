{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/augment/Fullstack/Modular backend secure user system and payment/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { inject, RuntimeError, formatRuntimeError, ErrorHandler, DestroyRef, assertNotInReactiveContext, assertInInjectionContext, Injector, ViewContext, ChangeDetectionScheduler, EffectScheduler, setInjectorProfilerContext, emitEffectCreatedEvent, EFFECTS, noop, FLAGS, markAncestorsForTraversal, setIsRefreshingViews, NodeInjectorDestroyRef, signalAsReadonlyFn, PendingTasks, signal } from './root_effect_scheduler-0BxwqIgm.mjs';\nimport { setActiveConsumer, createComputed, SIGNAL, REACTIVE_NODE, consumerDestroy, isInNotificationPhase, consumerPollProducersForChange, consumerBeforeComputation, consumerAfterComputation } from './signal-ePSl6jXn.mjs';\nimport { untracked as untracked$1, createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn } from './untracked-2ouAFbCz.mjs';\n\n/**\n * An `OutputEmitterRef` is created by the `output()` function and can be\n * used to emit values to consumers of your directive or component.\n *\n * Consumers of your directive/component can bind to the output and\n * subscribe to changes via the bound event syntax. For example:\n *\n * ```html\n * <my-comp (valueChange)=\"processNewValue($event)\" />\n * ```\n *\n * @publicAPI\n */\nclass OutputEmitterRef {\n  destroyed = false;\n  listeners = null;\n  errorHandler = /*#__PURE__*/inject(ErrorHandler, {\n    optional: true\n  });\n  /** @internal */\n  destroyRef = /*#__PURE__*/inject(DestroyRef);\n  constructor() {\n    // Clean-up all listeners and mark as destroyed upon destroy.\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n      this.listeners = null;\n    });\n  }\n  subscribe(callback) {\n    if (this.destroyed) {\n      throw new RuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    (this.listeners ??= []).push(callback);\n    return {\n      unsubscribe: () => {\n        const idx = this.listeners?.indexOf(callback);\n        if (idx !== undefined && idx !== -1) {\n          this.listeners?.splice(idx, 1);\n        }\n      }\n    };\n  }\n  /** Emits a new value to the output. */\n  emit(value) {\n    if (this.destroyed) {\n      console.warn(formatRuntimeError(953 /* RuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected emit for destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.'));\n      return;\n    }\n    if (this.listeners === null) {\n      return;\n    }\n    const previousConsumer = setActiveConsumer(null);\n    try {\n      for (const listenerFn of this.listeners) {\n        try {\n          listenerFn(value);\n        } catch (err) {\n          this.errorHandler?.handleError(err);\n        }\n      }\n    } finally {\n      setActiveConsumer(previousConsumer);\n    }\n  }\n}\n/** Gets the owning `DestroyRef` for the given output. */\nfunction getOutputDestroyRef(ref) {\n  return ref.destroyRef;\n}\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n  return untracked$1(nonReactiveReadsFn);\n}\n\n/**\n * Create a computed `Signal` which derives a reactive value from an expression.\n */\nfunction computed(computation, options) {\n  const getter = createComputed(computation, options?.equal);\n  if (ngDevMode) {\n    getter.toString = () => `[Computed: ${getter()}]`;\n    getter[SIGNAL].debugName = options?.debugName;\n  }\n  return getter;\n}\nclass EffectRefImpl {\n  [SIGNAL];\n  constructor(node) {\n    this[SIGNAL] = node;\n  }\n  destroy() {\n    this[SIGNAL].destroy();\n  }\n}\n/**\n * Registers an \"effect\" that will be scheduled & executed whenever the signals that it reads\n * changes.\n *\n * Angular has two different kinds of effect: component effects and root effects. Component effects\n * are created when `effect()` is called from a component, directive, or within a service of a\n * component/directive. Root effects are created when `effect()` is called from outside the\n * component tree, such as in a root service.\n *\n * The two effect types differ in their timing. Component effects run as a component lifecycle\n * event during Angular's synchronization (change detection) process, and can safely read input\n * signals or create/destroy views that depend on component state. Root effects run as microtasks\n * and have no connection to the component tree or change detection.\n *\n * `effect()` must be run in injection context, unless the `injector` option is manually specified.\n *\n * @publicApi 20.0\n */\nfunction effect(effectFn, options) {\n  ngDevMode && assertNotInReactiveContext(effect, 'Call `effect` outside of a reactive context. For example, schedule the ' + 'effect inside the component constructor.');\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(effect);\n  }\n  if (ngDevMode && options?.allowSignalWrites !== undefined) {\n    console.warn(`The 'allowSignalWrites' flag is deprecated and no longer impacts effect() (writes are always allowed)`);\n  }\n  const injector = options?.injector ?? inject(Injector);\n  let destroyRef = options?.manualCleanup !== true ? injector.get(DestroyRef) : null;\n  let node;\n  const viewContext = injector.get(ViewContext, null, {\n    optional: true\n  });\n  const notifier = injector.get(ChangeDetectionScheduler);\n  if (viewContext !== null) {\n    // This effect was created in the context of a view, and will be associated with the view.\n    node = createViewEffect(viewContext.view, notifier, effectFn);\n    if (destroyRef instanceof NodeInjectorDestroyRef && destroyRef._lView === viewContext.view) {\n      // The effect is being created in the same view as the `DestroyRef` references, so it will be\n      // automatically destroyed without the need for an explicit `DestroyRef` registration.\n      destroyRef = null;\n    }\n  } else {\n    // This effect was created outside the context of a view, and will be scheduled independently.\n    node = createRootEffect(effectFn, injector.get(EffectScheduler), notifier);\n  }\n  node.injector = injector;\n  if (destroyRef !== null) {\n    // If we need to register for cleanup, do that here.\n    node.onDestroyFn = destroyRef.onDestroy(() => node.destroy());\n  }\n  const effectRef = new EffectRefImpl(node);\n  if (ngDevMode) {\n    node.debugName = options?.debugName ?? '';\n    const prevInjectorProfilerContext = setInjectorProfilerContext({\n      injector,\n      token: null\n    });\n    try {\n      emitEffectCreatedEvent(effectRef);\n    } finally {\n      setInjectorProfilerContext(prevInjectorProfilerContext);\n    }\n  }\n  return effectRef;\n}\nconst BASE_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...REACTIVE_NODE,\n  consumerIsAlwaysLive: true,\n  consumerAllowSignalWrites: true,\n  dirty: true,\n  hasRun: false,\n  cleanupFns: undefined,\n  zone: null,\n  kind: 'effect',\n  onDestroyFn: noop,\n  run() {\n    this.dirty = false;\n    if (ngDevMode && isInNotificationPhase()) {\n      throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n    }\n    if (this.hasRun && !consumerPollProducersForChange(this)) {\n      return;\n    }\n    this.hasRun = true;\n    const registerCleanupFn = cleanupFn => (this.cleanupFns ??= []).push(cleanupFn);\n    const prevNode = consumerBeforeComputation(this);\n    // We clear `setIsRefreshingViews` so that `markForCheck()` within the body of an effect will\n    // cause CD to reach the component in question.\n    const prevRefreshingViews = setIsRefreshingViews(false);\n    try {\n      this.maybeCleanup();\n      this.fn(registerCleanupFn);\n    } finally {\n      setIsRefreshingViews(prevRefreshingViews);\n      consumerAfterComputation(this, prevNode);\n    }\n  },\n  maybeCleanup() {\n    if (!this.cleanupFns?.length) {\n      return;\n    }\n    const prevConsumer = setActiveConsumer(null);\n    try {\n      // Attempt to run the cleanup functions. Regardless of failure or success, we consider\n      // cleanup \"completed\" and clear the list for the next run of the effect. Note that an error\n      // from the cleanup function will still crash the current run of the effect.\n      while (this.cleanupFns.length) {\n        this.cleanupFns.pop()();\n      }\n    } finally {\n      this.cleanupFns = [];\n      setActiveConsumer(prevConsumer);\n    }\n  }\n}))();\nconst ROOT_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...BASE_EFFECT_NODE,\n  consumerMarkedDirty() {\n    this.scheduler.schedule(this);\n    this.notifier.notify(12 /* NotificationSource.RootEffect */);\n  },\n  destroy() {\n    consumerDestroy(this);\n    this.onDestroyFn();\n    this.maybeCleanup();\n    this.scheduler.remove(this);\n  }\n}))();\nconst VIEW_EFFECT_NODE = /* @__PURE__ */(() => ({\n  ...BASE_EFFECT_NODE,\n  consumerMarkedDirty() {\n    this.view[FLAGS] |= 8192 /* LViewFlags.HasChildViewsToRefresh */;\n    markAncestorsForTraversal(this.view);\n    this.notifier.notify(13 /* NotificationSource.ViewEffect */);\n  },\n  destroy() {\n    consumerDestroy(this);\n    this.onDestroyFn();\n    this.maybeCleanup();\n    this.view[EFFECTS]?.delete(this);\n  }\n}))();\nfunction createViewEffect(view, notifier, fn) {\n  const node = Object.create(VIEW_EFFECT_NODE);\n  node.view = view;\n  node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n  node.notifier = notifier;\n  node.fn = fn;\n  view[EFFECTS] ??= new Set();\n  view[EFFECTS].add(node);\n  node.consumerMarkedDirty(node);\n  return node;\n}\nfunction createRootEffect(fn, scheduler, notifier) {\n  const node = Object.create(ROOT_EFFECT_NODE);\n  node.fn = fn;\n  node.scheduler = scheduler;\n  node.notifier = notifier;\n  node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n  node.scheduler.add(node);\n  node.notifier.notify(12 /* NotificationSource.RootEffect */);\n  return node;\n}\nconst identityFn = v => v;\nfunction linkedSignal(optionsOrComputation, options) {\n  if (typeof optionsOrComputation === 'function') {\n    const getter = createLinkedSignal(optionsOrComputation, identityFn, options?.equal);\n    return upgradeLinkedSignalGetter(getter);\n  } else {\n    const getter = createLinkedSignal(optionsOrComputation.source, optionsOrComputation.computation, optionsOrComputation.equal);\n    return upgradeLinkedSignalGetter(getter);\n  }\n}\nfunction upgradeLinkedSignalGetter(getter) {\n  if (ngDevMode) {\n    getter.toString = () => `[LinkedSignal: ${getter()}]`;\n  }\n  const node = getter[SIGNAL];\n  const upgradedGetter = getter;\n  upgradedGetter.set = newValue => linkedSignalSetFn(node, newValue);\n  upgradedGetter.update = updateFn => linkedSignalUpdateFn(node, updateFn);\n  upgradedGetter.asReadonly = signalAsReadonlyFn.bind(getter);\n  return upgradedGetter;\n}\n\n/**\n * Whether a `Resource.value()` should throw an error when the resource is in the error state.\n *\n * This internal flag is being used to gradually roll out this behavior.\n */\nconst RESOURCE_VALUE_THROWS_ERRORS_DEFAULT = true;\nfunction resource(options) {\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(resource);\n  }\n  const oldNameForParams = options.request;\n  const params = options.params ?? oldNameForParams ?? (() => null);\n  return new ResourceImpl(params, getLoader(options), options.defaultValue, options.equal ? wrapEqualityFn(options.equal) : undefined, options.injector ?? inject(Injector), RESOURCE_VALUE_THROWS_ERRORS_DEFAULT);\n}\n/**\n * Base class which implements `.value` as a `WritableSignal` by delegating `.set` and `.update`.\n */\nclass BaseWritableResource {\n  value;\n  constructor(value) {\n    this.value = value;\n    this.value.set = this.set.bind(this);\n    this.value.update = this.update.bind(this);\n    this.value.asReadonly = signalAsReadonlyFn;\n  }\n  isError = /*#__PURE__*/computed(() => this.status() === 'error');\n  update(updateFn) {\n    this.set(updateFn(untracked(this.value)));\n  }\n  isLoading = /*#__PURE__*/computed(() => this.status() === 'loading' || this.status() === 'reloading');\n  hasValue() {\n    // Note: we specifically read `isError()` instead of `status()` here to avoid triggering\n    // reactive consumers which read `hasValue()`. This way, if `hasValue()` is used inside of an\n    // effect, it doesn't cause the effect to rerun on every status change.\n    if (this.isError()) {\n      return false;\n    }\n    return this.value() !== undefined;\n  }\n  asReadonly() {\n    return this;\n  }\n}\n/**\n * Implementation for `resource()` which uses a `linkedSignal` to manage the resource's state.\n */\nclass ResourceImpl extends BaseWritableResource {\n  loaderFn;\n  equal;\n  pendingTasks;\n  /**\n   * The current state of the resource. Status, value, and error are derived from this.\n   */\n  state;\n  /**\n   * Combines the current request with a reload counter which allows the resource to be reloaded on\n   * imperative command.\n   */\n  extRequest;\n  effectRef;\n  pendingController;\n  resolvePendingTask = undefined;\n  destroyed = false;\n  constructor(request, loaderFn, defaultValue, equal, injector, throwErrorsFromValue = RESOURCE_VALUE_THROWS_ERRORS_DEFAULT) {\n    super(\n    // Feed a computed signal for the value to `BaseWritableResource`, which will upgrade it to a\n    // `WritableSignal` that delegates to `ResourceImpl.set`.\n    computed(() => {\n      const streamValue = this.state().stream?.();\n      if (!streamValue) {\n        return defaultValue;\n      }\n      // Prevents `hasValue()` from throwing an error when a reload happened in the error state\n      if (this.state().status === 'loading' && this.error()) {\n        return defaultValue;\n      }\n      if (!isResolved(streamValue)) {\n        if (throwErrorsFromValue) {\n          throw new ResourceValueError(this.error());\n        } else {\n          return defaultValue;\n        }\n      }\n      return streamValue.value;\n    }, {\n      equal\n    }));\n    this.loaderFn = loaderFn;\n    this.equal = equal;\n    // Extend `request()` to include a writable reload signal.\n    this.extRequest = linkedSignal({\n      source: request,\n      computation: request => ({\n        request,\n        reload: 0\n      })\n    });\n    // The main resource state is managed in a `linkedSignal`, which allows the resource to change\n    // state instantaneously when the request signal changes.\n    this.state = linkedSignal({\n      // Whenever the request changes,\n      source: this.extRequest,\n      // Compute the state of the resource given a change in status.\n      computation: (extRequest, previous) => {\n        const status = extRequest.request === undefined ? 'idle' : 'loading';\n        if (!previous) {\n          return {\n            extRequest,\n            status,\n            previousStatus: 'idle',\n            stream: undefined\n          };\n        } else {\n          return {\n            extRequest,\n            status,\n            previousStatus: projectStatusOfState(previous.value),\n            // If the request hasn't changed, keep the previous stream.\n            stream: previous.value.extRequest.request === extRequest.request ? previous.value.stream : undefined\n          };\n        }\n      }\n    });\n    this.effectRef = effect(this.loadEffect.bind(this), {\n      injector,\n      manualCleanup: true\n    });\n    this.pendingTasks = injector.get(PendingTasks);\n    // Cancel any pending request when the resource itself is destroyed.\n    injector.get(DestroyRef).onDestroy(() => this.destroy());\n  }\n  status = /*#__PURE__*/computed(() => projectStatusOfState(this.state()));\n  error = /*#__PURE__*/computed(() => {\n    const stream = this.state().stream?.();\n    return stream && !isResolved(stream) ? stream.error : undefined;\n  });\n  /**\n   * Called either directly via `WritableResource.set` or via `.value.set()`.\n   */\n  set(value) {\n    if (this.destroyed) {\n      return;\n    }\n    const current = untracked(this.value);\n    const state = untracked(this.state);\n    if (state.status === 'local' && (this.equal ? this.equal(current, value) : current === value)) {\n      return;\n    }\n    // Enter Local state with the user-defined value.\n    this.state.set({\n      extRequest: state.extRequest,\n      status: 'local',\n      previousStatus: 'local',\n      stream: signal({\n        value\n      })\n    });\n    // We're departing from whatever state the resource was in previously, so cancel any in-progress\n    // loading operations.\n    this.abortInProgressLoad();\n  }\n  reload() {\n    // We don't want to restart in-progress loads.\n    const {\n      status\n    } = untracked(this.state);\n    if (status === 'idle' || status === 'loading') {\n      return false;\n    }\n    // Increment the request reload to trigger the `state` linked signal to switch us to `Reload`\n    this.extRequest.update(({\n      request,\n      reload\n    }) => ({\n      request,\n      reload: reload + 1\n    }));\n    return true;\n  }\n  destroy() {\n    this.destroyed = true;\n    this.effectRef.destroy();\n    this.abortInProgressLoad();\n    // Destroyed resources enter Idle state.\n    this.state.set({\n      extRequest: {\n        request: undefined,\n        reload: 0\n      },\n      status: 'idle',\n      previousStatus: 'idle',\n      stream: undefined\n    });\n  }\n  loadEffect() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const extRequest = _this.extRequest();\n      // Capture the previous status before any state transitions. Note that this is `untracked` since\n      // we do not want the effect to depend on the state of the resource, only on the request.\n      const {\n        status: currentStatus,\n        previousStatus\n      } = untracked(_this.state);\n      if (extRequest.request === undefined) {\n        // Nothing to load (and we should already be in a non-loading state).\n        return;\n      } else if (currentStatus !== 'loading') {\n        // We're not in a loading or reloading state, so this loading request is stale.\n        return;\n      }\n      // Cancel any previous loading attempts.\n      _this.abortInProgressLoad();\n      // Capturing _this_ load's pending task in a local variable is important here. We may attempt to\n      // resolve it twice:\n      //\n      //  1. when the loading function promise resolves/rejects\n      //  2. when cancelling the loading operation\n      //\n      // After the loading operation is cancelled, `this.resolvePendingTask` no longer represents this\n      // particular task, but this `await` may eventually resolve/reject. Thus, when we cancel in\n      // response to (1) below, we need to cancel the locally saved task.\n      let resolvePendingTask = _this.resolvePendingTask = _this.pendingTasks.add();\n      const {\n        signal: abortSignal\n      } = _this.pendingController = new AbortController();\n      try {\n        // The actual loading is run through `untracked` - only the request side of `resource` is\n        // reactive. This avoids any confusion with signals tracking or not tracking depending on\n        // which side of the `await` they are.\n        const stream = yield untracked(() => {\n          return _this.loaderFn({\n            params: extRequest.request,\n            // TODO(alxhub): cleanup after g3 removal of `request` alias.\n            request: extRequest.request,\n            abortSignal,\n            previous: {\n              status: previousStatus\n            }\n          });\n        });\n        // If this request has been aborted, or the current request no longer\n        // matches this load, then we should ignore this resolution.\n        if (abortSignal.aborted || untracked(_this.extRequest) !== extRequest) {\n          return;\n        }\n        _this.state.set({\n          extRequest,\n          status: 'resolved',\n          previousStatus: 'resolved',\n          stream\n        });\n      } catch (err) {\n        if (abortSignal.aborted || untracked(_this.extRequest) !== extRequest) {\n          return;\n        }\n        _this.state.set({\n          extRequest,\n          status: 'resolved',\n          previousStatus: 'error',\n          stream: signal({\n            error: encapsulateResourceError(err)\n          })\n        });\n      } finally {\n        // Resolve the pending task now that the resource has a value.\n        resolvePendingTask?.();\n        resolvePendingTask = undefined;\n      }\n    })();\n  }\n  abortInProgressLoad() {\n    untracked(() => this.pendingController?.abort());\n    this.pendingController = undefined;\n    // Once the load is aborted, we no longer want to block stability on its resolution.\n    this.resolvePendingTask?.();\n    this.resolvePendingTask = undefined;\n  }\n}\n/**\n * Wraps an equality function to handle either value being `undefined`.\n */\nfunction wrapEqualityFn(equal) {\n  return (a, b) => a === undefined || b === undefined ? a === b : equal(a, b);\n}\nfunction getLoader(options) {\n  if (isStreamingResourceOptions(options)) {\n    return options.stream;\n  }\n  return /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* (params) {\n      try {\n        return signal({\n          value: yield options.loader(params)\n        });\n      } catch (err) {\n        return signal({\n          error: encapsulateResourceError(err)\n        });\n      }\n    });\n    return function (_x) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n}\nfunction isStreamingResourceOptions(options) {\n  return !!options.stream;\n}\n/**\n * Project from a state with `ResourceInternalStatus` to the user-facing `ResourceStatus`\n */\nfunction projectStatusOfState(state) {\n  switch (state.status) {\n    case 'loading':\n      return state.extRequest.reload === 0 ? 'loading' : 'reloading';\n    case 'resolved':\n      return isResolved(state.stream()) ? 'resolved' : 'error';\n    default:\n      return state.status;\n  }\n}\nfunction isResolved(state) {\n  return state.error === undefined;\n}\nfunction encapsulateResourceError(error) {\n  if (error instanceof Error) {\n    return error;\n  }\n  return new ResourceWrappedError(error);\n}\nclass ResourceValueError extends Error {\n  constructor(error) {\n    super(ngDevMode ? `Resource is currently in an error state (see Error.cause for details): ${error.message}` : error.message, {\n      cause: error\n    });\n  }\n}\nclass ResourceWrappedError extends Error {\n  constructor(error) {\n    super(ngDevMode ? `Resource returned an error that's not an Error instance: ${String(error)}. Check this error's .cause for the actual error.` : String(error), {\n      cause: error\n    });\n  }\n}\nexport { OutputEmitterRef, ResourceImpl, computed, effect, encapsulateResourceError, getOutputDestroyRef, linkedSignal, resource, untracked };\n//# sourceMappingURL=resource-BPCh38bN.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}