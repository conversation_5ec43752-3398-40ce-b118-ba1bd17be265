{"ast": null, "code": "import { T as TileCoordinator } from './public-api-BoO5eSq-.mjs';\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"\", \"mat-grid-avatar\", \"\"], [\"\", \"matGridAvatar\", \"\"]], [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]], \"*\"];\nconst _c2 = [\"[mat-grid-avatar], [matGridAvatar]\", \"[mat-line], [matLine]\", \"*\"];\nconst _c3 = \".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\";\nexport { ɵ as ɵTileCoordinator } from './public-api-BoO5eSq-.mjs';\nimport { s as setLines, M as MatLine, a as MatLineModule } from './line-Bz5f9Cyx.mjs';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ContentChildren, Directive, NgModule } from '@angular/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\nimport 'rxjs/operators';\nimport '@angular/cdk/a11y';\n\n/**\n * Injection token used to provide a grid list to a tile and to avoid circular imports.\n * @docs-private\n */\nconst MAT_GRID_LIST = /*#__PURE__*/new InjectionToken('MAT_GRID_LIST');\nlet MatGridTile = /*#__PURE__*/(() => {\n  class MatGridTile {\n    _element = inject(ElementRef);\n    _gridList = inject(MAT_GRID_LIST, {\n      optional: true\n    });\n    _rowspan = 1;\n    _colspan = 1;\n    constructor() {}\n    /** Amount of rows that the grid tile takes up. */\n    get rowspan() {\n      return this._rowspan;\n    }\n    set rowspan(value) {\n      this._rowspan = Math.round(coerceNumberProperty(value));\n    }\n    /** Amount of columns that the grid tile takes up. */\n    get colspan() {\n      return this._colspan;\n    }\n    set colspan(value) {\n      this._colspan = Math.round(coerceNumberProperty(value));\n    }\n    /**\n     * Sets the style of the grid-tile element.  Needs to be set manually to avoid\n     * \"Changed after checked\" errors that would occur with HostBinding.\n     */\n    _setStyle(property, value) {\n      this._element.nativeElement.style[property] = value;\n    }\n    static ɵfac = function MatGridTile_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridTile)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatGridTile,\n      selectors: [[\"mat-grid-tile\"]],\n      hostAttrs: [1, \"mat-grid-tile\"],\n      hostVars: 2,\n      hostBindings: function MatGridTile_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"rowspan\", ctx.rowspan)(\"colspan\", ctx.colspan);\n        }\n      },\n      inputs: {\n        rowspan: \"rowspan\",\n        colspan: \"colspan\"\n      },\n      exportAs: [\"matGridTile\"],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"mat-grid-tile-content\"]],\n      template: function MatGridTile_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size, var(--mat-sys-body-large))}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size, var(--mat-sys-body-medium))}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatGridTile;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatGridTileText = /*#__PURE__*/(() => {\n  class MatGridTileText {\n    _element = inject(ElementRef);\n    _lines;\n    constructor() {}\n    ngAfterContentInit() {\n      setLines(this._lines, this._element);\n    }\n    static ɵfac = function MatGridTileText_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridTileText)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatGridTileText,\n      selectors: [[\"mat-grid-tile-header\"], [\"mat-grid-tile-footer\"]],\n      contentQueries: function MatGridTileText_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatLine, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n        }\n      },\n      ngContentSelectors: _c2,\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"mat-grid-list-text\"]],\n      template: function MatGridTileText_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(3, 2);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatGridTileText;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nlet MatGridAvatarCssMatStyler = /*#__PURE__*/(() => {\n  class MatGridAvatarCssMatStyler {\n    static ɵfac = function MatGridAvatarCssMatStyler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridAvatarCssMatStyler)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatGridAvatarCssMatStyler,\n      selectors: [[\"\", \"mat-grid-avatar\", \"\"], [\"\", \"matGridAvatar\", \"\"]],\n      hostAttrs: [1, \"mat-grid-avatar\"]\n    });\n  }\n  return MatGridAvatarCssMatStyler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nlet MatGridTileHeaderCssMatStyler = /*#__PURE__*/(() => {\n  class MatGridTileHeaderCssMatStyler {\n    static ɵfac = function MatGridTileHeaderCssMatStyler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridTileHeaderCssMatStyler)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatGridTileHeaderCssMatStyler,\n      selectors: [[\"mat-grid-tile-header\"]],\n      hostAttrs: [1, \"mat-grid-tile-header\"]\n    });\n  }\n  return MatGridTileHeaderCssMatStyler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nlet MatGridTileFooterCssMatStyler = /*#__PURE__*/(() => {\n  class MatGridTileFooterCssMatStyler {\n    static ɵfac = function MatGridTileFooterCssMatStyler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridTileFooterCssMatStyler)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatGridTileFooterCssMatStyler,\n      selectors: [[\"mat-grid-tile-footer\"]],\n      hostAttrs: [1, \"mat-grid-tile-footer\"]\n    });\n  }\n  return MatGridTileFooterCssMatStyler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * RegExp that can be used to check whether a value will\n * be allowed inside a CSS `calc()` expression.\n */\nconst cssCalcAllowedValue = /^-?\\d+((\\.\\d+)?[A-Za-z%$]?)+$/;\n/**\n * Sets the style properties for an individual tile, given the position calculated by the\n * Tile Coordinator.\n * @docs-private\n */\nclass TileStyler {\n  _gutterSize;\n  _rows = 0;\n  _rowspan = 0;\n  _cols;\n  _direction;\n  /**\n   * Adds grid-list layout info once it is available. Cannot be processed in the constructor\n   * because these properties haven't been calculated by that point.\n   *\n   * @param gutterSize Size of the grid's gutter.\n   * @param tracker Instance of the TileCoordinator.\n   * @param cols Amount of columns in the grid.\n   * @param direction Layout direction of the grid.\n   */\n  init(gutterSize, tracker, cols, direction) {\n    this._gutterSize = normalizeUnits(gutterSize);\n    this._rows = tracker.rowCount;\n    this._rowspan = tracker.rowspan;\n    this._cols = cols;\n    this._direction = direction;\n  }\n  /**\n   * Computes the amount of space a single 1x1 tile would take up (width or height).\n   * Used as a basis for other calculations.\n   * @param sizePercent Percent of the total grid-list space that one 1x1 tile would take up.\n   * @param gutterFraction Fraction of the gutter size taken up by one 1x1 tile.\n   * @return The size of a 1x1 tile as an expression that can be evaluated via CSS calc().\n   */\n  getBaseTileSize(sizePercent, gutterFraction) {\n    // Take the base size percent (as would be if evenly dividing the size between cells),\n    // and then subtracting the size of one gutter. However, since there are no gutters on the\n    // edges, each tile only uses a fraction (gutterShare = numGutters / numCells) of the gutter\n    // size. (Imagine having one gutter per tile, and then breaking up the extra gutter on the\n    // edge evenly among the cells).\n    return `(${sizePercent}% - (${this._gutterSize} * ${gutterFraction}))`;\n  }\n  /**\n   * Gets The horizontal or vertical position of a tile, e.g., the 'top' or 'left' property value.\n   * @param offset Number of tiles that have already been rendered in the row/column.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @return Position of the tile as a CSS calc() expression.\n   */\n  getTilePosition(baseSize, offset) {\n    // The position comes the size of a 1x1 tile plus gutter for each previous tile in the\n    // row/column (offset).\n    return offset === 0 ? '0' : calc(`(${baseSize} + ${this._gutterSize}) * ${offset}`);\n  }\n  /**\n   * Gets the actual size of a tile, e.g., width or height, taking rowspan or colspan into account.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @param span The tile's rowspan or colspan.\n   * @return Size of the tile as a CSS calc() expression.\n   */\n  getTileSize(baseSize, span) {\n    return `(${baseSize} * ${span}) + (${span - 1} * ${this._gutterSize})`;\n  }\n  /**\n   * Sets the style properties to be applied to a tile for the given row and column index.\n   * @param tile Tile to which to apply the styling.\n   * @param rowIndex Index of the tile's row.\n   * @param colIndex Index of the tile's column.\n   */\n  setStyle(tile, rowIndex, colIndex) {\n    // Percent of the available horizontal space that one column takes up.\n    let percentWidthPerTile = 100 / this._cols;\n    // Fraction of the vertical gutter size that each column takes up.\n    // For example, if there are 5 columns, each column uses 4/5 = 0.8 times the gutter width.\n    let gutterWidthFractionPerTile = (this._cols - 1) / this._cols;\n    this.setColStyles(tile, colIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n    this.setRowStyles(tile, rowIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n  }\n  /** Sets the horizontal placement of the tile in the list. */\n  setColStyles(tile, colIndex, percentWidth, gutterWidth) {\n    // Base horizontal size of a column.\n    let baseTileWidth = this.getBaseTileSize(percentWidth, gutterWidth);\n    // The width and horizontal position of each tile is always calculated the same way, but the\n    // height and vertical position depends on the rowMode.\n    let side = this._direction === 'rtl' ? 'right' : 'left';\n    tile._setStyle(side, this.getTilePosition(baseTileWidth, colIndex));\n    tile._setStyle('width', calc(this.getTileSize(baseTileWidth, tile.colspan)));\n  }\n  /**\n   * Calculates the total size taken up by gutters across one axis of a list.\n   */\n  getGutterSpan() {\n    return `${this._gutterSize} * (${this._rowspan} - 1)`;\n  }\n  /**\n   * Calculates the total size taken up by tiles across one axis of a list.\n   * @param tileHeight Height of the tile.\n   */\n  getTileSpan(tileHeight) {\n    return `${this._rowspan} * ${this.getTileSize(tileHeight, 1)}`;\n  }\n  /**\n   * Calculates the computed height and returns the correct style property to set.\n   * This method can be implemented by each type of TileStyler.\n   * @docs-private\n   */\n  getComputedHeight() {\n    return null;\n  }\n}\n/**\n * This type of styler is instantiated when the user passes in a fixed row height.\n * Example `<mat-grid-list cols=\"3\" rowHeight=\"100px\">`\n * @docs-private\n */\nclass FixedTileStyler extends TileStyler {\n  fixedRowHeight;\n  constructor(fixedRowHeight) {\n    super();\n    this.fixedRowHeight = fixedRowHeight;\n  }\n  init(gutterSize, tracker, cols, direction) {\n    super.init(gutterSize, tracker, cols, direction);\n    this.fixedRowHeight = normalizeUnits(this.fixedRowHeight);\n    if (!cssCalcAllowedValue.test(this.fixedRowHeight) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid value \"${this.fixedRowHeight}\" set as rowHeight.`);\n    }\n  }\n  setRowStyles(tile, rowIndex) {\n    tile._setStyle('top', this.getTilePosition(this.fixedRowHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(this.fixedRowHeight, tile.rowspan)));\n  }\n  getComputedHeight() {\n    return ['height', calc(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)];\n  }\n  reset(list) {\n    list._setListStyle(['height', null]);\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n/**\n * This type of styler is instantiated when the user passes in a width:height ratio\n * for the row height.  Example `<mat-grid-list cols=\"3\" rowHeight=\"3:1\">`\n * @docs-private\n */\nclass RatioTileStyler extends TileStyler {\n  /** Ratio width:height given by user to determine row height. */\n  rowHeightRatio;\n  baseTileHeight;\n  constructor(value) {\n    super();\n    this._parseRatio(value);\n  }\n  setRowStyles(tile, rowIndex, percentWidth, gutterWidth) {\n    let percentHeightPerTile = percentWidth / this.rowHeightRatio;\n    this.baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterWidth);\n    // Use padding-top and margin-top to maintain the given aspect ratio, as\n    // a percentage-based value for these properties is applied versus the *width* of the\n    // containing block. See http://www.w3.org/TR/CSS2/box.html#margin-properties\n    tile._setStyle('marginTop', this.getTilePosition(this.baseTileHeight, rowIndex));\n    tile._setStyle('paddingTop', calc(this.getTileSize(this.baseTileHeight, tile.rowspan)));\n  }\n  getComputedHeight() {\n    return ['paddingBottom', calc(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`)];\n  }\n  reset(list) {\n    list._setListStyle(['paddingBottom', null]);\n    list._tiles.forEach(tile => {\n      tile._setStyle('marginTop', null);\n      tile._setStyle('paddingTop', null);\n    });\n  }\n  _parseRatio(value) {\n    const ratioParts = value.split(':');\n    if (ratioParts.length !== 2 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`mat-grid-list: invalid ratio given for row-height: \"${value}\"`);\n    }\n    this.rowHeightRatio = parseFloat(ratioParts[0]) / parseFloat(ratioParts[1]);\n  }\n}\n/**\n * This type of styler is instantiated when the user selects a \"fit\" row height mode.\n * In other words, the row height will reflect the total height of the container divided\n * by the number of rows.  Example `<mat-grid-list cols=\"3\" rowHeight=\"fit\">`\n *\n * @docs-private\n */\nclass FitTileStyler extends TileStyler {\n  setRowStyles(tile, rowIndex) {\n    // Percent of the available vertical space that one row takes up.\n    let percentHeightPerTile = 100 / this._rowspan;\n    // Fraction of the horizontal gutter size that each column takes up.\n    let gutterHeightPerTile = (this._rows - 1) / this._rows;\n    // Base vertical size of a column.\n    let baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterHeightPerTile);\n    tile._setStyle('top', this.getTilePosition(baseTileHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(baseTileHeight, tile.rowspan)));\n  }\n  reset(list) {\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n/** Wraps a CSS string in a calc function */\nfunction calc(exp) {\n  return `calc(${exp})`;\n}\n/** Appends pixels to a CSS string if no units are given. */\nfunction normalizeUnits(value) {\n  return value.match(/([A-Za-z%]+)$/) ? value : `${value}px`;\n}\n\n// TODO(kara): Conditional (responsive) column count / row size.\n// TODO(kara): Re-layout on window resize / media change (debounced).\n// TODO(kara): gridTileHeader and gridTileFooter.\nconst MAT_FIT_MODE = 'fit';\nlet MatGridList = /*#__PURE__*/(() => {\n  class MatGridList {\n    _element = inject(ElementRef);\n    _dir = inject(Directionality, {\n      optional: true\n    });\n    /** Number of columns being rendered. */\n    _cols;\n    /** Used for determining the position of each tile in the grid. */\n    _tileCoordinator;\n    /**\n     * Row height value passed in by user. This can be one of three types:\n     * - Number value (ex: \"100px\"):  sets a fixed row height to that value\n     * - Ratio value (ex: \"4:3\"): sets the row height based on width:height ratio\n     * - \"Fit\" mode (ex: \"fit\"): sets the row height to total height divided by number of rows\n     */\n    _rowHeight;\n    /** The amount of space between tiles. This will be something like '5px' or '2em'. */\n    _gutter = '1px';\n    /** Sets position and size styles for a tile */\n    _tileStyler;\n    /** Query list of tiles that are being rendered. */\n    _tiles;\n    constructor() {}\n    /** Amount of columns in the grid list. */\n    get cols() {\n      return this._cols;\n    }\n    set cols(value) {\n      this._cols = Math.max(1, Math.round(coerceNumberProperty(value)));\n    }\n    /** Size of the grid list's gutter in pixels. */\n    get gutterSize() {\n      return this._gutter;\n    }\n    set gutterSize(value) {\n      this._gutter = `${value == null ? '' : value}`;\n    }\n    /** Set internal representation of row height from the user-provided value. */\n    get rowHeight() {\n      return this._rowHeight;\n    }\n    set rowHeight(value) {\n      const newValue = `${value == null ? '' : value}`;\n      if (newValue !== this._rowHeight) {\n        this._rowHeight = newValue;\n        this._setTileStyler(this._rowHeight);\n      }\n    }\n    ngOnInit() {\n      this._checkCols();\n      this._checkRowHeight();\n    }\n    /**\n     * The layout calculation is fairly cheap if nothing changes, so there's little cost\n     * to run it frequently.\n     */\n    ngAfterContentChecked() {\n      this._layoutTiles();\n    }\n    /** Throw a friendly error if cols property is missing */\n    _checkCols() {\n      if (!this.cols && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`mat-grid-list: must pass in number of columns. ` + `Example: <mat-grid-list cols=\"3\">`);\n      }\n    }\n    /** Default to equal width:height if rowHeight property is missing */\n    _checkRowHeight() {\n      if (!this._rowHeight) {\n        this._setTileStyler('1:1');\n      }\n    }\n    /** Creates correct Tile Styler subtype based on rowHeight passed in by user */\n    _setTileStyler(rowHeight) {\n      if (this._tileStyler) {\n        this._tileStyler.reset(this);\n      }\n      if (rowHeight === MAT_FIT_MODE) {\n        this._tileStyler = new FitTileStyler();\n      } else if (rowHeight && rowHeight.indexOf(':') > -1) {\n        this._tileStyler = new RatioTileStyler(rowHeight);\n      } else {\n        this._tileStyler = new FixedTileStyler(rowHeight);\n      }\n    }\n    /** Computes and applies the size and position for all children grid tiles. */\n    _layoutTiles() {\n      if (!this._tileCoordinator) {\n        this._tileCoordinator = new TileCoordinator();\n      }\n      const tracker = this._tileCoordinator;\n      const tiles = this._tiles.filter(tile => !tile._gridList || tile._gridList === this);\n      const direction = this._dir ? this._dir.value : 'ltr';\n      this._tileCoordinator.update(this.cols, tiles);\n      this._tileStyler.init(this.gutterSize, tracker, this.cols, direction);\n      tiles.forEach((tile, index) => {\n        const pos = tracker.positions[index];\n        this._tileStyler.setStyle(tile, pos.row, pos.col);\n      });\n      this._setListStyle(this._tileStyler.getComputedHeight());\n    }\n    /** Sets style on the main grid-list element, given the style name and value. */\n    _setListStyle(style) {\n      if (style) {\n        this._element.nativeElement.style[style[0]] = style[1];\n      }\n    }\n    static ɵfac = function MatGridList_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridList)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatGridList,\n      selectors: [[\"mat-grid-list\"]],\n      contentQueries: function MatGridList_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatGridTile, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tiles = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-grid-list\"],\n      hostVars: 1,\n      hostBindings: function MatGridList_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"cols\", ctx.cols);\n        }\n      },\n      inputs: {\n        cols: \"cols\",\n        gutterSize: \"gutterSize\",\n        rowHeight: \"rowHeight\"\n      },\n      exportAs: [\"matGridList\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_GRID_LIST,\n        useExisting: MatGridList\n      }])],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 0,\n      template: function MatGridList_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [_c3],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatGridList;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet MatGridListModule = /*#__PURE__*/(() => {\n  class MatGridListModule {\n    static ɵfac = function MatGridListModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatGridListModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatGridListModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatLineModule, MatCommonModule, MatLineModule, MatCommonModule]\n    });\n  }\n  return MatGridListModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatGridAvatarCssMatStyler, MatGridList, MatGridListModule, MatGridTile, MatGridTileFooterCssMatStyler, MatGridTileHeaderCssMatStyler, MatGridTileText, MatLine };\n//# sourceMappingURL=grid-list.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}