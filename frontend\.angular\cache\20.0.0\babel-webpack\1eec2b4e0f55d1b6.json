{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let LoadingService = /*#__PURE__*/(() => {\n  class LoadingService {\n    constructor() {\n      this.loadingSubject = new BehaviorSubject(false);\n      this.loading$ = this.loadingSubject.asObservable();\n    }\n    show() {\n      this.loadingSubject.next(true);\n    }\n    hide() {\n      this.loadingSubject.next(false);\n    }\n    static #_ = this.ɵfac = function LoadingService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoadingService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoadingService,\n      factory: LoadingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return LoadingService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}