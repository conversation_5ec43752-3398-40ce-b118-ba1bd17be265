{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nlet PlatformNavigation = /*#__PURE__*/(() => {\n  class PlatformNavigation {\n    static ɵfac = function PlatformNavigation_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PlatformNavigation)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: PlatformNavigation,\n      factory: () => (() => window.navigation)(),\n      providedIn: 'platform'\n    });\n  }\n  return PlatformNavigation;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { PlatformNavigation };\n//# sourceMappingURL=platform_navigation-B45Jeakb.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}