{"name": "@types/passport-github2", "version": "1.2.9", "description": "TypeScript definitions for passport-github2", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/passport-github2", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/yasupeke"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "mthmulders", "url": "https://github.com/mthmulders"}, {"name": "<PERSON>", "githubUsername": "codepunkt", "url": "https://github.com/codepunkt"}, {"name": "<PERSON>", "githubUsername": "ivan94", "url": "https://github.com/ivan94"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/passport-github2"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/passport": "*", "@types/passport-oauth2": "*"}, "typesPublisherContentHash": "9ad8cff539aa4bd521744823878a2dbf7b70e3659b3a3083dbbc32f3c780ad58", "typeScriptVersion": "4.5"}