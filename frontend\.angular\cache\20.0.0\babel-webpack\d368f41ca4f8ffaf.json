{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"../../services/two-factor.service\";\nimport * as i3 from \"../../services/oauth.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/form-field\";\nimport * as i12 from \"@angular/material/input\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nfunction ProfileComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"img\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r0.currentUser.avatarUrl, i0.ɵɵsanitizeUrl)(\"alt\", (ctx_r0.currentUser.firstName || \"\") + \" \" + (ctx_r0.currentUser.lastName || \"\"));\n  }\n}\nfunction ProfileComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Connected via:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 19);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassMap(ctx_r0.getOAuthProviderIcon());\n    i0.ɵɵstyleProp(\"color\", ctx_r0.getOAuthProviderColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getOAuthProviderName(), \" \");\n  }\n}\nfunction ProfileComponent_button_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_button_58_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleChangePassword());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.showChangePassword ? \"expand_less\" : \"expand_more\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.showChangePassword ? \"Cancel\" : \"Change Password\", \" \");\n  }\n}\nfunction ProfileComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"mat-icon\", 22);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Password managed by \", ctx_r0.getOAuthProviderName());\n  }\n}\nfunction ProfileComponent_div_60_mat_form_field_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 25)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"2FA Code (Required)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 36);\n    i0.ɵɵelementStart(4, \"mat-icon\", 37);\n    i0.ɵɵtext(5, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-hint\");\n    i0.ɵɵtext(7, \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-error\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"twoFactorToken\"));\n  }\n}\nfunction ProfileComponent_div_60_mat_spinner_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 38);\n  }\n}\nfunction ProfileComponent_div_60_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Change Password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"form\", 24);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfileComponent_div_60_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onChangePassword());\n    });\n    i0.ɵɵelementStart(2, \"mat-form-field\", 25)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Current Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 26);\n    i0.ɵɵelementStart(6, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_60_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideCurrentPassword = !ctx_r0.hideCurrentPassword);\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-error\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-form-field\", 25)(12, \"mat-label\");\n    i0.ɵɵtext(13, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 28);\n    i0.ɵɵelementStart(15, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_60_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideNewPassword = !ctx_r0.hideNewPassword);\n    });\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"mat-error\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"mat-form-field\", 25)(21, \"mat-label\");\n    i0.ɵɵtext(22, \"Confirm New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(23, \"input\", 29);\n    i0.ɵɵelementStart(24, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_60_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.hideConfirmPassword = !ctx_r0.hideConfirmPassword);\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"mat-error\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, ProfileComponent_div_60_mat_form_field_29_Template, 10, 1, \"mat-form-field\", 30);\n    i0.ɵɵelementStart(30, \"div\", 31)(31, \"button\", 32);\n    i0.ɵɵtemplate(32, ProfileComponent_div_60_mat_spinner_32_Template, 1, 0, \"mat-spinner\", 33)(33, ProfileComponent_div_60_span_33_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_60_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleChangePassword());\n    });\n    i0.ɵɵtext(35, \" Cancel \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.changePasswordForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideCurrentPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideCurrentPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"currentPassword\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideNewPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideNewPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"newPassword\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r0.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getFieldError(\"confirmPassword\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.twoFactorStatus.enabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading);\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(authService, twoFactorService, oauthService, formBuilder, snackBar) {\n      this.authService = authService;\n      this.twoFactorService = twoFactorService;\n      this.oauthService = oauthService;\n      this.formBuilder = formBuilder;\n      this.snackBar = snackBar;\n      this.currentUser = null;\n      this.twoFactorStatus = {\n        enabled: false\n      };\n      this.loading = false;\n      this.hideCurrentPassword = true;\n      this.hideNewPassword = true;\n      this.hideConfirmPassword = true;\n      this.showChangePassword = false;\n      this.changePasswordForm = this.formBuilder.group({\n        currentPassword: ['', [Validators.required]],\n        newPassword: ['', [Validators.required, Validators.minLength(8)]],\n        confirmPassword: ['', [Validators.required]],\n        twoFactorToken: ['']\n      }, {\n        validators: this.passwordMatchValidator\n      });\n    }\n    ngOnInit() {\n      this.currentUser = this.authService.currentUserValue;\n      this.load2FAStatus();\n    }\n    load2FAStatus() {\n      this.twoFactorService.get2FAStatus().subscribe({\n        next: status => {\n          this.twoFactorStatus = status;\n        },\n        error: error => {\n          console.error('Failed to load 2FA status:', error);\n        }\n      });\n    }\n    toggleChangePassword() {\n      this.showChangePassword = !this.showChangePassword;\n      if (!this.showChangePassword) {\n        this.changePasswordForm.reset();\n      }\n    }\n    onChangePassword() {\n      if (this.changePasswordForm.invalid) {\n        this.markFormGroupTouched(this.changePasswordForm);\n        return;\n      }\n      this.loading = true;\n      const formValue = this.changePasswordForm.value;\n      this.authService.changePassword(formValue.currentPassword, formValue.newPassword, formValue.twoFactorToken || undefined).subscribe({\n        next: response => {\n          this.snackBar.open('Password changed successfully!', 'Close', {\n            duration: 3000\n          });\n          this.changePasswordForm.reset();\n          this.showChangePassword = false;\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'Failed to change password', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    isOAuthUser() {\n      return this.oauthService.isOAuthUser(this.currentUser);\n    }\n    getOAuthProviderName() {\n      return this.oauthService.getOAuthProviderName(this.currentUser);\n    }\n    getOAuthProviderIcon() {\n      return this.oauthService.getOAuthProviderIcon(this.currentUser);\n    }\n    getOAuthProviderColor() {\n      return this.oauthService.getOAuthProviderColor(this.currentUser);\n    }\n    getFieldError(fieldName) {\n      const field = this.changePasswordForm.get(fieldName);\n      if (field?.errors && field.touched) {\n        if (field.errors['required']) return `${fieldName} is required`;\n        if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        if (field.errors['passwordMismatch']) return 'Passwords do not match';\n      }\n      return '';\n    }\n    passwordMatchValidator(form) {\n      const newPassword = form.get('newPassword');\n      const confirmPassword = form.get('confirmPassword');\n      if (newPassword && confirmPassword && newPassword.value !== confirmPassword.value) {\n        confirmPassword.setErrors({\n          passwordMismatch: true\n        });\n      } else {\n        confirmPassword?.setErrors(null);\n      }\n      return null;\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n      });\n    }\n    static #_ = this.ɵfac = function ProfileComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      standalone: false,\n      decls: 66,\n      vars: 25,\n      consts: [[1, \"profile-container\"], [1, \"container\"], [1, \"user-info\"], [\"class\", \"user-avatar\", 4, \"ngIf\"], [1, \"user-details\"], [\"class\", \"oauth-info\", 4, \"ngIf\"], [1, \"account-status\"], [1, \"status-chips\"], [1, \"status-chip\"], [\"mat-button\", \"\", \"color\", \"primary\"], [1, \"security-section\"], [1, \"section-header\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"oauth-password-notice\", 4, \"ngIf\"], [\"class\", \"change-password-form\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"accent\", \"routerLink\", \"/dashboard/two-factor\"], [1, \"user-avatar\"], [3, \"src\", \"alt\"], [1, \"oauth-info\"], [1, \"oauth-provider\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"oauth-password-notice\"], [\"color\", \"warn\"], [1, \"change-password-form\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"currentPassword\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"newPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"autocomplete\", \"new-password\", 3, \"type\"], [\"class\", \"form-field\", \"appearance\", \"outline\", 4, \"ngIf\"], [1, \"form-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"matSuffix\", \"\"], [\"diameter\", \"20\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"Profile & Security Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"mat-card\")(5, \"mat-card-header\")(6, \"mat-card-title\");\n          i0.ɵɵtext(7, \"User Information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 2);\n          i0.ɵɵtemplate(10, ProfileComponent_div_10_Template, 2, 2, \"div\", 3);\n          i0.ɵɵelementStart(11, \"div\", 4)(12, \"p\")(13, \"strong\");\n          i0.ɵɵtext(14, \"Name:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\")(17, \"strong\");\n          i0.ɵɵtext(18, \"Email:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\")(21, \"strong\");\n          i0.ɵɵtext(22, \"Phone:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"p\")(25, \"strong\");\n          i0.ɵɵtext(26, \"Member since:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27);\n          i0.ɵɵpipe(28, \"date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(29, ProfileComponent_div_29_Template, 7, 5, \"div\", 5);\n          i0.ɵɵelementStart(30, \"div\", 6)(31, \"div\", 7)(32, \"div\", 8)(33, \"mat-icon\");\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\");\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 8)(38, \"mat-icon\");\n          i0.ɵɵtext(39, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"span\");\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(42, \"mat-card-actions\")(43, \"button\", 9)(44, \"mat-icon\");\n          i0.ɵɵtext(45, \"edit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" Edit Profile \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"mat-card\")(48, \"mat-card-header\")(49, \"mat-card-title\");\n          i0.ɵɵtext(50, \"Security Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"mat-card-content\")(52, \"p\");\n          i0.ɵɵtext(53, \"Manage your account security settings including two-factor authentication and password.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 10)(55, \"div\", 11)(56, \"h3\");\n          i0.ɵɵtext(57, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(58, ProfileComponent_button_58_Template, 4, 2, \"button\", 12)(59, ProfileComponent_div_59_Template, 5, 1, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(60, ProfileComponent_div_60_Template, 36, 14, \"div\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"mat-card-actions\")(62, \"button\", 15)(63, \"mat-icon\");\n          i0.ɵɵtext(64, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser == null ? null : ctx.currentUser.avatarUrl);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate2(\" \", ctx.currentUser == null ? null : ctx.currentUser.firstName, \" \", ctx.currentUser == null ? null : ctx.currentUser.lastName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (ctx.currentUser == null ? null : ctx.currentUser.phone) || \"Not provided\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(28, 22, ctx.currentUser == null ? null : ctx.currentUser.createdAt, \"mediumDate\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"verified\", ctx.currentUser == null ? null : ctx.currentUser.emailVerified)(\"unverified\", !(ctx.currentUser == null ? null : ctx.currentUser.emailVerified));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"verified\" : \"warning\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"Email \", (ctx.currentUser == null ? null : ctx.currentUser.emailVerified) ? \"Verified\" : \"Unverified\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"enabled\", ctx.twoFactorStatus.enabled)(\"disabled\", !ctx.twoFactorStatus.enabled);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"2FA \", ctx.twoFactorStatus.enabled ? \"Enabled\" : \"Disabled\");\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isOAuthUser());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isOAuthUser());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showChangePassword && !ctx.isOAuthUser());\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.twoFactorStatus.enabled ? \"Manage 2FA\" : \"Setup 2FA\", \" \");\n        }\n      },\n      dependencies: [i6.NgIf, i7.RouterLink, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.MaxLengthValidator, i4.FormGroupDirective, i4.FormControlName, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardTitle, i9.MatButton, i9.MatIconButton, i10.MatIcon, i11.MatFormField, i11.MatLabel, i11.MatHint, i11.MatError, i11.MatSuffix, i12.MatInput, i13.MatProgressSpinner, i6.DatePipe],\n      styles: [\".profile-container[_ngcontent-%COMP%]{min-height:100vh;background:#f5f5f5;padding:2rem}.container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}h1[_ngcontent-%COMP%]{color:#333;margin-bottom:2rem}mat-card[_ngcontent-%COMP%]{margin-bottom:1.5rem}.user-info[_ngcontent-%COMP%]{display:flex;gap:1.5rem;align-items:flex-start}.user-info[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:3px solid #e0e0e0}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]{flex:1}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:.5rem 0}.user-info[_ngcontent-%COMP%]   .user-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#333;margin-right:.5rem}.oauth-info[_ngcontent-%COMP%]{margin-top:1rem}.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.5rem;padding:.25rem .75rem;background:#f5f5f5;border-radius:20px;font-size:.875rem}.oauth-info[_ngcontent-%COMP%]   .oauth-provider[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.account-status[_ngcontent-%COMP%]{margin-top:1rem}.account-status[_ngcontent-%COMP%]   .status-chips[_ngcontent-%COMP%]{display:flex;gap:.5rem;flex-wrap:wrap}.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;padding:.5rem 1rem;border-radius:20px;font-size:.875rem;font-weight:500}.account-status[_ngcontent-%COMP%]   .status-chip[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px}.account-status[_ngcontent-%COMP%]   .status-chip.verified[_ngcontent-%COMP%]{background:#e8f5e8;color:#2e7d32;border:1px solid #4caf50}.account-status[_ngcontent-%COMP%]   .status-chip.unverified[_ngcontent-%COMP%]{background:#fff3e0;color:#f57c00;border:1px solid #ff9800}.account-status[_ngcontent-%COMP%]   .status-chip.enabled[_ngcontent-%COMP%]{background:#e3f2fd;color:#1976d2;border:1px solid #2196f3}.account-status[_ngcontent-%COMP%]   .status-chip.disabled[_ngcontent-%COMP%]{background:#f5f5f5;color:#666;border:1px solid #ccc}.security-section[_ngcontent-%COMP%]{margin-top:1.5rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:1rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;color:#333}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#666;font-size:.875rem}.security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .oauth-password-notice[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.change-password-form[_ngcontent-%COMP%]{background:#f9f9f9;padding:1.5rem;border-radius:8px;border:1px solid #e0e0e0}.change-password-form[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{width:100%;margin-bottom:1rem}.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-top:1.5rem}.change-password-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}\"]\n    });\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}