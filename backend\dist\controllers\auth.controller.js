"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const tslib_1 = require("tslib");
const repository_1 = require("@loopback/repository");
const rest_1 = require("@loopback/rest");
const core_1 = require("@loopback/core");
const authentication_1 = require("@loopback/authentication");
const authentication_jwt_1 = require("@loopback/authentication-jwt");
const security_1 = require("@loopback/security");
const bcryptjs_1 = require("bcryptjs");
const models_1 = require("../models");
const repositories_1 = require("../repositories");
const services_1 = require("../services");
let AuthController = class AuthController {
    constructor(jwtService, user, userRepository, securityService, emailService, smsService, oauthService) {
        this.jwtService = jwtService;
        this.user = user;
        this.userRepository = userRepository;
        this.securityService = securityService;
        this.emailService = emailService;
        this.smsService = smsService;
        this.oauthService = oauthService;
    }
    async signUp(newUserRequest) {
        console.log('🔍 Signup started for email:', newUserRequest.email);
        console.log('🔍 Request data:', {
            email: newUserRequest.email,
            firstName: newUserRequest.firstName,
            lastName: newUserRequest.lastName,
            phone: newUserRequest.phone,
            hasPassword: !!newUserRequest.password
        });
        // Check if email is disposable (optional - warn but allow)
        try {
            if (this.securityService) {
                const isDisposable = await this.securityService.isDisposableEmail(newUserRequest.email);
                if (isDisposable) {
                    console.log(`⚠️ Disposable email detected: ${newUserRequest.email}`);
                    // Allow registration but log the warning
                }
            }
        }
        catch (error) {
            console.log('⚠️ Email validation service unavailable, skipping check');
        }
        console.log('🔍 Step 1: Email validation completed');
        // Validate password strength
        console.log('🔍 Step 2: Starting password validation');
        if (this.securityService) {
            const passwordValidation = await this.securityService.validatePasswordStrength(newUserRequest.password);
            if (!passwordValidation.isValid) {
                throw new rest_1.HttpErrors.BadRequest(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
            }
            console.log('🔍 Step 2: Password validation passed');
        }
        else {
            // Basic password validation if service is not available
            if (newUserRequest.password.length < 8) {
                throw new rest_1.HttpErrors.BadRequest('Password must be at least 8 characters long');
            }
            console.log('🔍 Step 2: Basic password validation passed');
        }
        // Check if user already exists
        console.log('🔍 Step 3: Checking if user exists');
        const foundUser = await this.userRepository.findOne({
            where: { email: newUserRequest.email },
        });
        if (foundUser) {
            console.log('❌ Step 3: User already exists');
            throw new rest_1.HttpErrors.Conflict('Email already exists');
        }
        console.log('🔍 Step 3: User does not exist, proceeding');
        // Hash password
        let password;
        if (this.securityService) {
            password = await this.securityService.hashPassword(newUserRequest.password);
        }
        else {
            // Fallback to bcrypt directly
            const bcrypt = require('bcryptjs');
            password = await bcrypt.hash(newUserRequest.password, 12);
        }
        // Generate email verification token (optional)
        let emailVerificationToken;
        let emailVerificationExpires;
        try {
            if (this.securityService) {
                emailVerificationToken = await this.securityService.generateEmailVerificationToken();
                emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
            }
        }
        catch (error) {
            console.log('⚠️ Email verification token generation failed, proceeding without it');
        }
        // Create user
        const savedUser = await this.userRepository.create({
            email: newUserRequest.email,
            firstName: newUserRequest.firstName,
            lastName: newUserRequest.lastName,
            phone: newUserRequest.phone,
            password: password,
            emailVerificationToken,
            emailVerificationExpires,
            emailVerified: true, // Set to true by default to allow immediate login
            roles: ['user'],
        });
        // Send verification email (optional)
        try {
            if (emailVerificationToken && this.emailService) {
                await this.emailService.sendVerificationEmail(savedUser.email, emailVerificationToken);
                console.log(`📧 Verification email sent to ${savedUser.email}`);
            }
        }
        catch (error) {
            console.error('Failed to send verification email:', error);
            // Don't fail registration if email sending fails
        }
        return {
            message: 'User registered successfully. You can now log in.',
            userId: savedUser.id,
        };
    }
    async login(credentials) {
        // Verify credentials manually
        const user = await this.verifyCredentials(credentials);
        // Check if email is verified (optional - warn but allow login)
        if (!user.emailVerified) {
            console.log(`⚠️ User ${user.email} logging in with unverified email`);
            // Allow login but could add a flag to the response
        }
        // Check if 2FA is enabled
        if (user.twoFactorEnabled) {
            if (!credentials.twoFactorToken) {
                return {
                    token: '',
                    user,
                    requiresTwoFactor: true,
                };
            }
            // Verify 2FA token
            if (this.securityService) {
                const isValid2FA = await this.securityService.verifyTwoFactorToken(user.id, credentials.twoFactorToken);
                if (!isValid2FA) {
                    throw new rest_1.HttpErrors.Unauthorized('Invalid two-factor authentication token');
                }
            }
            else {
                throw new rest_1.HttpErrors.Unauthorized('Two-factor authentication service unavailable');
            }
        }
        // Convert a User object into a UserProfile object (reduced set of properties)
        const userProfile = this.convertToUserProfile(user);
        // Create a JSON Web Token based on the user profile
        const token = await this.jwtService.generateToken(userProfile);
        return { token, user };
    }
    async verifyEmail(request) {
        const user = await this.userRepository.findOne({
            where: {
                emailVerificationToken: request.token,
                emailVerificationExpires: { gt: new Date() },
            },
        });
        if (!user) {
            throw new rest_1.HttpErrors.BadRequest('Invalid or expired verification token');
        }
        await this.userRepository.updateById(user.id, {
            emailVerified: true,
            emailVerificationToken: undefined,
            emailVerificationExpires: undefined,
            updatedAt: new Date(),
        });
        return { message: 'Email verified successfully' };
    }
    async forgotPassword(request) {
        if (!request.email && !request.phone) {
            throw new rest_1.HttpErrors.BadRequest('Either email or phone is required');
        }
        let user = null;
        if (request.email) {
            user = await this.userRepository.findOne({
                where: { email: request.email },
            });
        }
        else if (request.phone) {
            user = await this.userRepository.findOne({
                where: { phone: request.phone },
            });
        }
        if (!user) {
            // Don't reveal if email/phone exists or not
            return { message: 'If the email/phone exists, a password reset link has been sent' };
        }
        if (!this.securityService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('Security service is not available');
        }
        const resetToken = await this.securityService.generatePasswordResetToken();
        const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
        await this.userRepository.updateById(user.id, {
            passwordResetToken: resetToken,
            passwordResetExpires: resetExpires,
            updatedAt: new Date(),
        });
        try {
            if (this.emailService) {
                await this.emailService.sendPasswordResetEmail(user.email, resetToken);
            }
        }
        catch (error) {
            console.error('Failed to send password reset email:', error);
        }
        return { message: 'If the email exists, a password reset link has been sent' };
    }
    async resetPassword(request) {
        const user = await this.userRepository.findOne({
            where: {
                passwordResetToken: request.token,
                passwordResetExpires: { gt: new Date() },
            },
        });
        if (!user) {
            throw new rest_1.HttpErrors.BadRequest('Invalid or expired reset token');
        }
        // Validate password strength
        if (!this.securityService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('Security service is not available');
        }
        const passwordValidation = await this.securityService.validatePasswordStrength(request.password);
        if (!passwordValidation.isValid) {
            throw new rest_1.HttpErrors.BadRequest(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
        }
        // Hash new password
        const hashedPassword = await this.securityService.hashPassword(request.password);
        // Update password
        await this.userRepository.updateById(user.id, {
            password: hashedPassword,
            updatedAt: new Date(),
        });
        // Clear reset token
        await this.userRepository.updateById(user.id, {
            passwordResetToken: undefined,
            passwordResetExpires: undefined,
            updatedAt: new Date(),
        });
        return { message: 'Password reset successfully' };
    }
    // Helper methods for authentication
    async verifyCredentials(credentials) {
        const invalidCredentialsError = 'Invalid email or password.';
        const foundUser = await this.userRepository.findOne({
            where: { email: credentials.email },
        });
        if (!foundUser) {
            throw new rest_1.HttpErrors.Unauthorized(invalidCredentialsError);
        }
        // Check if user is active
        if (!foundUser.isActive) {
            throw new rest_1.HttpErrors.Unauthorized('Account is deactivated.');
        }
        // Check if account is locked
        if (foundUser.lockUntil && foundUser.lockUntil > new Date()) {
            throw new rest_1.HttpErrors.Unauthorized('Account is temporarily locked. Please try again later.');
        }
        if (!foundUser.password) {
            throw new rest_1.HttpErrors.Unauthorized(invalidCredentialsError);
        }
        const passwordMatched = await (0, bcryptjs_1.compare)(credentials.password, foundUser.password);
        if (!passwordMatched) {
            // Increment login attempts
            await this.handleFailedLogin(foundUser);
            throw new rest_1.HttpErrors.Unauthorized(invalidCredentialsError);
        }
        // Reset login attempts on successful login
        await this.handleSuccessfulLogin(foundUser);
        return foundUser;
    }
    convertToUserProfile(user) {
        return {
            [security_1.securityId]: user.id.toString(),
            name: `${user.firstName} ${user.lastName}`,
            id: user.id,
            email: user.email,
            roles: user.roles,
        };
    }
    async handleFailedLogin(user) {
        const maxAttempts = 5;
        const lockTime = 30 * 60 * 1000; // 30 minutes
        const attempts = (user.loginAttempts || 0) + 1;
        const updateData = {
            loginAttempts: attempts,
            updatedAt: new Date(),
        };
        if (attempts >= maxAttempts) {
            updateData.lockUntil = new Date(Date.now() + lockTime);
        }
        await this.userRepository.updateById(user.id, updateData);
    }
    async handleSuccessfulLogin(user) {
        await this.userRepository.updateById(user.id, {
            loginAttempts: 0,
            lockUntil: undefined,
            lastLoginAt: new Date(),
            updatedAt: new Date(),
        });
    }
    async changePassword(currentUserProfile, request) {
        const userId = currentUserProfile[security_1.securityId];
        const user = await this.userRepository.findById(userId);
        if (!user) {
            throw new rest_1.HttpErrors.NotFound('User not found');
        }
        // Verify current password
        if (!user.password) {
            throw new rest_1.HttpErrors.BadRequest('User has no password set');
        }
        const isCurrentPasswordValid = await (0, bcryptjs_1.compare)(request.currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new rest_1.HttpErrors.BadRequest('Current password is incorrect');
        }
        // Check if 2FA is enabled and token is required
        if (user.twoFactorEnabled) {
            if (!request.twoFactorToken) {
                throw new rest_1.HttpErrors.BadRequest('Two-factor authentication token is required');
            }
            // Verify 2FA token
            const speakeasy = require('speakeasy');
            const verified = speakeasy.totp.verify({
                secret: user.twoFactorSecret,
                encoding: 'base32',
                token: request.twoFactorToken,
                window: 2,
            });
            if (!verified) {
                throw new rest_1.HttpErrors.BadRequest('Invalid two-factor authentication token');
            }
        }
        // Validate new password strength
        if (this.securityService) {
            const validation = await this.securityService.validatePasswordStrength(request.newPassword);
            if (!validation.isValid) {
                throw new rest_1.HttpErrors.BadRequest(`Password validation failed: ${validation.errors.join(', ')}`);
            }
        }
        // Hash new password
        const hashedNewPassword = await (0, bcryptjs_1.hash)(request.newPassword, 12);
        // Update password
        await this.userRepository.updateById(userId, {
            password: hashedNewPassword,
            updatedAt: new Date(),
        });
        return { message: 'Password changed successfully' };
    }
    async getOAuthUrl(provider) {
        if (!['google', 'github', 'microsoft'].includes(provider)) {
            throw new rest_1.HttpErrors.BadRequest('Unsupported OAuth provider');
        }
        if (!this.oauthService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('OAuth service is not available');
        }
        const state = Math.random().toString(36).substring(2, 15);
        const url = this.oauthService.generateOAuthUrl(provider, state);
        return { url, state };
    }
    async handleOAuthCallback(provider, request) {
        if (!['google', 'github', 'microsoft'].includes(provider)) {
            throw new rest_1.HttpErrors.BadRequest('Unsupported OAuth provider');
        }
        if (!this.oauthService) {
            throw new rest_1.HttpErrors.ServiceUnavailable('OAuth service is not available');
        }
        // Exchange code for access token
        const accessToken = await this.oauthService.exchangeCodeForToken(provider, request.code);
        // Get user info from OAuth provider
        let oauthUserData;
        switch (provider) {
            case 'google':
                oauthUserData = await this.oauthService.verifyGoogleToken(accessToken);
                break;
            case 'github':
                oauthUserData = await this.oauthService.verifyGitHubToken(accessToken);
                break;
            case 'microsoft':
                oauthUserData = await this.oauthService.verifyMicrosoftToken(accessToken);
                break;
            default:
                throw new rest_1.HttpErrors.BadRequest('Unsupported OAuth provider');
        }
        // Find or create user
        const user = await this.oauthService.findOrCreateOAuthUser(provider, oauthUserData);
        const isNewUser = !user.createdAt || (new Date().getTime() - new Date(user.createdAt).getTime()) < 60000; // Created within last minute
        // Generate JWT token
        const userProfile = this.convertToUserProfile(user);
        const token = await this.jwtService.generateToken(userProfile);
        return {
            token,
            user: {
                id: user.id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                avatarUrl: user.avatarUrl,
                oauthProvider: user.oauthProvider,
                roles: user.roles,
            },
            isNewUser,
        };
    }
};
exports.AuthController = AuthController;
tslib_1.__decorate([
    (0, rest_1.post)('/auth/signup'),
    (0, rest_1.response)(200, {
        description: 'User registration',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                        userId: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email', 'firstName', 'lastName', 'password'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        firstName: { type: 'string', minLength: 2, maxLength: 50 },
                        lastName: { type: 'string', minLength: 2, maxLength: 50 },
                        phone: { type: 'string' },
                        password: { type: 'string', minLength: 8 },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "signUp", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/login'),
    (0, rest_1.response)(200, {
        description: 'Token',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        user: (0, rest_1.getModelSchemaRef)(models_1.User, { exclude: ['password'] }),
                        requiresTwoFactor: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['email', 'password'],
                    properties: {
                        email: { type: 'string', format: 'email' },
                        password: { type: 'string', minLength: 8 },
                        twoFactorToken: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/verify-email'),
    (0, rest_1.response)(200, {
        description: 'Email verification',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token'],
                    properties: {
                        token: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "verifyEmail", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/forgot-password'),
    (0, rest_1.response)(200, {
        description: 'Password reset request',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        email: { type: 'string', format: 'email' },
                        phone: { type: 'string' },
                    },
                    anyOf: [
                        { required: ['email'] },
                        { required: ['phone'] }
                    ],
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "forgotPassword", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/reset-password'),
    (0, rest_1.response)(200, {
        description: 'Password reset',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['token', 'password'],
                    properties: {
                        token: { type: 'string' },
                        password: { type: 'string', minLength: 8 },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/change-password'),
    (0, authentication_1.authenticate)('jwt'),
    (0, rest_1.response)(200, {
        description: 'Change password',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        message: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, (0, core_1.inject)(security_1.SecurityBindings.USER)),
    tslib_1.__param(1, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['currentPassword', 'newPassword'],
                    properties: {
                        currentPassword: { type: 'string' },
                        newPassword: { type: 'string' },
                        twoFactorToken: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [Object, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "changePassword", null);
tslib_1.__decorate([
    (0, rest_1.get)('/auth/oauth/{provider}/url'),
    (0, rest_1.response)(200, {
        description: 'Get OAuth authorization URL',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        url: { type: 'string' },
                        state: { type: 'string' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('provider')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "getOAuthUrl", null);
tslib_1.__decorate([
    (0, rest_1.post)('/auth/oauth/{provider}/callback'),
    (0, rest_1.response)(200, {
        description: 'Handle OAuth callback',
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    properties: {
                        token: { type: 'string' },
                        user: { type: 'object' },
                        isNewUser: { type: 'boolean' },
                    },
                },
            },
        },
    }),
    tslib_1.__param(0, rest_1.param.path.string('provider')),
    tslib_1.__param(1, (0, rest_1.requestBody)({
        content: {
            'application/json': {
                schema: {
                    type: 'object',
                    required: ['code'],
                    properties: {
                        code: { type: 'string' },
                        state: { type: 'string' },
                    },
                },
            },
        },
    })),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, Object]),
    tslib_1.__metadata("design:returntype", Promise)
], AuthController.prototype, "handleOAuthCallback", null);
exports.AuthController = AuthController = tslib_1.__decorate([
    tslib_1.__param(0, (0, core_1.inject)(authentication_jwt_1.TokenServiceBindings.TOKEN_SERVICE)),
    tslib_1.__param(1, (0, core_1.inject)(security_1.SecurityBindings.USER, { optional: true })),
    tslib_1.__param(2, (0, repository_1.repository)(repositories_1.UserRepository)),
    tslib_1.__param(3, (0, core_1.inject)('services.SecurityService', { optional: true })),
    tslib_1.__param(4, (0, core_1.inject)('services.EmailService', { optional: true })),
    tslib_1.__param(5, (0, core_1.inject)('services.SmsService', { optional: true })),
    tslib_1.__param(6, (0, core_1.inject)('services.OAuthService', { optional: true })),
    tslib_1.__metadata("design:paramtypes", [Object, Object, repositories_1.UserRepository,
        services_1.SecurityService,
        services_1.EmailService,
        services_1.SmsService,
        services_1.OAuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map