{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\n// Components\nimport { ProfileComponent } from './profile.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProfileComponent\n}];\nexport class ProfileModule {\n  static #_ = this.ɵfac = function ProfileModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProfileModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ProfileModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule.forChild(routes), ReactiveFormsModule, MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatProgressSpinnerModule, MatSnackBarModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileModule, {\n    declarations: [ProfileComponent],\n    imports: [CommonModule, i1.RouterModule, ReactiveFormsModule, MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatProgressSpinnerModule, MatSnackBarModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatProgressSpinnerModule", "MatSnackBarModule", "ProfileComponent", "routes", "path", "component", "ProfileModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\augment\\Fullstack\\Modular backend secure user system and payment\\frontend\\src\\app\\modules\\profile\\profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Routes } from '@angular/router';\nimport { ReactiveFormsModule } from '@angular/forms';\n\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\n\n// Components\nimport { ProfileComponent } from './profile.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: ProfileComponent\n  }\n];\n\n@NgModule({\n  declarations: [\n    ProfileComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(routes),\n    ReactiveFormsModule,\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatProgressSpinnerModule,\n    MatSnackBarModule\n  ]\n})\nexport class ProfileModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,gBAAgB;AAEpD;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAE/D;AACA,SAASC,gBAAgB,QAAQ,qBAAqB;;;AAEtD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAmBD,OAAM,MAAOI,aAAa;EAAA,QAAAC,CAAA,G;qCAAbD,aAAa;EAAA;EAAA,QAAAE,EAAA,G;UAAbF;EAAa;EAAA,QAAAG,EAAA,G;cAZtBjB,YAAY,EACZC,YAAY,CAACiB,QAAQ,CAACP,MAAM,CAAC,EAC7BT,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB;EAAA;;;2EAGRK,aAAa;IAAAK,YAAA,GAftBT,gBAAgB;IAAAU,OAAA,GAGhBpB,YAAY,EAAAqB,EAAA,CAAApB,YAAA,EAEZC,mBAAmB,EACnBC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,wBAAwB,EACxBC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}