import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { environment } from '../../environments/environment';
import { 
  OAuthProvider, 
  OAuthUrlResponse, 
  OAuthCallbackRequest, 
  LoginResponse 
} from '../models/user.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class OAuthService {
  private readonly oauthProviders: OAuthProvider[] = [
    {
      name: 'google',
      displayName: 'Google',
      icon: 'fab fa-google',
      color: '#db4437'
    },
    {
      name: 'github',
      displayName: 'GitHub',
      icon: 'fab fa-github',
      color: '#333'
    },
    {
      name: 'microsoft',
      displayName: 'Microsoft',
      icon: 'fab fa-microsoft',
      color: '#00a1f1'
    }
  ];

  constructor(
    private http: HttpClient,
    private router: Router,
    private authService: AuthService
  ) {}

  getAvailableProviders(): OAuthProvider[] {
    return this.oauthProviders;
  }

  getOAuthUrl(provider: 'google' | 'github' | 'microsoft'): Observable<OAuthUrlResponse> {
    return this.http.get<OAuthUrlResponse>(`${environment.apiUrl}/auth/oauth/${provider}/url`)
      .pipe(
        catchError(this.handleError)
      );
  }

  initiateOAuthLogin(provider: 'google' | 'github' | 'microsoft'): void {
    this.getOAuthUrl(provider).subscribe({
      next: (response) => {
        // Store the provider in session storage for callback handling
        sessionStorage.setItem('oauth_provider', provider);
        sessionStorage.setItem('oauth_redirect', this.router.url);
        
        // Redirect to OAuth provider
        window.location.href = response.url;
      },
      error: (error) => {
        console.error(`Failed to get ${provider} OAuth URL:`, error);
        // Handle error - show notification to user
      }
    });
  }

  handleOAuthCallback(code: string, state?: string): Observable<LoginResponse> {
    const provider = sessionStorage.getItem('oauth_provider') as 'google' | 'github' | 'microsoft';
    const redirectUrl = sessionStorage.getItem('oauth_redirect') || '/dashboard';
    
    if (!provider) {
      return throwError(() => new Error('OAuth provider not found in session'));
    }

    const callbackData: OAuthCallbackRequest = { code };
    if (state) {
      callbackData.state = state;
    }

    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/oauth/${provider}/callback`, callbackData)
      .pipe(
        tap(response => {
          if (response.token) {
            // Clear OAuth session data
            sessionStorage.removeItem('oauth_provider');
            sessionStorage.removeItem('oauth_redirect');
            
            // Set authentication token
            this.authService.setToken(response.token);
            
            // Navigate to intended destination
            this.router.navigate([redirectUrl]);
          }
        }),
        catchError(this.handleError)
      );
  }

  isOAuthUser(user: any): boolean {
    return !!(user?.oauthProvider && (user?.googleId || user?.githubId || user?.microsoftId));
  }

  getOAuthProviderName(user: any): string {
    if (user?.googleId) return 'Google';
    if (user?.githubId) return 'GitHub';
    if (user?.microsoftId) return 'Microsoft';
    return 'Unknown';
  }

  getOAuthProviderIcon(user: any): string {
    if (user?.googleId) return 'fab fa-google';
    if (user?.githubId) return 'fab fa-github';
    if (user?.microsoftId) return 'fab fa-microsoft';
    return 'fas fa-user';
  }

  getOAuthProviderColor(user: any): string {
    if (user?.googleId) return '#db4437';
    if (user?.githubId) return '#333';
    if (user?.microsoftId) return '#00a1f1';
    return '#6c757d';
  }

  // Link OAuth account to existing user (if needed in future)
  linkOAuthAccount(provider: 'google' | 'github' | 'microsoft'): void {
    this.getOAuthUrl(provider).subscribe({
      next: (response) => {
        sessionStorage.setItem('oauth_action', 'link');
        sessionStorage.setItem('oauth_provider', provider);
        window.location.href = response.url;
      },
      error: (error) => {
        console.error(`Failed to link ${provider} account:`, error);
      }
    });
  }

  // Unlink OAuth account (if needed in future)
  unlinkOAuthAccount(provider: 'google' | 'github' | 'microsoft'): Observable<any> {
    const headers = this.authService.getAuthHeaders();
    return this.http.delete(`${environment.apiUrl}/auth/oauth/${provider}/unlink`, { headers })
      .pipe(
        catchError(this.handleError)
      );
  }

  private handleError(error: any): Observable<never> {
    let errorMessage = 'OAuth authentication error occurred';
    
    if (error.error instanceof ErrorEvent) {
      errorMessage = error.error.message;
    } else {
      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;
    }
    
    console.error('OAuth Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}
