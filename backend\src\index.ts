// Load environment variables FIRST, before any other imports
import * as dotenv from 'dotenv';
import * as path from 'path';

console.log('🔧 Loading environment variables...');
const envPath = path.resolve(__dirname, '../.env');
console.log('📁 Looking for .env file at:', envPath);
const result = dotenv.config({ path: envPath });
if (result.error) {
  console.error('❌ Error loading .env file:', result.error);
} else {
  console.log('✅ Environment variables loaded successfully');
  console.log('🔍 USE_POSTGRESQL:', process.env.USE_POSTGRESQL);
}

// Now import other modules after environment is loaded
import {SecureBackendApplication} from './application';
import {ApplicationConfig} from '@loopback/core';

export * from './application';

export async function main(options: ApplicationConfig = {}) {
  const app = new SecureBackendApplication(options);
  await app.boot();
  await app.start();

  const url = app.restServer.url;
  console.log(`Server is running at ${url}`);
  console.log(`Try ${url}/ping`);

  return app;
}

if (require.main === module) {
  // Run the application
  const config = {
    rest: {
      port: +(process.env.PORT ?? 3000),
      host: process.env.HOST,
      // The `gracePeriodForClose` provides a graceful close for http/https
      // servers with keep-alive clients. The default value is `Infinity`
      // (don't force-close). If you want to immediately destroy all sockets
      // upon stop, set its value to `0`.
      // See https://www.npmjs.com/package/stoppable
      gracePeriodForClose: 5000, // 5 seconds
      openApiSpec: {
        // useful when used with OpenAPI-to-GraphQL to locate your application
        setServersFromRequest: true,
      },
    },
  };
  main(config).catch(err => {
    console.error('Cannot start the application.', err);
    process.exit(1);
  });
}
