{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Angular Material\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatDividerModule } from '@angular/material/divider';\n// Components\nimport { LoginComponent } from '../../components/auth/login/login.component';\nimport { RegisterComponent } from '../../components/auth/register/register.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}];\nexport let AuthModule = /*#__PURE__*/(() => {\n  class AuthModule {\n    static #_ = this.ɵfac = function AuthModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(routes),\n      // Angular Material\n      MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatCheckboxModule, MatProgressSpinnerModule, MatSnackBarModule, MatDividerModule]\n    });\n  }\n  return AuthModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}