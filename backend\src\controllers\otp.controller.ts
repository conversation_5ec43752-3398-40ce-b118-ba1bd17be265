import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  post,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import {UserRepository} from '../repositories';
import {SecurityService, EmailService, SmsService} from '../services';

export class OtpController {
  constructor(
    @repository(UserRepository) protected userRepository: UserRepository,
    @inject('services.SecurityService') public securityService: SecurityService,
    @inject('services.EmailService') public emailService: EmailService,
    @inject('services.SmsService') public smsService: SmsService,
  ) {}

  @post('/otp/send')
  @response(200, {
    description: 'Send OTP via email or SMS',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async sendOTP(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['type'],
            properties: {
              email: {type: 'string', format: 'email'},
              phone: {type: 'string'},
              type: {type: 'string', enum: ['login', 'verification']},
            },
          },
        },
      },
    })
    request: {email?: string; phone?: string; type: string},
  ): Promise<{message: string}> {
    if (!request.email && !request.phone) {
      throw new HttpErrors.BadRequest('Either email or phone is required');
    }

    if (request.email) {
      // Check if user exists for login type
      if (request.type === 'login') {
        const user = await this.userRepository.findOne({
          where: {email: request.email},
        });

        if (!user) {
          throw new HttpErrors.NotFound('User not found');
        }

        if (!user.emailVerified) {
          // In development, allow OTP for unverified emails with warning
          if (process.env.NODE_ENV === 'development') {
            console.log('⚠️  Warning: Sending OTP to unverified email in development mode');
          } else {
            throw new HttpErrors.BadRequest('Email not verified. Please verify your email first.');
          }
        }
      }

      const otp = await this.securityService.generateOTP(request.email, request.type);

      try {
        await this.emailService.sendOTPEmail(request.email, otp, request.type);
        console.log(`✅ OTP sent to email: ${request.email}`);
      } catch (emailError) {
        console.error('❌ Email service error:', emailError);
        // In development, continue even if email fails
        if (process.env.NODE_ENV === 'development') {
          console.log(`📧 OTP for ${request.email}: ${otp} (Email service failed, showing in console)`);
        } else {
          throw new HttpErrors.ServiceUnavailable('Failed to send OTP email');
        }
      }

      return {message: 'OTP sent to email successfully'};
    }

    if (request.phone) {
      // Check if user exists for login type
      if (request.type === 'login') {
        const user = await this.userRepository.findOne({
          where: {phone: request.phone},
        });

        if (!user) {
          throw new HttpErrors.NotFound('User not found');
        }

        // For development: Send OTP to email instead of phone
        if (process.env.NODE_ENV === 'development') {
          console.log('📱 Development mode: Sending OTP to email instead of phone');
          const otp = await this.securityService.generateOTP(user.email, request.type);
          await this.emailService.sendOTPEmail(user.email, otp, request.type);
          return {message: 'OTP sent to email (associated with phone) successfully'};
        }

        if (!user.phoneVerified) {
          throw new HttpErrors.BadRequest('Phone not verified');
        }
      }

      const otp = await this.securityService.generateOTP(request.phone, request.type);
      await this.smsService.sendOTP(request.phone, otp, request.type);
      return {message: 'OTP sent to phone successfully'};
    }

    throw new HttpErrors.BadRequest('Invalid request');
  }

  @post('/otp/send-email')
  @response(200, {
    description: 'Send OTP via email',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async sendEmailOTP(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['email', 'type'],
            properties: {
              email: {type: 'string', format: 'email'},
              type: {type: 'string', enum: ['login', 'verification']},
            },
          },
        },
      },
    })
    request: {email: string; type: string},
  ): Promise<{message: string}> {
    // Check if user exists for login type
    if (request.type === 'login') {
      const user = await this.userRepository.findOne({
        where: {email: request.email},
      });

      if (!user) {
        throw new HttpErrors.NotFound('User not found');
      }

      if (!user.emailVerified) {
        // In development, allow OTP for unverified emails with warning
        if (process.env.NODE_ENV === 'development') {
          console.log('⚠️  Warning: Sending OTP to unverified email in development mode');
        } else {
          throw new HttpErrors.BadRequest('Email not verified. Please verify your email first.');
        }
      }
    }

    const otp = await this.securityService.generateOTP(request.email, request.type);

    try {
      await this.emailService.sendOTPEmail(request.email, otp, request.type);
      console.log(`✅ OTP sent to email: ${request.email}`);
    } catch (emailError) {
      console.error('❌ Email service error:', emailError);
      // In development, continue even if email fails
      if (process.env.NODE_ENV === 'development') {
        console.log(`📧 OTP for ${request.email}: ${otp} (Email service failed, showing in console)`);
      } else {
        throw new HttpErrors.ServiceUnavailable('Failed to send OTP email');
      }
    }

    return {message: 'OTP sent successfully'};
  }

  @post('/otp/send-sms')
  @response(200, {
    description: 'Send OTP via SMS',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async sendSMSOTP(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['phone', 'type'],
            properties: {
              phone: {type: 'string'},
              type: {type: 'string', enum: ['login', 'verification']},
            },
          },
        },
      },
    })
    request: {phone: string; type: string},
  ): Promise<{message: string}> {
    // Check if user exists for login type
    if (request.type === 'login') {
      const user = await this.userRepository.findOne({
        where: {phone: request.phone},
      });

      if (!user) {
        throw new HttpErrors.NotFound('User not found');
      }

      if (!user.phoneVerified) {
        throw new HttpErrors.BadRequest('Phone not verified');
      }
    }

    const otp = await this.securityService.generateOTP(request.phone, request.type);
    await this.smsService.sendOTP(request.phone, otp, request.type);

    return {message: 'OTP sent successfully'};
  }

  @post('/otp/verify')
  @response(200, {
    description: 'Verify OTP',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            valid: {type: 'boolean'},
            message: {type: 'string'},
          },
        },
      },
    },
  })
  async verifyOTP(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['identifier', 'code', 'type'],
            properties: {
              identifier: {type: 'string'}, // email or phone
              code: {type: 'string'},
              type: {type: 'string', enum: ['login', 'verification']},
            },
          },
        },
      },
    })
    request: {identifier: string; code: string; type: string},
  ): Promise<{valid: boolean; message: string}> {
    const isValid = await this.securityService.verifyOTP(
      request.identifier,
      request.code,
      request.type,
    );

    if (isValid && request.type === 'verification') {
      // Mark email or phone as verified
      const isEmail = request.identifier.includes('@');
      
      if (isEmail) {
        const user = await this.userRepository.findOne({
          where: {email: request.identifier},
        });
        
        if (user) {
          await this.userRepository.updateById(user.id, {
            emailVerified: true,
            updatedAt: new Date(),
          });
        }
      } else {
        const user = await this.userRepository.findOne({
          where: {phone: request.identifier},
        });
        
        if (user) {
          await this.userRepository.updateById(user.id, {
            phoneVerified: true,
            updatedAt: new Date(),
          });
        }
      }
    }

    return {
      valid: isValid,
      message: isValid ? 'OTP verified successfully' : 'Invalid or expired OTP',
    };
  }

  @post('/otp/login')
  @response(200, {
    description: 'Login with OTP',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            token: {type: 'string'},
            user: {type: 'object'},
          },
        },
      },
    },
  })
  async loginWithOTP(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['identifier', 'code'],
            properties: {
              identifier: {type: 'string'}, // email or phone
              code: {type: 'string'},
            },
          },
        },
      },
    })
    request: {identifier: string; code: string},
  ): Promise<{token: string; user: any}> {
    // Verify OTP
    const isValid = await this.securityService.verifyOTP(
      request.identifier,
      request.code,
      'login',
    );

    if (!isValid) {
      throw new HttpErrors.Unauthorized('Invalid or expired OTP');
    }

    // Find user
    const isEmail = request.identifier.includes('@');
    const whereClause = isEmail 
      ? {email: request.identifier}
      : {phone: request.identifier};

    const user = await this.userRepository.findOne({
      where: whereClause,
    });

    if (!user) {
      throw new HttpErrors.NotFound('User not found');
    }

    if (!user.isActive) {
      throw new HttpErrors.Unauthorized('Account is deactivated');
    }

    // Update last login
    await this.userRepository.updateById(user.id, {
      lastLoginAt: new Date(),
      updatedAt: new Date(),
    });

    // Generate proper JWT token
    const userProfile = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      roles: user.roles,
    };

    // Use the same JWT service as regular login
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        roles: user.roles,
      },
      process.env.JWT_SECRET,
      {
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
        algorithm: 'HS256',
      }
    );

    return {
      token,
      user: userProfile,
    };
  }
}
