import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { environment } from '../../environments/environment';
import {
  User,
  UserRegistration,
  UserLogin,
  LoginResponse,
  PasswordReset,
  ApiResponse,
  OTPRequest,
  OTPVerification
} from '../models/index';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject: BehaviorSubject<User | null>;
  public currentUser: Observable<User | null>;
  private tokenKey = environment.security.jwtTokenKey;

  constructor(
    private http: HttpClient,
    private router: Router
  ) {
    this.currentUserSubject = new BehaviorSubject<User | null>(this.getUserFromStorage());
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  public get isAuthenticated(): boolean {
    return !!this.getToken() && !!this.currentUserValue;
  }

  public get isEmailVerified(): boolean {
    return this.currentUserValue?.emailVerified || false;
  }

  public get isTwoFactorEnabled(): boolean {
    return this.currentUserValue?.twoFactorEnabled || false;
  }

  register(userData: UserRegistration): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/signup`, userData)
      .pipe(
        catchError(this.handleError)
      );
  }

  login(credentials: UserLogin): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/login`, credentials)
      .pipe(
        tap(response => {
          if (response.token && !response.requiresTwoFactor) {
            this.setToken(response.token);
            this.currentUserSubject.next(response.user);
          }
        }),
        catchError(this.handleError)
      );
  }

  logout(): void {
    this.removeToken();
    this.currentUserSubject.next(null);
    this.router.navigate(['/auth/login']);
  }

  verifyEmail(token: string): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/verify-email`, { token })
      .pipe(
        catchError(this.handleError)
      );
  }

  forgotPassword(email: string): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/forgot-password`, { email })
      .pipe(
        catchError(this.handleError)
      );
  }

  resetPassword(resetData: PasswordReset): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/reset-password`, resetData)
      .pipe(
        catchError(this.handleError)
      );
  }

  sendOTP(request: OTPRequest): Observable<ApiResponse> {
    const endpoint = request.identifier.includes('@') ? 'send-email' : 'send-sms';
    return this.http.post<ApiResponse>(`${environment.apiUrl}/otp/${endpoint}`, request)
      .pipe(
        catchError(this.handleError)
      );
  }

  verifyOTP(verification: OTPVerification): Observable<ApiResponse> {
    return this.http.post<ApiResponse>(`${environment.apiUrl}/otp/verify`, verification)
      .pipe(
        catchError(this.handleError)
      );
  }

  loginWithOTP(identifier: string, code: string): Observable<LoginResponse> {
    return this.http.post<LoginResponse>(`${environment.apiUrl}/otp/login`, { identifier, code })
      .pipe(
        tap(response => {
          if (response.token) {
            this.setToken(response.token);
            this.currentUserSubject.next(response.user);
          }
        }),
        catchError(this.handleError)
      );
  }

  // New API: Change Password
  changePassword(currentPassword: string, newPassword: string, twoFactorToken?: string): Observable<ApiResponse> {
    const headers = this.getAuthHeaders();
    const body: any = { currentPassword, newPassword };
    if (twoFactorToken) {
      body.twoFactorToken = twoFactorToken;
    }

    return this.http.post<ApiResponse>(`${environment.apiUrl}/auth/change-password`, body, { headers })
      .pipe(
        catchError(this.handleError)
      );
  }

  // New API: OAuth URLs
  getOAuthUrl(provider: 'google' | 'github' | 'microsoft'): Observable<{url: string}> {
    return this.http.get<{url: string}>(`${environment.apiUrl}/auth/oauth/${provider}/url`)
      .pipe(
        catchError(this.handleError)
      );
  }

  // New API: OAuth Callback
  oauthCallback(provider: 'google' | 'github' | 'microsoft', code: string, state?: string): Observable<LoginResponse> {
    const body: any = { code };
    if (state) {
      body.state = state;
    }

    return this.http.post<LoginResponse>(`${environment.apiUrl}/auth/oauth/${provider}/callback`, body)
      .pipe(
        tap(response => {
          if (response.token) {
            this.setToken(response.token);
            this.currentUserSubject.next(response.user);
          }
        }),
        catchError(this.handleError)
      );
  }

  // New API: Send OTP (unified endpoint)
  sendOTPUnified(email?: string, phone?: string, type: 'login' | 'verification' = 'verification'): Observable<ApiResponse> {
    const body: any = { type };
    if (email) body.email = email;
    if (phone) body.phone = phone;

    return this.http.post<ApiResponse>(`${environment.apiUrl}/otp/send`, body)
      .pipe(
        catchError(this.handleError)
      );
  }

  refreshUserData(): Observable<User> {
    return this.http.get<User>(`${environment.apiUrl}/auth/me`)
      .pipe(
        tap(user => {
          this.currentUserSubject.next(user);
        }),
        catchError(this.handleError)
      );
  }

  getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(this.tokenKey);
    }
    return null;
  }

  setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.tokenKey, token);
    }
  }

  removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.tokenKey);
    }
  }

  getAuthHeaders(): HttpHeaders {
    const token = this.getToken();
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    });
  }

  private getUserFromStorage(): User | null {
    if (typeof window !== 'undefined') {
      const token = this.getToken();
      if (token) {
        try {
          // Decode JWT token to get user info (basic implementation)
          const payload = JSON.parse(atob(token.split('.')[1]));
          return payload.user || null;
        } catch (error) {
          console.error('Error parsing token:', error);
          this.removeToken();
        }
      }
    }
    return null;
  }

  private handleError(error: any): Observable<never> {
    let errorMessage = 'An error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = error.error.message;
    } else {
      // Server-side error
      errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;
    }
    
    console.error('Auth Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }

  // Security utilities
  isTokenExpired(): boolean {
    const token = this.getToken();
    if (!token) return true;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expiry = payload.exp * 1000; // Convert to milliseconds
      return Date.now() > expiry;
    } catch (error) {
      return true;
    }
  }

  autoLogout(): void {
    const token = this.getToken();
    if (token && this.isTokenExpired()) {
      this.logout();
    }
  }
}
